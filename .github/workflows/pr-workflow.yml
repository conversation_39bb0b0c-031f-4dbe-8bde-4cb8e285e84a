name: release/main PR Workflow

on:
  push:
    branches: [release, main]
  pull_request:
    branches: [release, main]

jobs:
  build:

    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres
        env:
          POSTGRES_DB: finance_erp_test
          POSTGRES_USER: finance_erp_user
          POSTGRES_PASSWORD: finance_erp_password
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
    - uses: actions/checkout@v2
    - uses: webfactory/ssh-agent@v0.5.4
      with:
        ssh-private-key: |
          ${{ secrets.SSH_TREEBO_COMMONS_PRIVATE_KEY }}
          ${{ secrets.SSH_FLASKHEALTHCHECK_PRIVATE_KEY }}
          ${{ secrets.SSH_THSC_PRIVATE_KEY }}
    - name: Set up Python:3.9
      uses: actions/setup-python@v2
      with:
        python-version: '3.9'

    - name: Install dependencies
      run: |
        cat << EOF > requirements/deploy.txt
        git+ssh://**************/treebo-noss/treebo-common.git@v3.2.12#egg=treebo-commons
        git+ssh://**************/treebo-noss/prometheus.git@main#egg=thsc
        -r base.txt
        EOF
        pip install -r requirements/dev.txt
    - name: Itests with Pytest
      run: |
        pytest -x finance_erp/tests

    - name: Black formatter check
      run: |
        black finance_erp --check

    - name: Isort check
      run: |
        isort finance_erp --profile=black --check
