import inspect
import logging
from collections import defaultdict

from flask import make_response, jsonify

logger = logging.getLogger(__name__)


def fully_qualified_name(obj):
    if not inspect.isclass(obj):
        _obj = obj.__class__
    else:
        _obj = obj
    return "{m}.{kls}".format(m=_obj.__module__, kls=_obj.__name__)


class APIExceptionHandler:
    registry = defaultdict()

    @classmethod
    def register(cls, exc):

        def wrapper(func):
            try:
                _exc = iter(exc)
            except TypeError:
                _exc = [exc]

            for ex in _exc:
                cls.registry[fully_qualified_name(ex)] = func
            return func

        return wrapper

    @classmethod
    def handle(cls, exc, context):

        handler_name = fully_qualified_name(exc)
        if handler_name in cls.registry:
            handler = cls.registry[handler_name]
        else:
            handler = cls.generic_handler

        return handler(exc, context)

    @classmethod
    def generic_handler(cls, exc, context):
        logger.exception("{c} failed due to {e}".format(c=context, e=exc))
        response = cls.response_for_exception(exc, status_code=500)
        return response

    @classmethod
    def extract_message_and_errors(cls, exc):
        errors = exc.errors if hasattr(exc, 'errors') else []
        message = getattr(exc, 'message', '')

        if not message:
            message = str(exc)

        if not errors:
            errors = [message]
        return message, errors

    @classmethod
    def response_for_exception(cls, exc, status_code):
        message, errors = cls.extract_message_and_errors(exc)
        return cls.standard_error_response(message, errors, status_code)

    @classmethod
    def standard_error_response(cls, message, errors, status_code):
        return cls.make_response(data={'errors': errors, 'message': message}, status_code=status_code)

    @classmethod
    def make_response(cls, data, status_code, headers=None):
        return make_response(jsonify(data), status_code, headers)
