from ths_common.constants.base_enum import BaseEnum


class PurchaseInvoiceReportStatus:
    NOT_APPLICABLE = "not_applicable"
    PENDING = "pending"
    GENERATED = "generated"
    FAILED = "failed"
    MANUAL_ACTION_NEEDED = "manual_action_needed"


class InvoiceEntryType(BaseEnum):
    ORDER = "order"
    CREDIT = "credit memo"


class PaymentTypes(BaseEnum):
    """
    Payment types
    """

    PAYMENT = "payment", "Payment"
    REFUND = "refund", "Refund"
    CREDIT_OFFERED = "credit_offered", "Credit Offered"

    @staticmethod
    def key():
        return "payment_type"

    @staticmethod
    def option_label():
        return "Payment Type"

    @classmethod
    def get_enums(cls, user_type):
        return [enum for enum in cls]


class PaymentGateways(BaseEnum):
    RAZORPAY = "razorpay"
    PHONEPE = "phonepe"
    PHONEPE_APP = "phonepe_app"
    AMAZONPAY = "amazonpay"


class UserRole:
    ADMIN = "admin"


class PrivilegeCodes:
    CAN_PUSH_DATA_TO_BIZC = "CAN_PUSH_DATA_TO_BIZC"
    CAN_PULL_DATA_FROM_SOURCES = "CAN_PULL_DATA_FROM_SOURCES"
    CAN_DOWNLOAD_INVOICES_AND_CREDIT_NOTES = "CAN_DOWNLOAD_INVOICES_AND_CREDIT_NOTES"
    CAN_MANAGE_ACCESS_CONTROLS = "CAN_MANAGE_ACCESS_CONTROLS"
