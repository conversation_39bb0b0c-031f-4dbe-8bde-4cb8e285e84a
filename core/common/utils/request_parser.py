import functools
import logging
from enum import Enum
from typing import Type

from flask import request
from pydantic import BaseModel, ValidationError
from ths_common.exceptions import ApiValidationException
from werkzeug.exceptions import UnsupportedMediaType

logger = logging.getLogger(__name__)


class RequestTypes(Enum):
    JSON = "json"
    ARGS = "args"
    FORM = "form"


def prepare_error_list(pydantic_errors):
    errors = []
    for error in pydantic_errors:
        loc = ".".join(str(part) for part in error.get("loc", []))
        msg = error.get("msg", "Unknown error")
        errors.append(
            {"field": loc, "error": f"[{loc.replace('_', ' ').title()}] -> {msg}"}
        )
    return errors


def validate_and_parse_data(data, schema, many):
    """
    Validate and parse data
    :param data:
    :param schema:
    :param many:
    :return:
    """
    try:
        if many:
            return [schema(**item).model_dump() for item in data]
        return schema(**data).model_dump()
    except ValidationError as e:
        logger.info("APIValidationError: %s", e.errors())
        error_messages = prepare_error_list(e.errors())
        raise ApiValidationException(error_messages=error_messages)


def get_request_data(request_attr):
    """Extract and format request data based on request attribute type"""
    if request_attr == RequestTypes.JSON:
        if "application/json" not in request.content_type:
            raise UnsupportedMediaType()
        if request.content_length == 0:
            return dict()
        data = getattr(request, request_attr.value)
    else:
        data = getattr(request, request_attr.value)

    if request_attr in [RequestTypes.ARGS, RequestTypes.FORM]:
        data = data.to_dict()

    return data


def parse_parse_data(schema: Type[BaseModel], request_attr: RequestTypes, many: bool):
    """
    @request_attr is 'args' for GET APIs
                     'form' for POST APIs
                     'json' for POST APIs content-type application/json
    """

    def parse_parse_data_inner(func):
        """
        validate_decorator
        :param func:
        :return:
        """

        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            """
            wrapper
            :param args:
            :param kwargs:
            :return:
            """
            data = get_request_data(request_attr)
            logger.info(
                "API request={0}, method={1}, data={2}".format(
                    request.base_url, request.method, data
                )
            )
            parsed_data = validate_and_parse_data(data, schema, many)
            kwargs["parsed_request"] = parsed_data
            return func(*args, **kwargs)

        return wrapper

    return parse_parse_data_inner


def schema_wrapper_parser(
    schema: Type[BaseModel],
    many: bool = False,
    param_type: RequestTypes = RequestTypes.JSON,
):
    """

    Args:
        schema:
        many:
        param_type: json, form, args

    Returns:

    """
    return parse_parse_data(schema, param_type, many=many)
