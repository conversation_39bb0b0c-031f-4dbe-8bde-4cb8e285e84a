-- revision: '20230203010910_initial_migration'
-- down_revision: ''

-- upgrade
--
-- Name: Hotel , Corporate, Payment table -
--

CREATE TABLE hotel (
    hotel_code VARCHAR(255) NOT NULL PRIMARY KEY,
    vendor_name VARCHAR(255),
    search_name VA<PERSON><PERSON><PERSON>(255),
    address VARCHAR(255),
    city VARCHAR(255),
    country_code VARCHAR(255),
    pan VARCHAR(255),
    state_code VARCHAR(255),
    gstin VARCHAR(255),
    gst_vendor_type VARCHAR(255),
    msme VARCHAR(255),
    verified BOOLEAN,
    pushed BOOLEAN,
    deleted BOOLEAN,
    last_push_attempt_at TIMESTAMP WITH TIME ZONE,
    nav_push_remark VARCHAR(255),

    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL
);

CREATE TABLE corporate (
    corporate_code VARCHAR(50) NOT NULL PRIMARY KEY,
    customer_legal_name VARCHAR(255),
    customer_trading_name VA<PERSON><PERSON><PERSON>(255),
    address VARCHAR(255),
    city VARCHAR(50),
    phone_number <PERSON><PERSON><PERSON><PERSON>(20),
    post_code VARCHAR(20),
    email VARCHAR(255),
    credit_limit VARCHAR(50),
    country_code VARCHAR(10),
    pan VARCHAR(50),
    state_code VARCHAR(10),
    gstin VARCHAR(50),
    gst_customer_type VARCHAR(50),
    tan_number VARCHAR(50),

    verified BOOLEAN DEFAULT false,
    pushed BOOLEAN DEFAULT false,
    deleted BOOLEAN DEFAULT false,
    last_push_attempt_at TIMESTAMP WITH TIME ZONE,
    nav_push_remark VARCHAR(255),

    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL
);

CREATE TABLE pg_payments (
    pg_charges VARCHAR(255),
    total_amount VARCHAR(255),
    uu_id VARCHAR(255) NOT NULL PRIMARY KEY,
    pg_transaction_id VARCHAR(255) NOT NULL,
    reference_number VARCHAR(255),
    hotel_code VARCHAR(255),
    corporate_code VARCHAR(255),
    pg_name VARCHAR(255),
    pg_tax VARCHAR(255),
    posting_date DATE,

    verified BOOLEAN DEFAULT FALSE,
    pushed BOOLEAN DEFAULT FALSE,
    deleted BOOLEAN DEFAULT FALSE,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL,
    last_push_attempt_at TIMESTAMP WITH TIME ZONE,
    nav_push_remark TEXT
);

CREATE TABLE purchase_invoice (
    unique_ref_id VARCHAR(255) NOT NULL PRIMARY KEY,
    vendor_number VARCHAR(255),
    due_date DATE,
    order_date DATE,
    posting_date DATE,
    check_in DATE,
    check_out DATE,
    source_created_on DATE,
    uvid_date DATE,
    remark VARCHAR(255),
    hsn_code VARCHAR(255),
    original_invoice_number VARCHAR(255),
    structure VARCHAR(255),
    nature_of_supply VARCHAR(255),
    gst_vendor_type VARCHAR(255),
    entry_type VARCHAR(255),
    reference_number VARCHAR(255),
    state_code VARCHAR(255),
    unit_price VARCHAR(255),
    tax_percentage VARCHAR(255),
    hotel_name VARCHAR(255),
    stay_days VARCHAR(255),
    room_type VARCHAR(255),
    occupancy VARCHAR(255),
    guest_name VARCHAR(255),
    invoice_number VARCHAR(255),
    total_invoice_amount VARCHAR(255),
    hotel_code VARCHAR(255),
    source VARCHAR(255),

    verified BOOLEAN DEFAULT FALSE,
    pushed BOOLEAN DEFAULT FALSE,
    deleted BOOLEAN DEFAULT FALSE,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL,
    last_push_attempt_at TIMESTAMP WITH TIME ZONE,
    nav_push_remark TEXT
);

CREATE TABLE settlement_treebo_fee (
    amount VARCHAR(255),
    description VARCHAR(255),
    uu_id VARCHAR(255) NOT NULL PRIMARY KEY,
    gst_percent VARCHAR(255),
    entry_type VARCHAR(255),
    hotel_code VARCHAR(255),
    hsn_code VARCHAR(255),
    doc_type VARCHAR(255),
    remarks VARCHAR(255),
    settlement_date DATE,

    verified BOOLEAN DEFAULT FALSE,
    pushed BOOLEAN DEFAULT FALSE,
    deleted BOOLEAN DEFAULT FALSE,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL,
    last_push_attempt_at TIMESTAMP WITH TIME ZONE,
    nav_push_remark TEXT
);

CREATE TABLE settlement_expense (
    tds_per VARCHAR(255),
    invoice_number VARCHAR(255),
    uu_id VARCHAR(255) NOT NULL PRIMARY KEY,
    invoice_amount VARCHAR(255),
    entry_type VARCHAR(255),
    hotel_code VARCHAR(255),
    hsn_code VARCHAR(255),
    doc_type VARCHAR(255),
    remarks VARCHAR(255),
    posting_date DATE,
    invoice_date DATE,

    verified BOOLEAN DEFAULT FALSE,
    pushed BOOLEAN DEFAULT FALSE,
    deleted BOOLEAN DEFAULT FALSE,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL,
    last_push_attempt_at TIMESTAMP WITH TIME ZONE,
    nav_push_remark TEXT
);

CREATE TABLE settlement_loan (
    loan_amount VARCHAR(255),
    uu_id VARCHAR(255) NOT NULL PRIMARY KEY,
    entry_type VARCHAR(255),
    hotel_code VARCHAR(255),
    remarks VARCHAR(255),
    doc_type VARCHAR(255),
    posting_date DATE,

    verified BOOLEAN DEFAULT FALSE,
    pushed BOOLEAN DEFAULT FALSE,
    deleted BOOLEAN DEFAULT FALSE,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL,
    last_push_attempt_at TIMESTAMP WITH TIME ZONE,
    nav_push_remark TEXT
);

CREATE TABLE settlement_tax (
    cgst_amount VARCHAR(255),
    sgst_amount VARCHAR(255),
    total_amount VARCHAR(255),
    uu_id VARCHAR(255) NOT NULL PRIMARY KEY,
    entry_type VARCHAR(255),
    hotel_code VARCHAR(255),
    remarks VARCHAR(255),
    doc_type VARCHAR(255),
    posting_date DATE,

    verified BOOLEAN DEFAULT FALSE,
    pushed BOOLEAN DEFAULT FALSE,
    deleted BOOLEAN DEFAULT FALSE,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL,
    last_push_attempt_at TIMESTAMP WITH TIME ZONE,
    nav_push_remark TEXT
);

CREATE TABLE settlement_hotel_adjustment (
    amount VARCHAR(255),
    invoice_number VARCHAR(255),
    invoice_amount VARCHAR(255),
    uu_id VARCHAR(255) NOT NULL PRIMARY KEY,
    entry_type VARCHAR(255),
    adjustment_type VARCHAR(255),
    hotel_code VARCHAR(255),
    remarks VARCHAR(255),
    doc_type VARCHAR(255),
    posting_date DATE,
    invoice_date DATE NULL,

    verified BOOLEAN DEFAULT FALSE,
    pushed BOOLEAN DEFAULT FALSE,
    deleted BOOLEAN DEFAULT FALSE,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL,
    last_push_attempt_at TIMESTAMP WITH TIME ZONE,
    nav_push_remark TEXT
);

CREATE TABLE process_level_audits (
    id SERIAL PRIMARY KEY,
    audit_type VARCHAR(255),
    entity_type VARCHAR(255),
    event_type VARCHAR(255),
    event_id VARCHAR(255),
    event_data JSONB,
    description TEXT,
    detailed_trace TEXT,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL
);

CREATE TABLE entity_level_audits (
    id SERIAL PRIMARY KEY,
    entity_name VARCHAR(255) NOT NULL,
    uu_id VARCHAR(255) NOT NULL,
    event_type VARCHAR(255) NOT NULL,
    event_data TEXT,
    created_at timestamp with time zone DEFAULT now() NOT NULL
);

CREATE TABLE job (
    job_id VARCHAR(255) PRIMARY KEY,
    job_name VARCHAR(255) NOT NULL,
    data TEXT,
    eta timestamp with time zone DEFAULT now() NOT NULL,
    generated_at timestamp with time zone DEFAULT now() NOT NULL,
    status VARCHAR(255),
    picked_at TIMESTAMP WITH TIME ZONE,
    failure_message TEXT,
    total_tries INTEGER,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL
);

-- create the users table
CREATE TABLE users (
    email VARCHAR(255) PRIMARY KEY,
    authenticated BOOLEAN NOT NULL DEFAULT TRUE,
    deleted BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    modified_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- create the user_groups table
CREATE TABLE user_groups (
    email VARCHAR(255) PRIMARY KEY,
    role VARCHAR(255) NOT NULL,
    deleted BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    modified_at TIMESTAMP NOT NULL DEFAULT NOW()
);

CREATE TABLE ota (
    posting_date DATE,
    paid_at_ota VARCHAR,
    commission_amount VARCHAR,
    tds_percentage VARCHAR,
    reference_number VARCHAR PRIMARY KEY,
    hotel_code VARCHAR,
    check_in DATE,
    check_out DATE,
    pretax_room_rent VARCHAR,
    guest_name VARCHAR,
    ota_name VARCHAR,
    commission_percent VARCHAR,
    mop VARCHAR,
    booking_created_date DATE,
    verified BOOLEAN DEFAULT false,
    pushed BOOLEAN DEFAULT false,
    last_push_attempt_at TIMESTAMP,
    nav_push_remark VARCHAR,
    deleted BOOLEAN DEFAULT FALSE,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL
);

CREATE TABLE sale_invoice (
	remarks VARCHAR(255),
    hsn_code VARCHAR(255),
    customer_number VARCHAR(255),
    original_invoice_number VARCHAR(255),
	structure VARCHAR(255),
    nature_of_supply VARCHAR(255),
    entry_type VARCHAR(255),
    order_date DATE,
    posting_date DATE,
    check_in DATE,
    check_out DATE,
    source_created_on DATE,
    uvid_date DATE,
    reference_number VARCHAR(255),
    state_code VARCHAR(255),
    unit_price VARCHAR(255),
    tax_percentage VARCHAR(255),
    hotel_name VARCHAR(255),
    stay_days VARCHAR(255),
    room_type VARCHAR(255),
    occupancy VARCHAR(255),
    guest_name VARCHAR(255),
    invoice_number VARCHAR(255),
    total_invoice_amount VARCHAR(255),
    hotel_code VARCHAR(255),
    unique_ref_id VARCHAR(255) NOT NULL PRIMARY KEY,
    source VARCHAR(255),
    gst_customer_type VARCHAR(255),
    invoice_charge_type VARCHAR(255),
    billed_to_legal_name VARCHAR(255),
    billed_to_state_code VARCHAR(255),
    billed_to_gstin VARCHAR(255),

    verified BOOLEAN DEFAULT FALSE,
    pushed BOOLEAN DEFAULT FALSE,
    deleted BOOLEAN DEFAULT FALSE,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL,
    last_push_attempt_at TIMESTAMP WITH TIME ZONE,
    nav_push_remark TEXT
);

-- downgrade