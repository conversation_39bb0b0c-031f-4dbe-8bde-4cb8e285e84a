-- revision: '20230517131818_add_ta_commission_table'
-- down_revision: '20230505170910_add_booking_owner_legal_entity_id_sales'

-- upgrade
CREATE TABLE ta_commission(
    posting_date DATE,
    commission_amount numeric(15,4),
    uu_id VARCHAR(255) NOT NULL PRIMARY KEY,
    reference_number VARCHAR(255) NOT NULL,
    hotel_code VARCHAR(255),
    check_in DATE,
    check_out DAT<PERSON>,
    pretax_room_rent numeric(15,4),
    ta_name VA<PERSON>HA<PERSON>(255),
    guest_name VA<PERSON>HA<PERSON>(255),
    entry_type VARCHAR(255),
    ta_sh_profile_code VARCHAR(255),
    commission_percent VARCHAR(255),
    booking_created_date DATE,
    tds_percentage VARCHAR,
    mop VARCHAR,
    paid_at_ota numeric(15,4),

    verified BOOLEAN DEFAULT FALSE,
    pushed BOOLEAN DEFAULT FALSE,
    deleted BOOLEAN DEFAULT FALSE,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL,
    last_push_attempt_at TIMESTAMP WITH TIME ZONE,
    nav_push_remark TEXT
);
CREATE INDEX ix_ta_commission_reference_number ON ta_commission (reference_number);
CREATE INDEX ix_ta_commission_reference_number_pushed ON ta_commission (reference_number, pushed);

-- downgrade
DROP TABLE ta_commission;
