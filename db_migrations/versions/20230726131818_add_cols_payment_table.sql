-- revision: '20230726131818_add_cols_payment_table'
-- down_revision: '20230517131818_add_ta_commission_table'

-- upgrade
ALTER TABLE pg_payments ADD payment_date DATE;
ALTER TABLE pg_payments ADD paid_by varchar(255);
ALTER TABLE pg_payments ADD paid_to varchar(255);
ALTER TABLE pg_payments ADD payment_type varchar(255);
ALTER TABLE pg_payments ADD paymode_type varchar(255);
ALTER TABLE pg_payments ADD payor_name varchar(255);
ALTER TABLE pg_payments ADD hotel_name varchar(255);
ALTER TABLE pg_payments ADD invoice_id varchar(255);
ALTER TABLE pg_payments ADD check_in DATE;
ALTER TABLE pg_payments ADD check_out DATE;
ALTER TABLE pg_payments ADD channel varchar(255);
ALTER TABLE pg_payments ADD sub_channel varchar(255);
ALTER TABLE pg_payments ADD original_booking_amount varchar(255);
ALTER TABLE pg_payments ADD is_advance varchar(255);

ALTER TABLE pg_payments RENAME COLUMN pg_name TO paymode;
ALTER TABLE pg_payments RENAME COLUMN corporate_code TO payor_entity;
ALTER TABLE pg_payments RENAME COLUMN total_amount TO payment_amount;

-- downgrade
ALTER TABLE pg_payments DROP COLUMN payment_date;
ALTER TABLE pg_payments DROP COLUMN paid_by;
ALTER TABLE pg_payments DROP COLUMN paid_to;
ALTER TABLE pg_payments DROP COLUMN payment_type;
ALTER TABLE pg_payments DROP COLUMN paymode_type;
ALTER TABLE pg_payments DROP COLUMN payor_name;
ALTER TABLE pg_payments DROP COLUMN hotel_name;
ALTER TABLE pg_payments DROP COLUMN invoice_id;
ALTER TABLE pg_payments DROP COLUMN check_in;
ALTER TABLE pg_payments DROP COLUMN check_out;
ALTER TABLE pg_payments DROP COLUMN channel;
ALTER TABLE pg_payments DROP COLUMN sub_channel;
ALTER TABLE pg_payments DROP COLUMN original_booking_amount;
ALTER TABLE pg_payments DROP COLUMN is_advance;

ALTER TABLE pg_payments RENAME COLUMN paymode TO pg_name;
ALTER TABLE pg_payments RENAME COLUMN payor_entity TO corporate_code;
ALTER TABLE pg_payments RENAME COLUMN payment_amount TO total_amount;
