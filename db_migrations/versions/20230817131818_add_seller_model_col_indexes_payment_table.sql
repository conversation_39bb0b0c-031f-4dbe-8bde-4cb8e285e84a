-- revision: '20230817131818_add_seller_model_col_indexes_payment_table'
-- down_revision: '20230726131818_add_cols_payment_table'

-- upgrade
ALTER TABLE pg_payments ADD seller_model varchar(255);

CREATE INDEX ix_pg_payment_payment_date ON pg_payments (payment_date);
CREATE INDEX ix_pg_payment_posting_date ON pg_payments (posting_date);
CREATE INDEX ix_pg_payment_check_out ON pg_payments (check_out);
-- downgrade
ALTER TABLE pg_payments DROP COLUMN seller_model;

DROP INDEX ix_pg_payment_payment_date;
DROP INDEX ix_pg_payment_posting_date;
DROP INDEX ix_pg_payment_check_out;
