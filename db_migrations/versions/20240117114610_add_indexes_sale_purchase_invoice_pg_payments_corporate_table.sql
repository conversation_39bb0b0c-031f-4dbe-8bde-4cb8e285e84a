-- revision: '20240117114610_add_indexes_sale_purchase_invoice_pg_payments_corporate_table'
-- down_revision: '20231116131818_add_booker_entity_col_pg_payments'

-- upgrade
CREATE INDEX ix_sale_invoice_invoice_number ON sale_invoice (invoice_number);
CREATE INDEX ix_sale_invoice_posting_date ON sale_invoice (posting_date);
CREATE INDEX ix_sale_invoice_pushed ON sale_invoice (pushed);
CREATE INDEX ix_sale_invoice_buy_side_invoice_number ON sale_invoice (buy_side_invoice_number);
CREATE INDEX ix_sale_invoice_customer_number ON sale_invoice (customer_number);
CREATE INDEX ix_sale_invoice_hotel_code ON sale_invoice (hotel_code);
CREATE INDEX ix_sale_invoice_reference_number ON sale_invoice (reference_number);

CREATE INDEX ix_purchase_invoice_invoice_number ON purchase_invoice (invoice_number);
CREATE INDEX ix_purchase_invoice_posting_date ON purchase_invoice (posting_date);
CREATE INDEX ix_purchase_invoice_pushed ON purchase_invoice (pushed);
CREATE INDEX ix_purchase_invoice_customer_invoice_number ON purchase_invoice (customer_invoice_number);
CREATE INDEX ix_purchase_invoice_reference_number ON purchase_invoice (reference_number);
CREATE INDEX ix_purchase_invoice_hotel_code ON purchase_invoice (hotel_code);

CREATE INDEX ix_pg_payment_pushed ON pg_payments (pushed);
CREATE INDEX ix_pg_payment_reference_number ON pg_payments (reference_number);
CREATE INDEX ix_pg_payment_hotel_code ON pg_payments (hotel_code);
CREATE INDEX ix_pg_payment_uu_id ON pg_payments (uu_id);

CREATE INDEX ix_corporate_pushed ON corporate (pushed);
CREATE INDEX ix_corporate_corporate_code ON corporate (corporate_code);

-- downgrade
DROP INDEX ix_sale_invoice_invoice_number;
DROP INDEX ix_sale_invoice_posting_date;
DROP INDEX ix_sale_invoice_pushed;
DROP INDEX ix_sale_invoice_buy_side_invoice_number;
DROP INDEX ix_sale_invoice_customer_number;
DROP INDEX ix_sale_invoice_hotel_code;
DROP INDEX ix_sale_invoice_reference_number;

DROP INDEX ix_purchase_invoice_invoice_number;
DROP INDEX ix_purchase_invoice_posting_date;
DROP INDEX ix_purchase_invoice_pushed;
DROP INDEX ix_purchase_invoice_customer_invoice_number;
DROP INDEX ix_purchase_invoice_reference_number;
DROP INDEX ix_purchase_invoice_hotel_code;

DROP INDEX ix_pg_payment_pushed;
DROP INDEX ix_pg_payment_reference_number;
DROP INDEX ix_pg_payment_hotel_code;
DROP INDEX ix_pg_payment_uu_id;

DROP INDEX ix_corporate_pushed;
DROP INDEX ix_corporate_corporate_code;

