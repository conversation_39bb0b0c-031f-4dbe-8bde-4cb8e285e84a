-- revision: '20240305114610_add_pg_payments_summary_table_and_modify_pg_payments'
-- down_revision: '20240305114610_add_sales_summary_table_and_modify_sales_table'

-- upgrade
CREATE TABLE pg_payments_summary (
    pg_charges VARCHAR(255),
    payment_amount VARCHAR(255),
    uu_id VARCHAR(255) NOT NULL PRIMARY KEY,
    pg_transaction_id VARCHAR(255) NOT NULL,
    reference_number VARCHAR(255),
    hotel_code VARCHAR(255),
    payor_entity VARCHAR(255),
    paymode VARCHAR(255),
    pg_tax VARCHAR(255),
    posting_date DATE,
    payment_date DATE,
    paid_by VARCHAR(255),
    paid_to VARCHAR(255),
    payment_type VARCHAR(255),
    paymode_type VARCHAR(255),
    payor_name VARCHAR(255),
    hotel_name VARCHAR(255),
    invoice_id VARCHAR(255),
    check_in DATE,
    check_out DATE,
    channel VARCHAR(255),
    sub_channel VARCHAR(255),
    original_booking_amount VARCHAR(255),
    is_advance VARCHAR(255),
    seller_model VARCHAR(255),
    booker_entity VARCHAR(255),
    athena_code VARCHAR(255),

    verified BOOLEAN DEFAULT FALSE,
    pushed BOOLEAN DEFAULT FALSE,
    deleted BOOLEAN DEFAULT FALSE,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL,
    last_push_attempt_at TIMESTAMP WITH TIME ZONE,
    nav_push_remark TEXT
);

ALTER TABLE pg_payments ADD status varchar(255);
ALTER TABLE pg_payments ADD aggregated_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE pg_payments ADD aggregation_id varchar(255);


CREATE INDEX ix_pg_payments_status ON pg_payments (status);
CREATE INDEX ix_pg_payments_aggregation_filter ON pg_payments (verified, status, deleted);
CREATE INDEX ix_pg_payments_summary_pushed ON pg_payments_summary (pushed);


UPDATE pg_payments SET status='ingested' where pushed is false;
UPDATE pg_payments SET status='pushed' where pushed is true;

ALTER TABLE pg_payments DROP COLUMN pushed;

-- downgrade
DROP TABLE pg_payments;

ALTER TABLE pg_payments DROP COLUMN status;
ALTER TABLE pg_payments DROP COLUMN aggregated_at;
ALTER TABLE pg_payments DROP COLUMN aggregation_id;

DROP INDEX ix_pg_payments_status;
DROP INDEX ix_pg_payments_aggregation_filter;
DROP INDEX ix_pg_payments_summary_pushed;
ALTER TABLE pg_payments ADD pushed;
