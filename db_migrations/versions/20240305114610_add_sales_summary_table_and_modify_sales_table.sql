-- revision: '20240305114610_add_sales_summary_table_and_modify_sales_table'
-- down_revision: '20230821131818_add_refund_reason_col_pg_payment'

-- upgrade

CREATE TABLE sales_summary(
    hsn_code VARCHAR(255),
    entry_type VARCHAR(255),
    order_date DATE,
    posting_date DATE,
    uvid_date DATE,
    unit_price DECIMAL,
    tax_percentage INTEGER,
    cgst DECIMAL,
    sgst DECIMAL,
    igst DECIMAL,
    tax_type VARCHAR(255),
    unique_ref_id VARCHAR(255) NOT NULL PRIMARY KEY,

    pushed BOOLEAN DEFAULT FALSE,
    deleted BOOLEAN DEFAULT FALSE,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL,
    last_push_attempt_at TIMESTAMP WITH TIME ZONE,
    nav_push_remark TEXT
);
CREATE INDEX ix_sales_summary_pushed_deleted ON sales_summary (pushed, deleted);
CREATE INDEX ix_sales_summary_posting_date ON sales_summary (posting_date);
CREATE INDEX ix_sales_summary_pushed ON sales_summary (pushed);

ALTER TABLE sale_invoice ADD status varchar(255);
ALTER TABLE sale_invoice ADD aggregated_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE sale_invoice ADD aggregation_id varchar(255);
ALTER TABLE sale_invoice ADD cgst DECIMAL;
ALTER TABLE sale_invoice ADD sgst DECIMAL;
ALTER TABLE sale_invoice ADD igst DECIMAL;
ALTER TABLE sale_invoice ADD tax_type varchar(255);

CREATE INDEX ix_sale_invoice_status ON sale_invoice (status);
CREATE INDEX ix_sale_invoice_aggregation_filter ON sale_invoice (verified, status, deleted);

UPDATE sale_invoice SET status='ingested' where pushed is false;
UPDATE sale_invoice SET status='pushed' where pushed is true;
DROP INDEX ix_sale_invoice_pushed;
ALTER TABLE sale_invoice DROP COLUMN pushed;

-- downgrade

DROP TABLE sales_summary;

ALTER TABLE sale_invoice DROP COLUMN status;
ALTER TABLE sale_invoice DROP COLUMN aggregated_at;
ALTER TABLE sale_invoice DROP COLUMN aggregation_id;
ALTER TABLE sale_invoice DROP COLUMN cgst;
ALTER TABLE sale_invoice DROP COLUMN sgst;
ALTER TABLE sale_invoice DROP COLUMN igst;
ALTER TABLE sale_invoice DROP COLUMN tax_type;

DROP INDEX ix_sale_invoice_status;
DROP INDEX ix_sale_invoice_aggregation_filter;
ALTER TABLE sale_invoice ADD pushed;
CREATE INDEX ix_sale_invoice_pushed ON sale_invoice (pushed);
