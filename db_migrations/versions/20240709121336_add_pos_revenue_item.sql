-- revision: '20240709121336_add_pos_revenue_item'
-- down_revision: '20240721184511_add_booking_owner_in_pg_payment_and_summary'

-- upgrade

CREATE TABLE pos_revenue_item (
    uu_id VARCHAR(255) PRIMARY KEY,
    interface_id VARCHAR(255) NOT NULL,
    interface_name VARCHAR(255) NOT NULL,
    hotel_id VARCHAR(255) NOT NULL,
    bill_id VARCHAR(255) NOT NULL,
    reservation_id VARCHAR(255),
    guest_name VARCHAR(255) NOT NULL,
    amount DECIMAL NOT NULL,
    tax DECIMAL,
    tax_details JSONB,
    sku_category VARCHAR(255) NOT NULL,
    hsn_code VARCHAR(255) NOT NULL,
    payment_method VARCHAR(255) NOT NULL,
    pos_bill_date DATE NOT NULL,
    pos_bill_time TIME NOT NULL,
    revenue_center VARCHAR(255),
    serving_time VARCHAR(255),
    workstation_id VARCHAR(255),
    waiter_id VARCHAR(255),
    verified BOOLEAN DEFAULT TRUE,
    pushed B<PERSON><PERSON>EAN DEFAULT FALSE,
    last_push_attempt_at TIMESTAMP,
    ext_push_remark VARCHAR(255),
    deleted BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    modified_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

CREATE INDEX ix_pos_revenue_item_bill_id ON pos_revenue_item (bill_id);
CREATE INDEX ix_pos_revenue_item_hotel_id ON pos_revenue_item (hotel_id);
CREATE INDEX ix_pos_revenue_item_reservation_id ON pos_revenue_item (reservation_id);
CREATE INDEX ix_pos_revenue_item_pos_bill_date ON pos_revenue_item (pos_bill_date);

-- downgrade

DROP TABLE pos_revenue_item;
