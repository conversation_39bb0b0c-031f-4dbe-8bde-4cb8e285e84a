-- revision: '202408201184511_add_table_of_invoice_communications'
-- down_revision: '20240913091915_pos_revenue_table_guest_name_can_be_null'

-- upgrade
CREATE TABLE invoice (
    invoice_id VARCHAR PRIMARY KEY,
    invoice_number VARCHAR NOT NULL,
    status VARCHAR NOT NULL,
    booking_reference_number VARCHAR NOT NULL,
    booking_id VARCHAR NOT NULL,
    customer_external_id VARCHAR,
    issued_to JSONB,
    issued_by JSONB,
    booking_meta JSONB,
    vendor_details JSONB,
    issued_to_type VARCHAR,
    issued_by_type VARCHAR,
    pre_tax_amount DECIMAL NOT NULL,
    tax_amount DECIMAL NOT NULL,
    post_tax_amount DECIMAL NOT NULL,
    due_date DATE,
    invoice_date DATE NOT NULL,
    is_b2b BOOLEAN NOT NULL,
    invoice_type VARCHAR,
    is_spot_credit BOOLEAN,
    invoice_url VARCHAR,
    hotel_id VARCHAR NOT NULL,
    frozen_at timestamp with time zone,

    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL
);
CREATE INDEX ix_invoice_invoice_number ON invoice (invoice_number);
CREATE INDEX ix_invoice_invoice_date ON invoice (invoice_date);
CREATE INDEX ix_invoice_booking_reference_number ON invoice (booking_reference_number);
CREATE INDEX ix_invoice_invoice_id ON invoice (invoice_id);
CREATE INDEX ix_invoice_hotel_id ON invoice (hotel_id);
CREATE INDEX ix_invoice_is_b2b_invoice_type ON invoice (is_b2b, invoice_type);
CREATE INDEX ix_invoice_is_b2b_invoice_type_customer ON invoice (is_b2b, invoice_type, customer_external_id);

CREATE TABLE credit_note (
    credit_note_id VARCHAR PRIMARY KEY,
    credit_note_number VARCHAR NOT NULL,
    booking_reference_number VARCHAR NOT NULL,
    booking_id VARCHAR NOT NULL,
    customer_external_id VARCHAR,
    issued_to JSONB,
    issued_by JSONB,
    booking_meta JSONB,
    vendor_details JSONB,
    invoice_meta JSONB,
    issued_to_type VARCHAR,
    issued_by_type VARCHAR,
    pre_tax_amount DECIMAL NOT NULL,
    tax_amount DECIMAL NOT NULL,
    post_tax_amount DECIMAL NOT NULL,
    credit_note_date DATE NOT NULL,
    is_b2b BOOLEAN NOT NULL,
    credit_note_type VARCHAR,
    is_spot_credit BOOLEAN,
    invoice_ids VARCHAR[] NOT NULL,
    credit_note_url VARCHAR,
    hotel_id VARCHAR NOT NULL,

    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL
);
CREATE INDEX ix_credit_note_credit_note_number ON credit_note (credit_note_number);
CREATE INDEX ix_credit_note_credit_note_date ON credit_note (credit_note_date);
CREATE INDEX ix_credit_note_booking_reference_number ON credit_note (booking_reference_number);
CREATE INDEX ix_credit_note_credit_note_id ON credit_note (credit_note_id);
CREATE INDEX ix_credit_note_hotel_id ON credit_note (hotel_id);
CREATE INDEX ix_credit_note_is_b2b_invoice_type ON credit_note (is_b2b, credit_note_type);

CREATE TABLE booking (
    booking_id VARCHAR PRIMARY KEY,
    reference_number VARCHAR NOT NULL,
    checkin_date TIMESTAMP NOT NULL,
    checkout_date TIMESTAMP NOT NULL,
    actual_checkin_date TIMESTAMP,
    actual_checkout_date TIMESTAMP,
    channel_code VARCHAR NOT NULL,
    subchannel_code VARCHAR NOT NULL,
    application_code VARCHAR NOT NULL,
    status VARCHAR NOT NULL,
    booker_legal_entity_id VARCHAR,
    total_booking_amount DECIMAL NOT NULL,
    attachments JSONB,

    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL
);
CREATE INDEX ix_booking_booking_id ON booking (booking_id);
CREATE INDEX ix_booking_reference_number ON booking (reference_number);

CREATE TABLE stay_summary (
    stay_summary_id VARCHAR PRIMARY KEY,
    corporate_id VARCHAR NOT NULL,
    billing_from DATE NOT NULL,
    billing_to DATE NOT NULL,
    billing_date DATE NOT NULL,
    signed_url VARCHAR,
    booking_request_signed_url VARCHAR
);

CREATE TABLE stay_summary_invoice (
    invoice_id VARCHAR,
    stay_summary_id VARCHAR,
    PRIMARY KEY (invoice_id, stay_summary_id),
    FOREIGN KEY (invoice_id) REFERENCES invoice(invoice_id),
    FOREIGN KEY (stay_summary_id) REFERENCES stay_summary(stay_summary_id)
);

CREATE TABLE stay_summary_credit_note (
    credit_note_id VARCHAR,
    stay_summary_id VARCHAR,
    PRIMARY KEY (credit_note_id, stay_summary_id),
    FOREIGN KEY (credit_note_id) REFERENCES credit_note(credit_note_id),
    FOREIGN KEY (stay_summary_id) REFERENCES stay_summary(stay_summary_id)
);

CREATE TABLE stay_summary_sequence (
    corporate_code VARCHAR PRIMARY KEY,
    sequence INTEGER NOT NULL
);

CREATE TABLE communication_log (
    id SERIAL PRIMARY KEY,
    resource_type VARCHAR(50) NOT NULL,
    resource_id VARCHAR NOT NULL,
    communication_type VARCHAR(50) NOT NULL,
    status VARCHAR(20) NOT NULL,
    communication_details JSONB,
    notification_id VARCHAR(255),
    message TEXT,

    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE corporate ADD COLUMN billing_period INTEGER;
ALTER TABLE corporate ADD COLUMN credit_period INTEGER;
ALTER TABLE corporate ADD COLUMN is_billing_enabled BOOLEAN;
ALTER TABLE corporate ADD COLUMN next_billing_date DATE;
ALTER TABLE corporate ADD COLUMN communication_settings JSONB;
ALTER TABLE corporate ADD COLUMN corporate_pocs JSONB;
ALTER TABLE job ADD COLUMN is_resilient BOOLEAN;
CREATE INDEX ix_corporate_for_billing ON corporate (is_billing_enabled, next_billing_date);

-- downgrade
DROP TABLE invoice;
DROP TABLE credit_note;
DROP TABLE booking;
DROP TABLE stay_summary;
DROP TABLE stay_summary_invoice;
DROP TABLE stay_summary_credit_note;
DROP TABLE stay_summary_sequence;
DROP TABLE communication_log;

DROP INDEX ix_corporate_for_billing;
ALTER TABLE corporate DROP COLUMN billing_period;
ALTER TABLE corporate DROP COLUMN next_billing_date;
ALTER TABLE corporate DROP COLUMN communication_settings;
ALTER TABLE corporate DROP COLUMN corporate_pocs;
