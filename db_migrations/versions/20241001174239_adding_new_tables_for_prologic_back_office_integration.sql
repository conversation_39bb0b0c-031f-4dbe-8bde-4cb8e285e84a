-- revision: '20241001174239_adding_new_tables_for_prologic_back_office_integration'
-- down_revision: '20241014000828_ad_external_account_number_in_corporate_table'

-- upgrade
CREATE TABLE ledgers_file_record (
    id SERIAL PRIMARY KEY,
    hotel_id VARCHAR NOT NULL,
    erp_name VARCHAR NOT NULL,
    business_date DATE NOT NULL,
    ledger_file_type VARCHAR,
    ledger_file_path VARCHAR,
    ledger_file_name VARCHAR,
    remarks VARCHAR,
    status VARCHAR,

    deleted BOOLEAN,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL
);
CREATE INDEX ix_ledgers_file_record_hotel_id ON ledgers_file_record (hotel_id);
CREATE INDEX ix_ledgers_file_record_business_date ON ledgers_file_record (business_date);

CREATE TABLE transaction_master (
    transaction_id SERIAL PRIMARY KEY,
    hotel_id VARCHAR,
    erp_name VARCHA<PERSON>,
    gl_code VARCHAR,
    is_active BOOLEAN DEFAULT TRUE,
    merge_gl_entries BOOLEAN DEFAULT TRUE,
    particulars VARCHAR,
    identifier_name VARCHAR NOT NULL,
    display_name VARCHAR NOT NULL,
    revenue_center VARCHAR,
    identifier VARCHAR NOT NULL,  -- sku_id/payment_mode
    transaction_type VARCHAR NOT NULL,  -- charge/allowance/tax/payment
    transaction_metadata JSONB,

    deleted BOOLEAN DEFAULT FALSE,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL
);
CREATE INDEX ix_transaction_master_hotel_id ON transaction_master (hotel_id);
CREATE INDEX ix_transaction_master_erp_name ON transaction_master (erp_name);
CREATE INDEX ix_transaction_master_identifier ON transaction_master (identifier);
CREATE INDEX ix_transaction_master_transaction_type ON transaction_master (transaction_type);

CREATE TABLE gl_ledger_item (
    id SERIAL PRIMARY KEY,
    posting_date DATE NOT NULL,
    hotel_id VARCHAR NOT NULL,
    erp_name VARCHAR NOT NULL,
    particular VARCHAR,
    txn_amount FLOAT,
    txn_group INTEGER,
    gl_code VARCHAR,
    debit FLOAT DEFAULT 0.0,
    credit FLOAT DEFAULT 0.0,

    deleted BOOLEAN DEFAULT FALSE,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL
);
CREATE INDEX idx_gl_ledger_item_hotel_id ON gl_ledger_item (hotel_id);
CREATE INDEX idx_gl_ledger_item_posting_date ON gl_ledger_item (posting_date);
CREATE INDEX idx_gl_ledger_item_hotel_id_posting_date_erp_name ON gl_ledger_item (hotel_id, posting_date, erp_name);

CREATE TABLE cl_ledger_item (
    id SERIAL PRIMARY KEY,
    account_number VARCHAR NOT NULL,
    account_name VARCHAR,
    guest_name VARCHAR,
    posting_date DATE NOT NULL,
    hotel_id VARCHAR NOT NULL,
    erp_name VARCHAR NOT NULL,
    txt_date DATE,
    checkin_date DATE,
    checkout_date DATE,
    txn_amount FLOAT,
    folio_number INTEGER,
    room_number VARCHAR,
    booking_id VARCHAR,

    deleted BOOLEAN DEFAULT FALSE,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL
);

CREATE INDEX idx_cl_ledger_item_hotel_id ON cl_ledger_item (hotel_id);
CREATE INDEX idx_cl_ledger_item_posting_date ON cl_ledger_item (posting_date);
CREATE INDEX idx_cl_ledger_item_hotel_id_posting_date_erp_name ON cl_ledger_item (hotel_id, posting_date, erp_name);


CREATE TABLE back_office_payment_details (
    uu_id VARCHAR(255) PRIMARY KEY,
    bill_id VARCHAR(255),
    payment_id INTEGER,
    payment_split_id INTEGER,
    payment_type VARCHAR(255),
    amount FLOAT,
    posting_date DATE,
    date_of_payment DATE,
    payment_mode VARCHAR(255),
    payment_mode_sub_type VARCHAR(255),
    payment_ref_id VARCHAR(255),
    payment_channel VARCHAR(255),
    hotel_id VARCHAR(255),
    booking_id VARCHAR(255),
    category VARCHAR(255),
    owner_name VARCHAR(255),
    folio_number INTEGER,
    checkin_date DATE,
    checkout_date DATE,
    debtor_code VARCHAR(255),
    billed_entity_id INTEGER,
    account_number INTEGER,
    room_number VARCHAR(255),
    booking_reference_number VARCHAR(255),
    revenue_center VARCHAR(255),
    fin_erp_posting_date DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    modified_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    deleted BOOLEAN DEFAULT FALSE
);

CREATE TABLE back_office_charge_details (
    uu_id VARCHAR(255) PRIMARY KEY,
    bill_id VARCHAR(255),
    charge_split_id INTEGER,
    charge_id INTEGER,
    pretax_amount FLOAT,
    posttax_amount FLOAT,
    tax_amount FLOAT,
    tax_details JSONB,
    charge_type VARCHAR(255),
    bill_to_type VARCHAR(255),
    item_id VARCHAR(255),
    sku_category_id VARCHAR(255),
    applicable_business_date DATE,
    posting_date DATE,
    is_inclusion_charge BOOLEAN DEFAULT FALSE,
    category VARCHAR(255),
    owner_name VARCHAR(255),
    folio_number INTEGER,
    booking_id VARCHAR(255),
    hotel_id VARCHAR(255),
    billed_entity_id INTEGER,
    account_number INTEGER,
    booking_reference_number VARCHAR(255),
    revenue_center VARCHAR(255),
    fin_erp_posting_date DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    modified_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    deleted BOOLEAN DEFAULT FALSE
);

CREATE TABLE back_office_allowance_details (
    uu_id VARCHAR(255) PRIMARY KEY,
    bill_id VARCHAR(255),
    allowance_id INTEGER,
    charge_id INTEGER,
    charge_split_id INTEGER,
    posting_date DATE,
    tax_amount FLOAT,
    posttax_amount FLOAT,
    pretax_amount FLOAT,
    tax_details JSONB,
    charge_type VARCHAR(255),
    bill_to_type VARCHAR(255),
    item_id VARCHAR(255),
    sku_category_id VARCHAR(255),
    hotel_id VARCHAR(255),
    booking_id VARCHAR(255),
    category VARCHAR,
    owner_name VARCHAR,
    folio_number INTEGER,
    billed_entity_id INTEGER,
    account_number INTEGER,
    booking_reference_number VARCHAR(255),
    revenue_center VARCHAR(255),
    fin_erp_posting_date DATE,
    deleted BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    modified_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

CREATE TABLE back_office_folio_details (
    uu_id VARCHAR(255) PRIMARY KEY,
    bill_id VARCHAR(255),
    hotel_id VARCHAR(255),
    booking_id VARCHAR(255),
    category VARCHAR(255),
    owner_name VARCHAR(255),
    folio_number INTEGER,
    folio_status VARCHAR(255),
    billed_entity_id INTEGER,
    account_number INTEGER,
    booking_reference_number VARCHAR(255),
    is_credit_folio BOOLEAN,
    payment_split_details JSONB,
    fin_erp_posting_date DATE,
    deleted BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    modified_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

CREATE UNIQUE INDEX unique_active_back_office_payment_details
ON back_office_payment_details (bill_id, payment_id, payment_split_id)
WHERE deleted = FALSE;

CREATE UNIQUE INDEX unique_active_back_office_allowance_details
ON back_office_allowance_details (bill_id, allowance_id, charge_id, charge_split_id)
WHERE deleted = FALSE;

ALTER TABLE pos_revenue_item
ADD COLUMN sku_id VARCHAR(255),
ADD COLUMN payment_mode_sub_type VARCHAR(255);


-- downgrade
DROP TABLE ledgers_file_record;
DROP TABLE transaction_master;
DROP TABLE gl_ledger_item;
DROP TABLE cl_ledger_item;

DROP TABLE back_office_charge_details;
DROP TABLE back_office_payment_details;
DROP TABLE back_office_allowance_details;
DROP TABLE back_office_folio_details;

ALTER TABLE pos_revenue_item
DROP COLUMN sku_id,
DROP COLUMN payment_mode_sub_type;
