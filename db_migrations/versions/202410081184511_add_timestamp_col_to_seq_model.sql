-- revision: '202410081184511_add_timestamp_col_to_seq_model'
-- down_revision: '202408201184511_add_table_of_invoice_communications'

-- upgrade
alter table stay_summary_sequence
ADD COLUMN created_at timestamp with time zone DEFAULT now() NOT NULL;

alter table stay_summary_sequence
ADD COLUMN modified_at timestamp with time zone DEFAULT now() NOT NULL;


alter table stay_summary
ADD COLUMN created_at timestamp with time zone DEFAULT now() NOT NULL;

alter table stay_summary
ADD COLUMN modified_at timestamp with time zone DEFAULT now() NOT NULL;

-- downgrade

ALTER TABLE stay_summary_sequence DROP COLUMN created_at;
ALTER TABLE stay_summary_sequence DROP COLUMN modified_at;


ALTER TABLE stay_summary DROP COLUMN created_at;
ALTER TABLE stay_summary DROP COLUMN modified_at;
