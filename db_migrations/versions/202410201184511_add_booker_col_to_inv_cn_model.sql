-- revision: '202410201184511_add_booker_col_to_inv_cn_model'
-- down_revision: '202410081184511_add_timestamp_col_to_seq_model'

-- upgrade
alter table invoice
ADD COLUMN booker_legal_entity_id varchar(50);

alter table credit_note
ADD COLUMN booker_legal_entity_id varchar(50);

DROP INDEX ix_invoice_is_b2b_invoice_type_customer;
DROP INDEX ix_invoice_booking_reference_number;
DROP INDEX ix_invoice_is_b2b_invoice_type;

DROP INDEX ix_credit_note_booking_reference_number;
DROP INDEX ix_credit_note_is_b2b_invoice_type;

CREATE INDEX ix_invoice_is_b2b_invoice_type_booker ON invoice (
    booker_legal_entity_id,
    is_b2b,
    invoice_type,
    invoice_date,
    status
);
CREATE INDEX ix_credit_note_is_b2b_invoice_type_booker ON credit_note (is_b2b, credit_note_type, booker_legal_entity_id);

-- downgrade

DROP INDEX ix_invoice_is_b2b_invoice_type_booker;
DROP INDEX ix_credit_note_is_b2b_invoice_type_booker;

ALTER TABLE invoice DROP COLUMN booker_legal_entity_id;
ALTER TABLE credit_note DROP COLUMN booker_legal_entity_id;
