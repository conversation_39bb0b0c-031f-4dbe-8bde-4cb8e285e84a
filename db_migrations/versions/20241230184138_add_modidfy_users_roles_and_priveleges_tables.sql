-- revision: '20241230184138_add_modidfy_users_roles_and_priveleges_tables'
-- down_revision: '20241115113927_adding_new_cols_in_payment_data_and_ledger_items_model'

-- upgrade
CREATE TABLE roles (
    id SERIAL PRIMARY KEY,
    name VA<PERSON><PERSON>R UNIQUE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted BOOLEAN DEFAULT FALSE
);

CREATE TABLE privileges (
    id SERIAL PRIMARY KEY,
    name VARCHAR UNIQUE NOT NULL,
    description VARCHAR,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted BOOLEAN DEFAULT FALSE
);

CREATE TABLE roles_privileges (
    id SERIAL PRIMARY KEY,
    role_id INTEGER NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
    privilege_id INTEGER NOT NULL REFERENCES privileges(id) ON DELETE CASCADE,
    CONSTRAINT role_privilege_unique UNIQUE (role_id, privilege_id)
);

ALTER TABLE user_groups
    DROP CONSTRAINT user_groups_pkey,
    ADD COLUMN id SERIAL PRIMARY KEY,
    ADD COLUMN role_id INTEGER REFERENCES roles(id),
    ADD CONSTRAINT user_groups_email_unique UNIQUE (email);

ALTER TABLE user_groups DROP COLUMN role;

-- downgrade
ALTER TABLE user_groups
    DROP CONSTRAINT user_groups_email_unique,
    DROP COLUMN id,
    DROP COLUMN role_id;

ALTER TABLE user_groups
    ADD CONSTRAINT user_groups_pkey PRIMARY KEY (email);

ALTER TABLE user_groups ADD COLUMN role VARCHAR;

DROP TABLE roles_privileges;

DROP TABLE privileges;

DROP TABLE roles;
