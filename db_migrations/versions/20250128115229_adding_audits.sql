-- revision: '20250128115229_adding_audits'
-- down_revision: '20241230184138_add_modidfy_users_roles_and_priveleges_tables'


-- upgrade
CREATE TABLE audit_logs (
    id SERIAL PRIMARY KEY,
    user_action VARCHAR,
    user_details JSONB,
    entity_name VA<PERSON>HA<PERSON>,
    entity_id VARCHAR,
    request_id VARCHAR,
    source VARCHAR,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    details JSONB
);
DROP table entity_level_audits;

-- downgrade
DROP TABLE audit_logs;
CREATE TABLE entity_level_audits (
    id SERIAL PRIMARY KEY,
    entity_name VARCHAR(255) NOT NULL,
    uu_id VARCHAR(255) NOT NULL,
    event_type VARCHAR(255) NOT NULL,
    event_data TEXT,
    created_at timestamp with time zone DEFAULT now() NOT NULL
);

