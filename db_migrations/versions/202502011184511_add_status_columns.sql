-- revision: '202502011184511_add_status_columns'
-- down_revision: '20250128115229_adding_audits'

-- upgrade
ALTER TABLE corporate RENAME COLUMN nav_push_remark to erp_remarks;
DROP INDEX ix_corporate_pushed;
ALTER TABLE corporate ADD COLUMN status varchar(255);
UPDATE corporate SET status='ingested' where pushed is false;
UPDATE corporate SET status='pushed' where pushed is true;
CREATE INDEX ix_corporate_status ON corporate (status);
ALTER TABLE corporate DROP COLUMN pushed;

ALTER TABLE hotel RENAME COLUMN nav_push_remark to erp_remarks;
ALTER TABLE hotel ADD COLUMN status varchar(255);
UPDATE hotel SET status='ingested' where pushed is false;
UPDATE hotel SET status='pushed' where pushed is true;
ALTER TABLE hotel DROP COLUMN pushed;

ALTER TABLE ota RENAME COLUMN nav_push_remark to erp_remarks;
ALTER TABLE ota ADD COLUMN status varchar(255);
UPDATE ota SET status='ingested' where pushed is false;
UPDATE ota SET status='pushed' where pushed is true;
ALTER TABLE ota DROP COLUMN pushed;

ALTER TABLE pg_payments RENAME COLUMN nav_push_remark to erp_remarks;

ALTER TABLE pg_payments_summary RENAME COLUMN nav_push_remark to erp_remarks;
DROP INDEX ix_pg_payments_summary_pushed;
ALTER TABLE pg_payments_summary ADD COLUMN status varchar(255);
UPDATE pg_payments_summary SET status='ingested' where pushed is false;
UPDATE pg_payments_summary SET status='pushed' where pushed is true;
CREATE INDEX ix_pg_payments_summary_status ON pg_payments_summary (status);
ALTER TABLE pg_payments_summary DROP COLUMN pushed;

ALTER TABLE pos_revenue_item RENAME COLUMN ext_push_remark to erp_remarks;
ALTER TABLE pos_revenue_item ADD COLUMN status varchar(255);
UPDATE pos_revenue_item SET status='ingested' where pushed is false;
UPDATE pos_revenue_item SET status='pushed' where pushed is true;
ALTER TABLE pos_revenue_item DROP COLUMN pushed;

ALTER TABLE purchase_invoice RENAME COLUMN nav_push_remark to erp_remarks;
DROP INDEX ix_purchase_invoice_pushed;
ALTER TABLE purchase_invoice ADD COLUMN status varchar(255);
UPDATE purchase_invoice SET status='ingested' where pushed is false;
UPDATE purchase_invoice SET status='pushed' where pushed is true;
CREATE INDEX ix_purchase_invoice_status ON purchase_invoice (status);
ALTER TABLE purchase_invoice DROP COLUMN pushed;

ALTER TABLE sale_invoice RENAME COLUMN nav_push_remark to erp_remarks;

ALTER TABLE sales_summary RENAME COLUMN nav_push_remark to erp_remarks;
DROP INDEX ix_sales_summary_pushed_deleted;
DROP INDEX ix_sales_summary_pushed;
ALTER TABLE sales_summary ADD COLUMN status varchar(255);
UPDATE sales_summary SET status='ingested' where pushed is false;
UPDATE sales_summary SET status='pushed' where pushed is true;
CREATE INDEX ix_sales_summary_status_deleted ON sales_summary (status, deleted);
CREATE INDEX ix_sales_summary_status ON sales_summary (status);
ALTER TABLE sales_summary DROP COLUMN pushed;

ALTER TABLE settlement_expense RENAME COLUMN nav_push_remark to erp_remarks;
ALTER TABLE settlement_expense ADD COLUMN status varchar(255);
UPDATE settlement_expense SET status='ingested' where pushed is false;
UPDATE settlement_expense SET status='pushed' where pushed is true;
ALTER TABLE settlement_expense DROP COLUMN pushed;

ALTER TABLE settlement_hotel_adjustment RENAME COLUMN nav_push_remark to erp_remarks;
ALTER TABLE settlement_hotel_adjustment ADD COLUMN status varchar(255);
UPDATE settlement_hotel_adjustment SET status='ingested' where pushed is false;
UPDATE settlement_hotel_adjustment SET status='pushed' where pushed is true;
ALTER TABLE settlement_hotel_adjustment DROP COLUMN pushed;

ALTER TABLE settlement_loan RENAME COLUMN nav_push_remark to erp_remarks;
ALTER TABLE settlement_loan ADD COLUMN status varchar(255);
UPDATE settlement_loan SET status='ingested' where pushed is false;
UPDATE settlement_loan SET status='pushed' where pushed is true;
ALTER TABLE settlement_loan DROP COLUMN pushed;

ALTER TABLE settlement_tax RENAME COLUMN nav_push_remark to erp_remarks;
ALTER TABLE settlement_tax ADD COLUMN status varchar(255);
UPDATE settlement_tax SET status='ingested' where pushed is false;
UPDATE settlement_tax SET status='pushed' where pushed is true;
ALTER TABLE settlement_tax DROP COLUMN pushed;

ALTER TABLE settlement_treebo_fee RENAME COLUMN nav_push_remark to erp_remarks;
ALTER TABLE settlement_treebo_fee ADD COLUMN status varchar(255);
UPDATE settlement_treebo_fee SET status='ingested' where pushed is false;
UPDATE settlement_treebo_fee SET status='pushed' where pushed is true;
ALTER TABLE settlement_treebo_fee DROP COLUMN pushed;

ALTER TABLE ta_commission RENAME COLUMN nav_push_remark to erp_remarks;
DROP INDEX ix_ta_commission_reference_number_pushed;
ALTER TABLE ta_commission ADD COLUMN status varchar(255);
UPDATE ta_commission SET status='ingested' where pushed is false;
UPDATE ta_commission SET status='pushed' where pushed is true;
CREATE INDEX ix_ta_commission_reference_number_status ON ta_commission (reference_number, status);
ALTER TABLE ta_commission DROP COLUMN pushed;


-- downgrade

ALTER TABLE ta_commission ADD COLUMN pushed boolean;
UPDATE ta_commission SET pushed = false WHERE status = 'ingested';
UPDATE ta_commission SET pushed = true  WHERE status = 'pushed';
DROP INDEX ix_ta_commission_reference_number_status;
ALTER TABLE ta_commission DROP COLUMN status;
CREATE INDEX ix_ta_commission_reference_number_pushed ON ta_commission (reference_number, pushed);
ALTER TABLE ta_commission RENAME COLUMN erp_remarks TO nav_push_remark;

ALTER TABLE settlement_treebo_fee ADD COLUMN pushed boolean;
UPDATE settlement_treebo_fee SET pushed = false WHERE status = 'ingested';
UPDATE settlement_treebo_fee SET pushed = true  WHERE status = 'pushed';
ALTER TABLE settlement_treebo_fee DROP COLUMN status;
ALTER TABLE settlement_treebo_fee RENAME COLUMN erp_remarks TO nav_push_remark;

ALTER TABLE settlement_tax ADD COLUMN pushed boolean;
UPDATE settlement_tax SET pushed = false WHERE status = 'ingested';
UPDATE settlement_tax SET pushed = true  WHERE status = 'pushed';
ALTER TABLE settlement_tax DROP COLUMN status;
ALTER TABLE settlement_tax RENAME COLUMN erp_remarks TO nav_push_remark;

ALTER TABLE settlement_loan ADD COLUMN pushed boolean;
UPDATE settlement_loan SET pushed = false WHERE status = 'ingested';
UPDATE settlement_loan SET pushed = true  WHERE status = 'pushed';
ALTER TABLE settlement_loan DROP COLUMN status;
ALTER TABLE settlement_loan RENAME COLUMN erp_remarks TO nav_push_remark;

ALTER TABLE settlement_hotel_adjustment ADD COLUMN pushed boolean;
UPDATE settlement_hotel_adjustment SET pushed = false WHERE status = 'ingested';
UPDATE settlement_hotel_adjustment SET pushed = true  WHERE status = 'pushed';
ALTER TABLE settlement_hotel_adjustment DROP COLUMN status;
ALTER TABLE settlement_hotel_adjustment RENAME COLUMN erp_remarks TO nav_push_remark;

ALTER TABLE settlement_expense ADD COLUMN pushed boolean;
UPDATE settlement_expense SET pushed = false WHERE status = 'ingested';
UPDATE settlement_expense SET pushed = true  WHERE status = 'pushed';
ALTER TABLE settlement_expense DROP COLUMN status;
ALTER TABLE settlement_expense RENAME COLUMN erp_remarks TO nav_push_remark;

ALTER TABLE sales_summary ADD COLUMN pushed boolean;
UPDATE sales_summary SET pushed = false WHERE status = 'ingested';
UPDATE sales_summary SET pushed = true  WHERE status = 'pushed';
DROP INDEX ix_sales_summary_status;
DROP INDEX ix_sales_summary_status_deleted;
ALTER TABLE sales_summary DROP COLUMN status;
CREATE INDEX ix_sales_summary_pushed ON sales_summary (pushed);
CREATE INDEX ix_sales_summary_pushed_deleted ON sales_summary (pushed, deleted);
ALTER TABLE sales_summary RENAME COLUMN erp_remarks TO nav_push_remark;

ALTER TABLE sale_invoice RENAME COLUMN erp_remarks TO nav_push_remark;

ALTER TABLE purchase_invoice ADD COLUMN pushed boolean;
UPDATE purchase_invoice SET pushed = false WHERE status = 'ingested';
UPDATE purchase_invoice SET pushed = true  WHERE status = 'pushed';
DROP INDEX ix_purchase_invoice_status;
ALTER TABLE purchase_invoice DROP COLUMN status;
CREATE INDEX ix_purchase_invoice_pushed ON purchase_invoice (pushed);
ALTER TABLE purchase_invoice RENAME COLUMN erp_remarks TO nav_push_remark;

ALTER TABLE pos_revenue_item ADD COLUMN pushed boolean;
UPDATE pos_revenue_item SET pushed = false WHERE status = 'ingested';
UPDATE pos_revenue_item SET pushed = true  WHERE status = 'pushed';
ALTER TABLE pos_revenue_item DROP COLUMN status;
ALTER TABLE pos_revenue_item RENAME COLUMN erp_remarks TO ext_push_remark;

ALTER TABLE pg_payments_summary ADD COLUMN pushed boolean;
UPDATE pg_payments_summary SET pushed = false WHERE status = 'ingested';
UPDATE pg_payments_summary SET pushed = true  WHERE status = 'pushed';
DROP INDEX ix_pg_payments_summary_status;
ALTER TABLE pg_payments_summary DROP COLUMN status;
CREATE INDEX ix_pg_payments_summary_pushed ON pg_payments_summary (pushed);
ALTER TABLE pg_payments_summary RENAME COLUMN erp_remarks TO nav_push_remark;

ALTER TABLE pg_payments RENAME COLUMN erp_remarks TO nav_push_remark;

ALTER TABLE ota ADD COLUMN pushed boolean;
UPDATE ota SET pushed = false WHERE status = 'ingested';
UPDATE ota SET pushed = true  WHERE status = 'pushed';
ALTER TABLE ota DROP COLUMN status;
ALTER TABLE ota RENAME COLUMN erp_remarks TO nav_push_remark;

ALTER TABLE hotel ADD COLUMN pushed boolean;
UPDATE hotel SET pushed = false WHERE status = 'ingested';
UPDATE hotel SET pushed = true  WHERE status = 'pushed';
ALTER TABLE hotel DROP COLUMN status;
ALTER TABLE hotel RENAME COLUMN erp_remarks TO nav_push_remark;

ALTER TABLE corporate ADD COLUMN pushed boolean;
UPDATE corporate SET pushed = false WHERE status = 'ingested';
UPDATE corporate SET pushed = true  WHERE status = 'pushed';
DROP INDEX ix_corporate_status;
ALTER TABLE corporate DROP COLUMN status;
CREATE INDEX ix_corporate_pushed ON corporate (pushed);
ALTER TABLE corporate RENAME COLUMN erp_remarks TO nav_push_remark;
