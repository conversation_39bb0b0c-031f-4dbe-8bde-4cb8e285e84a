-- revision: '20250414153006_standardize_database_data_types'
-- down_revision: '202504051184511_add_purchase_types_in_purchase_table'
-- upgrade

ALTER TABLE ota
ALTER COLUMN paid_at_ota TYPE NUMERIC(20, 4) USING CASE WHEN paid_at_ota::text = '' OR paid_at_ota IS NULL THEN NULL ELSE paid_at_ota::numeric(20, 4) END,
ALTER COLUMN commission_amount TYPE NUMERIC(20, 4) USING CASE WHEN commission_amount::text = '' OR commission_amount IS NULL THEN NULL ELSE commission_amount::numeric(20, 4) END,
ALTER COLUMN commission_percent TYPE NUMERIC(20, 4) USING CASE WHEN commission_percent::text = '' OR commission_percent IS NULL THEN NULL ELSE commission_percent::numeric(20, 4) END,
ALTER COLUMN pretax_room_rent TYPE NUMERIC(20, 4) USING CASE WHEN pretax_room_rent::text = '' OR pretax_room_rent IS NULL THEN NULL ELSE pretax_room_rent::numeric(20, 4) END;


ALTER TABLE pg_payments
ALTER COLUMN pg_charges TYPE NUMERIC(20, 4) USING CASE WHEN pg_charges::text = '' OR pg_charges IS NULL THEN NULL ELSE pg_charges::numeric(20, 4) END,
ALTER COLUMN pg_tax TYPE NUMERIC(20, 4) USING CASE WHEN pg_tax::text = '' OR pg_tax IS NULL THEN NULL ELSE pg_tax::numeric(20, 4) END,
ALTER COLUMN payment_amount TYPE NUMERIC(20, 4) USING CASE WHEN payment_amount::text = '' OR payment_amount IS NULL THEN NULL ELSE payment_amount::numeric(20, 4) END,
ALTER COLUMN platform_fees TYPE NUMERIC(20, 4) USING CASE WHEN platform_fees::text = '' OR platform_fees IS NULL THEN NULL ELSE platform_fees::numeric(20, 4) END,
ALTER COLUMN original_booking_amount TYPE NUMERIC(20, 4) USING CASE WHEN original_booking_amount::text = '' OR original_booking_amount IS NULL THEN NULL ELSE original_booking_amount::numeric(20, 4) END;


ALTER TABLE pg_payments_summary
ALTER COLUMN pg_charges TYPE NUMERIC(20, 4) USING CASE WHEN pg_charges::text = '' OR pg_charges IS NULL THEN NULL ELSE pg_charges::numeric(20, 4) END,
ALTER COLUMN pg_tax TYPE NUMERIC(20, 4) USING CASE WHEN pg_tax::text = '' OR pg_tax IS NULL THEN NULL ELSE pg_tax::numeric(20, 4) END,
ALTER COLUMN payment_amount TYPE NUMERIC(20, 4) USING CASE WHEN payment_amount::text = '' OR payment_amount IS NULL THEN NULL ELSE payment_amount::numeric(20, 4) END,
ALTER COLUMN platform_fees TYPE NUMERIC(20, 4) USING CASE WHEN platform_fees::text = '' OR platform_fees IS NULL THEN NULL ELSE platform_fees::numeric(20, 4) END,
ALTER COLUMN original_booking_amount TYPE NUMERIC(20, 4) USING CASE WHEN original_booking_amount::text = '' OR original_booking_amount IS NULL THEN NULL ELSE original_booking_amount::numeric(20, 4) END;


ALTER TABLE purchase_invoice
ALTER COLUMN unit_price TYPE NUMERIC(20, 4) USING CASE WHEN unit_price::text = '' OR unit_price IS NULL THEN NULL ELSE unit_price::numeric(20, 4) END,
ALTER COLUMN tax_percentage TYPE NUMERIC(20, 4) USING CASE WHEN tax_percentage::text = '' OR tax_percentage IS NULL THEN NULL ELSE tax_percentage::numeric(20, 4) END,
ALTER COLUMN total_invoice_amount TYPE NUMERIC(20, 4) USING CASE WHEN total_invoice_amount::text = '' OR total_invoice_amount IS NULL THEN NULL ELSE total_invoice_amount::numeric(20, 4) END;


ALTER TABLE sale_invoice
ALTER COLUMN unit_price TYPE NUMERIC(20, 4) USING CASE WHEN unit_price::text = '' OR unit_price IS NULL THEN NULL ELSE unit_price::numeric(20, 4) END,
ALTER COLUMN tax_percentage TYPE NUMERIC(20, 4) USING CASE WHEN tax_percentage::text = '' OR tax_percentage IS NULL THEN NULL ELSE tax_percentage::numeric(20, 4) END,
ALTER COLUMN total_invoice_amount TYPE NUMERIC(20, 4) USING CASE WHEN total_invoice_amount::text = '' OR total_invoice_amount IS NULL THEN NULL ELSE total_invoice_amount::numeric(20, 4) END,
ALTER COLUMN source_created_on TYPE DATE USING CASE WHEN TRIM(COALESCE(source_created_on::text, '')) = '' THEN NULL ELSE source_created_on::DATE END;


ALTER TABLE settlement_expense
ALTER COLUMN invoice_amount TYPE NUMERIC(20, 4) USING CASE WHEN invoice_amount::text = '' OR invoice_amount IS NULL THEN NULL ELSE invoice_amount::numeric(20, 4) END,
ALTER COLUMN tds_per TYPE NUMERIC(20, 4) USING CASE WHEN tds_per::text = '' OR tds_per IS NULL THEN NULL ELSE tds_per::numeric(20, 4) END;


ALTER TABLE settlement_hotel_adjustment
ALTER COLUMN amount TYPE NUMERIC(20, 4) USING CASE WHEN amount::text = '' OR amount IS NULL THEN NULL ELSE amount::numeric(20, 4) END,
ALTER COLUMN invoice_amount TYPE NUMERIC(20, 4) USING CASE WHEN invoice_amount::text = '' OR invoice_amount IS NULL THEN NULL ELSE invoice_amount::numeric(20, 4) END;


ALTER TABLE settlement_loan
ALTER COLUMN loan_amount TYPE NUMERIC(20, 4) USING CASE WHEN loan_amount::text = '' OR loan_amount IS NULL THEN NULL ELSE loan_amount::numeric(20, 4) END;


ALTER TABLE settlement_tax
ALTER COLUMN cgst_amount TYPE NUMERIC(20, 4) USING CASE WHEN cgst_amount::text = '' OR cgst_amount IS NULL THEN NULL ELSE cgst_amount::numeric(20, 4) END,
ALTER COLUMN sgst_amount TYPE NUMERIC(20, 4) USING CASE WHEN sgst_amount::text = '' OR sgst_amount IS NULL THEN NULL ELSE sgst_amount::numeric(20, 4) END,
ALTER COLUMN total_amount TYPE NUMERIC(20, 4) USING CASE WHEN total_amount::text = '' OR total_amount IS NULL THEN NULL ELSE total_amount::numeric(20, 4) END;


ALTER TABLE settlement_treebo_fee
ALTER COLUMN amount TYPE NUMERIC(20, 4) USING CASE WHEN amount::text = '' OR amount IS NULL THEN NULL ELSE amount::numeric(20, 4) END,
ALTER COLUMN gst_percent TYPE NUMERIC(20, 4) USING CASE WHEN gst_percent::text = '' OR gst_percent IS NULL THEN NULL ELSE gst_percent::numeric(20, 4) END;


ALTER TABLE ta_commission
ALTER COLUMN commission_percent TYPE NUMERIC(20, 4) USING CASE WHEN commission_percent::text = '' OR commission_percent IS NULL THEN NULL ELSE commission_percent::numeric(20, 4) END;

-- downgrade

ALTER TABLE ota
ALTER COLUMN paid_at_ota TYPE VARCHAR(255),
ALTER COLUMN commission_amount TYPE VARCHAR(255),
ALTER COLUMN commission_percent TYPE VARCHAR(255),
ALTER COLUMN pretax_room_rent TYPE VARCHAR(255);


ALTER TABLE pg_payments
ALTER COLUMN pg_charges TYPE VARCHAR(255),
ALTER COLUMN pg_tax TYPE VARCHAR(255),
ALTER COLUMN platform_fees TYPE VARCHAR(255),
ALTER COLUMN payment_amount TYPE VARCHAR(255),
ALTER COLUMN original_booking_amount TYPE VARCHAR(255);


ALTER TABLE pg_payments_summary
ALTER COLUMN pg_charges TYPE VARCHAR(255),
ALTER COLUMN pg_tax TYPE VARCHAR(255),
ALTER COLUMN platform_fees TYPE VARCHAR(255),
ALTER COLUMN payment_amount TYPE VARCHAR(255),
ALTER COLUMN original_booking_amount TYPE VARCHAR(255);


ALTER TABLE purchase_invoice
ALTER COLUMN unit_price TYPE VARCHAR(255),
ALTER COLUMN tax_percentage TYPE VARCHAR(255),
ALTER COLUMN total_invoice_amount TYPE VARCHAR(255);


ALTER TABLE sale_invoice
ALTER COLUMN unit_price TYPE VARCHAR(255),
ALTER COLUMN tax_percentage TYPE VARCHAR(255),
ALTER COLUMN total_invoice_amount TYPE VARCHAR(255),
ALTER COLUMN source_created_on TYPE VARCHAR(255);


ALTER TABLE settlement_expense
ALTER COLUMN invoice_amount TYPE VARCHAR(255),
ALTER COLUMN tds_per TYPE VARCHAR(255);


ALTER TABLE settlement_hotel_adjustment
ALTER COLUMN amount TYPE VARCHAR(255),
ALTER COLUMN invoice_amount TYPE VARCHAR(255);


ALTER TABLE settlement_loan
ALTER COLUMN loan_amount TYPE VARCHAR(255);


ALTER TABLE settlement_tax
ALTER COLUMN cgst_amount TYPE VARCHAR(255),
ALTER COLUMN sgst_amount TYPE VARCHAR(255),
ALTER COLUMN total_amount TYPE VARCHAR(255);


ALTER TABLE settlement_treebo_fee
ALTER COLUMN amount TYPE VARCHAR(255),
ALTER COLUMN gst_percent TYPE VARCHAR(255);


ALTER TABLE ta_commission
ALTER COLUMN commission_percent TYPE VARCHAR(255);