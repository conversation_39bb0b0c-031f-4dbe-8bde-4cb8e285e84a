version: "3"

services:
  finance_erp_service:
    image: "${DOCKER_REGISTRY}${DOCKER_TAG_NAME}:${VERSION}"
    env_file: "${ENV_FILE}"
    restart: always
    environment:
      - AWS_REGION=${regions}
      - CLUSTER_IDENTIFIER=${CLUSTER_IDENTIFIER}
    entrypoint: /usr/src/finance_erp/scripts/gunicorn_start
    volumes:
      - "${LOG_ROOT}:/ebs1/logs"
    ports:
      - "${FINANCE_ERP_SERVICE_PORT}:9021"
    container_name: "finance_erp_service"
    tty: true
