version: "3"

services:
  finance_erp_job_executor:
    image: "${DOCKER_REGISTRY}${DOCKER_TAG_NAME}:${VERSION}"
    env_file: "${ENV_FILE}"
    environment:
      - AWS_REGION=${regions}
      - CLUSTER_IDENTIFIER=${CLUSTER_IDENTIFIER}
      - TENANT_ID=${TENANT_ID}
    volumes:
      - "${LOG_ROOT}:/ebs1/logs"
    restart: always
    tty: true
    container_name: "finance_erp_job_executor_${TENANT_ID}"
    entrypoint: ["/usr/src/finance_erp/scripts/job_executor_start", "${TENANT_ID}"]
