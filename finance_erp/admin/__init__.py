import time

import jwt
from flask import current_app as app
from flask import redirect
from flask_admin import Admin, AdminIndexView, expose
from flask_admin.menu import MenuLink
from flask_login import current_user
from treebo_commons.multitenancy.sqlalchemy import db_engine

from finance_erp.admin.access_management import (
    PrivilegesView,
    RolesPrivilegesView,
    RolesView,
    UserGroupView,
)
from finance_erp.admin.back_office.allowance_details import AllowanceView
from finance_erp.admin.back_office.charge_details import ChargeView
from finance_erp.admin.back_office.folio_details import FolioDetailsView
from finance_erp.admin.back_office.payment_details import PaymentView
from finance_erp.admin.back_office.transaction_master import (
    RefreshTransactionMasterItemView,
    TransactionMasterItemView,
)
from finance_erp.admin.invoice.invoice import InvoiceDownloadView
from finance_erp.admin.pos.pos_revenue import POSRevenueItemView
from finance_erp.admin.reports.corporate import CorporateView, IngestCorporatesView
from finance_erp.admin.reports.hotel import HotelView, IngestHotelsView
from finance_erp.admin.reports.ota_commission import OtaCommissionReportView
from finance_erp.admin.reports.pg_payment.pg_payment import PGPaymentView
from finance_erp.admin.reports.pg_payment.pg_payment_summary import PGPaymentSummaryView
from finance_erp.admin.reports.purchase_invoice import PurchaseInvoiceView
from finance_erp.admin.reports.sales.sales_invoice import SalesInvoiceView
from finance_erp.admin.reports.sales.sales_invoice_summary import (
    SalesInvoiceSummaryView,
)
from finance_erp.admin.reports.settlement.expense import ExpenseView
from finance_erp.admin.reports.settlement.hotel_adjustment import HotelAdjustmentView
from finance_erp.admin.reports.settlement.loan import LoanView
from finance_erp.admin.reports.settlement.tax import TaxView
from finance_erp.admin.reports.settlement.treebo_fee import TreeboFeeView
from finance_erp.admin.reports.ta_commission import TaCommissionReportView
from finance_erp.common import constants
from finance_erp.domain.auth.models import (
    PrivilegesModel,
    RolesModel,
    RolesPrivilegesModel,
    UserGroupModel,
)
from finance_erp.domain.back_office.models import (
    AllowanceModel,
    ChargeModel,
    FolioDetailsModel,
    PaymentModel,
    TransactionMaster,
)
from finance_erp.domain.company_profile.models import CorporateModel
from finance_erp.domain.hotel.models import HotelModel
from finance_erp.domain.ota.models import OtaModel
from finance_erp.domain.payment.models import PGPaymentModel, PGPaymentSummaryModel
from finance_erp.domain.pos.models import POSRevenueItemModel
from finance_erp.domain.reseller.models import (
    PurchaseInvoiceModel,
    SaleInvoiceModel,
    SalesSummaryModel,
)
from finance_erp.domain.settlement.expense.models import ExpenseModel
from finance_erp.domain.settlement.hotel_adjustment.models import HotelAdjustmentModel
from finance_erp.domain.settlement.loan.models import LoanModel
from finance_erp.domain.settlement.tax.models import TaxModel
from finance_erp.domain.settlement.treebo_fee.models import TreeboFeeModel
from finance_erp.domain.ta_commission.models import TaCommissionModel
from finance_erp.infrastructure.external_clients.catalog_service_client import (
    CatalogServiceClient,
)
from object_registry import locate_instance


class FinanceERPAdminIndexView(AdminIndexView):
    def is_visible(self):
        # This view won't appear in the menu structure
        return app.config.get("ENV") == "production"

    @expose("/")
    def index(self):
        if not current_user.is_authenticated and app.config["ENV"] not in [
            "local",
            "development",
        ]:
            return redirect("/erp/login")
        if app.config.get("ENV") == "production":
            catalog_service_client = locate_instance(CatalogServiceClient)
            metabase_dashboard_id = catalog_service_client.get_metabase_dashboard_id()
            if metabase_dashboard_id is None:
                return redirect("/erp/admin/purchaseinvoicemodel")
            resource = dict(dashboard=metabase_dashboard_id)
            payload = dict(
                resource=resource,
                params=dict(),
                exp=round(time.time())
                + int(app.config["METABASE_EMBED_EXPIRATION_TIME"]),
            )
            token = jwt.encode(
                payload, app.config["METABASE_SECRET_KEY"], algorithm="HS256"
            )
            params = {
                "report_url": f'{app.config["METABASE_BASE_URL"]}embed/dashboard/{token}#bordered=true&titled=true'
            }
            return self.render("home.html", **params)
        return redirect("/erp/admin/purchaseinvoicemodel")


def setup_admin(app, tenant_id):
    app.config["FLASK_ADMIN_SWATCH"] = "yeti"
    admin = Admin(
        app,
        name="Finance Admin",
        index_view=FinanceERPAdminIndexView(url="/erp/admin"),
        template_mode=constants.ADMIN_TEMPLATE,
        url="/erp/admin",
    )  # pylint:  disable=invalid-name
    setup_admin_views(admin, tenant_id)


def setup_admin_views(admin, tenant_id):
    session = db_engine.get_scoped_session(tenant_id=tenant_id)
    views = [
        PurchaseInvoiceView(
            PurchaseInvoiceModel, session, category="Invoice", name="Purchase"
        ),
        SalesInvoiceView(SaleInvoiceModel, session, category="Invoice", name="Sales"),
        SalesInvoiceSummaryView(
            SalesSummaryModel, session, category="Invoice", name="Sales Invoice Summary"
        ),
        PGPaymentView(PGPaymentModel, session, name="Payment"),
        PGPaymentSummaryView(PGPaymentSummaryModel, session, name="Payment Summary"),
        CorporateView(
            CorporateModel, session, category="Corporate", name="Corporate List"
        ),
        IngestCorporatesView(category="Corporate", name="Ingest Corporates"),
        OtaCommissionReportView(OtaModel, session, name="Ota Commission"),
        HotelView(HotelModel, session, category="Hotel", name="Hotel List"),
        IngestHotelsView(category="Hotel", name="Ingest Hotels"),
        TreeboFeeView(
            TreeboFeeModel, session, category="Settlement", name="Treebo Fee"
        ),
        TaxView(TaxModel, session, category="Settlement", name="Tax"),
        ExpenseView(ExpenseModel, session, category="Settlement", name="Expense"),
        LoanView(LoanModel, session, category="Settlement", name="Loan"),
        HotelAdjustmentView(
            HotelAdjustmentModel,
            session,
            category="Settlement",
            name="Hotel Adjustment",
        ),
        TaCommissionReportView(TaCommissionModel, session, name="Ta Commission"),
        POSRevenueItemView(POSRevenueItemModel, session, name="POS Revenue"),
        TransactionMasterItemView(
            TransactionMaster,
            session,
            category="Transaction Master",
            name="Transaction Master Model",
        ),
        RefreshTransactionMasterItemView(
            category="Transaction Master", name="Refresh Transaction Master"
        ),
        AllowanceView(
            AllowanceModel, session, category="Financial Data", name="Allowance Model"
        ),
        ChargeView(
            ChargeModel, session, category="Financial Data", name="Charge Model"
        ),
        FolioDetailsView(
            FolioDetailsModel,
            session,
            category="Financial Data",
            name="Folio Details Model",
        ),
        PaymentView(
            PaymentModel, session, category="Financial Data", name="Payment Model"
        ),
        InvoiceDownloadView(name="Download Invoices"),
        UserGroupView(
            UserGroupModel,
            session,
            category="Access Management",
            name="User Group Model",
        ),
        RolesView(
            RolesModel, session, category="Access Management", name="Roles Model"
        ),
        PrivilegesView(
            PrivilegesModel,
            session,
            category="Access Management",
            name="Privileges Model",
        ),
        RolesPrivilegesView(
            RolesPrivilegesModel,
            session,
            category="Access Management",
            name="Roles Privileges Model",
        ),
    ]

    for view in views:
        admin.add_views(view)
    admin.add_link(MenuLink(name="Logout", url="/erp/logout"))
