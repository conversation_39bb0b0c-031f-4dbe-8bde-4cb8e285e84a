from wtforms.validators import DataRequired, Regexp

from core.common.constants import PrivilegeCodes
from finance_erp.admin.base.admin_view import BaseAdminViewForNavisionModel
from finance_erp.common.constants import EMAIL_REGEX
from finance_erp.domain.auth.models import PrivilegesModel, RolesModel


class UserGroupView(BaseAdminViewForNavisionModel):
    column_list = ("email", "role.name", "created_at", "modified_at")
    column_labels = {
        "email": "Email",
        "role.name": "Role",
        "created_at": "Created At",
        "modified_at": "Modified At",
    }
    column_default_sort = ("modified_at", True)
    column_searchable_list = ("email", "role.name")
    column_filters = ["email", "role.name"]

    form_args = {
        "email": {
            "validators": [
                DataRequired(message="Email is required."),
                Regexp(
                    EMAIL_REGEX,
                    message="Please enter a valid email address.",
                ),
            ],
        },
        "role": {
            "query_factory": lambda: RolesModel.query.filter_by(deleted=False).order_by(
                RolesModel.name.asc()
            ),
            "get_label": "name",
            "validators": [DataRequired(message="Role is required.")],
        },
    }

    def is_accessible(self):
        return super().is_accessible() and self.has_privilege(
            PrivilegeCodes.CAN_MANAGE_ACCESS_CONTROLS
        )

    def get_query(self):
        return super().get_query().filter_by(deleted=False)

    def get_count_query(self):
        return super().get_count_query().filter_by(deleted=False)


class RolesView(BaseAdminViewForNavisionModel):
    column_list = ("name", "created_at", "modified_at")
    column_labels = {
        "name": "Name",
        "created_at": "Created At",
        "modified_at": "Modified At",
    }
    column_default_sort = ("modified_at", True)
    column_searchable_list = ("name",)
    column_filters = ["name"]
    form_excluded_columns = (
        "user_groups",
        "roles_privileges",
        "privileges",
        "created_at",
        "modified_at",
        "deleted",
    )

    form_args = {
        "name": {
            "validators": [
                DataRequired(message="Name is required."),
            ],
        }
    }

    def is_accessible(self):
        return super().is_accessible() and self.has_privilege(
            PrivilegeCodes.CAN_MANAGE_ACCESS_CONTROLS
        )

    def get_query(self):
        return super().get_query().filter_by(deleted=False)

    def get_count_query(self):
        return super().get_count_query().filter_by(deleted=False)


class PrivilegesView(BaseAdminViewForNavisionModel):
    column_list = ("name", "description", "created_at", "modified_at")
    column_labels = {
        "name": "Name",
        "description": "Description",
        "created_at": "Created At",
        "modified_at": "Modified At",
    }
    column_default_sort = ("modified_at", True)
    column_searchable_list = ("name",)
    column_filters = ["name"]
    form_excluded_columns = (
        "roles",
        "roles_privileges",
        "created_at",
        "modified_at",
        "deleted",
    )

    form_args = {
        "name": {
            "validators": [
                DataRequired(message="Name is required."),
            ],
        }
    }

    def is_accessible(self):
        return super().is_accessible() and self.has_privilege(
            PrivilegeCodes.CAN_MANAGE_ACCESS_CONTROLS
        )

    def get_query(self):
        return super().get_query().filter_by(deleted=False)

    def get_count_query(self):
        return super().get_count_query().filter_by(deleted=False)


class RolesPrivilegesView(BaseAdminViewForNavisionModel):
    column_list = ("role.name", "privilege.name")
    column_labels = {
        "role.name": "Role",
        "privilege.name": "Privilege",
    }
    column_default_sort = ("role.name", True)
    column_searchable_list = ("role.name", "privilege.name")
    column_filters = ["role.name", "privilege.name"]

    form_args = {
        "role": {
            "query_factory": lambda: RolesModel.query.filter_by(deleted=False).order_by(
                RolesModel.name.asc()
            ),
            "get_label": "name",
            "validators": [DataRequired(message="Role is required.")],
        },
        "privilege": {
            "query_factory": lambda: PrivilegesModel.query.filter_by(
                deleted=False
            ).order_by(PrivilegesModel.name.asc()),
            "get_label": "name",
            "validators": [DataRequired(message="Role is required.")],
        },
    }

    def is_accessible(self):
        return super().is_accessible() and self.has_privilege(
            PrivilegeCodes.CAN_MANAGE_ACCESS_CONTROLS
        )
