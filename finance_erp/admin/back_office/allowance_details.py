from flask import current_app as app
from flask_admin import BaseView, expose
from flask_login import current_user
from werkzeug.utils import redirect
from wtforms import form

from finance_erp.admin.base.admin_view import BaseAdminViewForNavisionModel


class AllowanceForm(form.Form):
    pass


class AllowanceView(BaseAdminViewForNavisionModel):
    can_create = False
    can_edit = False
    can_delete = False
    column_list = (
        "allowance_id",
        "charge_split_id",
        "charge_id",
        "posting_date",
        "tax_amount",
        "posttax_amount",
        "pretax_amount",
        "tax_details",
        "charge_type",
        "bill_to_type",
        "item_id",
        "sku_category_id",
        "hotel_id",
        "revenue_center",
        "booking_id",
        "owner_name",
        "category",
        "folio_number",
        "billed_entity_id",
        "account_number",
        "booking_reference_number",
        "fin_erp_posting_date",
    )

    column_labels = {
        "allowance_id": "Allowance ID",
        "charge_split_id": "Charge Split ID",
        "charge_id": "Charge ID",
        "posting_date": "Posting Date",
        "tax_amount": "Tax Amount",
        "posttax_amount": "Post-tax Amount",
        "pretax_amount": "Pre-tax Amount",
        "tax_details": "Tax Details",
        "charge_type": "Charge Type",
        "bill_to_type": "Bill To Type",
        "item_id": "Item ID",
        "sku_category_id": "SKU Category ID",
        "hotel_id": "Hotel ID",
        "revenue_center": "Revenue Center",
        "booking_id": "Booking ID",
        "owner_name": "Owner Name",
        "category": "Category",
        "folio_number": "Folio Number",
        "billed_entity_id": "Billed Entity ID",
        "account_number": "Account Number",
        "booking_reference_number": "Booking Reference Number",
        "fin_erp_posting_date": "Finance ERP Posting Date",
    }

    form = AllowanceForm

    column_default_sort = ("fin_erp_posting_date", True)

    column_searchable_list = ("hotel_id", "bill_id", "booking_id")

    column_filters = [
        "hotel_id",
        "fin_erp_posting_date",
    ]


class RefreshAllowanceView(BaseView):
    def is_accessible(self):
        if app.config["ENV"] in ["local", "development"]:
            return True
        return current_user.is_authenticated

    def inaccessible_callback(self, name, **kwargs):
        return redirect("/erp/login")

    @expose("/", methods=("GET", "POST"))
    def create_view(self):
        return self.render("back_office/refresh_allowance.html")
