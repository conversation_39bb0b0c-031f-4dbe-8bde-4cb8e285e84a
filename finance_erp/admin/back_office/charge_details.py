from flask import current_app as app
from flask_admin import BaseView, expose
from flask_login import current_user
from werkzeug.utils import redirect
from wtforms import form

from finance_erp.admin.base.admin_view import BaseAdminViewForNavisionModel


class ChargeForm(form.Form):
    pass


class ChargeView(BaseAdminViewForNavisionModel):
    can_create = False
    can_edit = False
    can_delete = False
    column_list = (
        "charge_split_id",
        "charge_id",
        "pretax_amount",
        "tax_amount",
        "posttax_amount",
        "tax_details",
        "charge_type",
        "bill_to_type",
        "item_id",
        "sku_category_id",
        "applicable_business_date",
        "posting_date",
        "is_inclusion_charge",
        "owner_name",
        "category",
        "folio_number",
        "hotel_id",
        "revenue_center",
        "booking_id",
        "billed_entity_id",
        "account_number",
        "booking_reference_number",
        "fin_erp_posting_date",
    )

    column_labels = {
        "charge_split_id": "Charge Split ID",
        "charge_id": "Charge ID",
        "pretax_amount": "Pre-tax Amount",
        "tax_amount": "Tax Amount",
        "posttax_amount": "Post-tax Amount",
        "tax_details": "Tax Details",
        "charge_type": "Charge Type",
        "bill_to_type": "Bill To Type",
        "item_id": "Item ID",
        "sku_category_id": "SKU Category ID",
        "applicable_business_date": "Applicable Business Date",
        "posting_date": "Posting Date",
        "is_inclusion_charge": "Is Inclusion Charge",
        "owner_name": "Owner Name",
        "category": "Category",
        "folio_number": "Folio Number",
        "hotel_id": "Hotel ID",
        "revenue_center": "Revenue Center",
        "booking_id": "Booking ID",
        "billed_entity_id": "Billed Entity ID",
        "account_number": "Account Number",
        "booking_reference_number": "Booking Reference Number",
        "fin_erp_posting_date": "Finance ERP Posting Date",
    }

    form = ChargeForm

    column_default_sort = ("fin_erp_posting_date", True)

    column_searchable_list = ("hotel_id", "bill_id", "booking_id")

    column_filters = [
        "hotel_id",
        "fin_erp_posting_date",
    ]


class RefreshChargeView(BaseView):
    def is_accessible(self):
        if app.config["ENV"] in ["local", "development"]:
            return True
        return current_user.is_authenticated

    def inaccessible_callback(self, name, **kwargs):
        return redirect("/erp/login")

    @expose("/", methods=("GET", "POST"))
    def create_view(self):
        return self.render("back_office/refresh_charge.html")
