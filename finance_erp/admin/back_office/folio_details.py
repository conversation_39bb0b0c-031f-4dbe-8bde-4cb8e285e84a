from flask import current_app as app
from flask_admin import BaseView, expose
from flask_login import current_user
from werkzeug.utils import redirect
from wtforms import form

from finance_erp.admin.base.admin_view import BaseAdminViewForNavisionModel


class FolioDetailsForm(form.Form):
    pass


class FolioDetailsView(BaseAdminViewForNavisionModel):
    can_create = False
    can_edit = False
    can_delete = False

    column_list = (
        "hotel_id",
        "bill_id",
        "booking_id",
        "category",
        "owner_name",
        "folio_number",
        "billed_entity_id",
        "account_number",
        "booking_reference_number",
        "is_credit_folio",
        "payment_split_details",
        "folio_status",
        "fin_erp_posting_date",
    )

    column_labels = {
        "hotel_id": "Hotel ID",
        "bill_id": "Bill ID",
        "booking_id": "Booking ID",
        "category": "Category",
        "owner_name": "Owner Name",
        "folio_number": "Folio Number",
        "billed_entity_id": "Billed Entity ID",
        "account_number": "Account Number",
        "booking_reference_number": "Booking Reference Number",
        "is_credit_folio": "Is Credit Folio",
        "payment_split_details": "Payment Split Details",
        "folio_status": "Folio Status",
        "fin_erp_posting_date": "Finance ERP Posting Date",
    }

    form = FolioDetailsForm

    column_default_sort = ("fin_erp_posting_date", True)

    column_searchable_list = ("hotel_id", "bill_id", "booking_id")

    column_filters = [
        "hotel_id",
        "fin_erp_posting_date",
    ]


class RefreshFolioView(BaseView):
    def is_accessible(self):
        if app.config["ENV"] in ["local", "development"]:
            return True
        return current_user.is_authenticated

    def inaccessible_callback(self, name, **kwargs):
        return redirect("/erp/login")

    @expose("/", methods=("GET", "POST"))
    def create_view(self):
        return self.render("back_office/refresh_folio.html")
