from flask import current_app as app
from flask_admin import BaseView, expose
from flask_login import current_user
from werkzeug.utils import redirect
from wtforms import form

from finance_erp.admin.base.admin_view import BaseAdminViewForNavisionModel


class PaymentForm(form.Form):
    pass


class PaymentView(BaseAdminViewForNavisionModel):
    can_create = False
    can_edit = False
    can_delete = False
    column_list = (
        "payment_id",
        "payment_split_id",
        "payment_type",
        "amount",
        "posting_date",
        "date_of_payment",
        "payment_mode",
        "payment_mode_sub_type",
        "payment_ref_id",
        "payment_channel",
        "hotel_id",
        "revenue_center",
        "booking_id",
        "category",
        "owner_name",
        "folio_number",
        "debtor_code",
        "checkin_date",
        "checkout_date",
        "billed_entity_id",
        "account_number",
        "booking_reference_number",
        "fin_erp_posting_date",
    )

    column_labels = {
        "payment_id": "Payment ID",
        "payment_split_id": "Payment Split ID",
        "payment_type": "Payment Type",
        "amount": "Amount",
        "posting_date": "Posting Date",
        "date_of_payment": "Date of Payment",
        "payment_mode": "Payment Mode",
        "payment_mode_sub_type": "Payment Mode Sub Type",
        "payment_ref_id": "Payment Reference ID",
        "payment_channel": "Payment Channel",
        "hotel_id": "Hotel ID",
        "revenue_center": "Revenue Center",
        "booking_id": "Booking ID",
        "category": "Category",
        "owner_name": "Owner Name",
        "folio_number": "Folio Number",
        "debtor_code": "Debtor Code",
        "checkin_date": "Check-in Date",
        "checkout_date": "Check-out Date",
        "billed_entity_id": "Billed Entity ID",
        "account_number": "Account Number",
        "booking_reference_number": "Booking Reference Number",
        "fin_erp_posting_date": "Finance ERP Posting Date",
    }

    form = PaymentForm

    column_default_sort = ("fin_erp_posting_date", True)

    column_searchable_list = ("hotel_id", "bill_id", "booking_id")

    column_filters = [
        "hotel_id",
        "fin_erp_posting_date",
    ]


class RefreshPaymentView(BaseView):
    def is_accessible(self):
        if app.config["ENV"] in ["local", "development"]:
            return True
        return current_user.is_authenticated

    def inaccessible_callback(self, name, **kwargs):
        return redirect("/erp/login")

    @expose("/", methods=("GET", "POST"))
    def create_view(self):
        return self.render("back_office/refresh_payment.html")
