from flask import current_app as app
from flask_admin import BaseView, expose
from flask_login import current_user
from werkzeug.utils import redirect
from wtforms import form

from core.common.constants import PrivilegeCodes
from finance_erp.admin.base.admin_view import BaseAdminViewForNavisionModel
from finance_erp.domain.auth.models import UserGroupModel


class TransactionMasterItemForm(form.Form):
    pass


class TransactionMasterItemView(BaseAdminViewForNavisionModel):
    can_create = False
    can_edit = False
    can_delete = False
    column_list = (
        "transaction_id",
        "hotel_id",
        "erp_name",
        "identifier_name",
        "display_name",
        "gl_code",
        "is_active",
        "merge_gl_entries",
        "particulars",
        "revenue_center",
        "identifier",
        "transaction_type",
        "transaction_metadata",
        "created_at",
        "modified_at",
    )

    column_labels = {
        "transaction_id": "Transaction Id",
        "hotel_id": "Hotel ID",
        "erp_name": "ERP Name",
        "identifier_name": "Identifier Name",
        "display_name": "Display Name",
        "gl_code": "GL Code",
        "is_active": "Is Active",
        "merge_gl_entries": "Merge GLs",
        "particulars": "Particulars",
        "revenue_center": "Revenue Center",
        "identifier": "Identifier",
        "transaction_type": "Transaction Type",
        "transaction_metadata": "Transaction MetaData",
        "created_at": "Created At",
        "modified_at": "Modified At",
    }

    form = TransactionMasterItemForm

    column_default_sort = ("modified_at", True)

    column_searchable_list = ("hotel_id", "gl_code", "identifier")

    column_filters = [
        "hotel_id",
        "gl_code",
        "identifier",
    ]


class RefreshTransactionMasterItemView(BaseView):
    def is_accessible(self):
        if app.config["ENV"] in ["local", "development"]:
            return True
        if not current_user.is_authenticated:
            return False
        return self.has_privilege(PrivilegeCodes.CAN_PULL_DATA_FROM_SOURCES)

    def has_privilege(self, privilege_name):
        user_email = current_user.email
        user_group = UserGroupModel.query.filter_by(email=user_email).first()

        if not user_group or not user_group.role:
            return False

        for privilege in user_group.role.privileges:
            if privilege.name == privilege_name:
                return True

        return False

    def inaccessible_callback(self, name, **kwargs):
        return redirect("/erp/login")

    @expose("/", methods=("GET", "POST"))
    def create_view(self):
        user_email = current_user.email
        user_group = UserGroupModel.query.filter_by(email=user_email).first()
        user_info = {
            "user_id": user_group.id,
            "user_name": user_email,
            "role": user_group.role.name,
        }
        return self.render(
            "back_office/refresh_transaction_master.html", user_info=user_info
        )
