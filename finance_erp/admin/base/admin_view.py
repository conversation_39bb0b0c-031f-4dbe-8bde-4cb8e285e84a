from flask import current_app as app
from flask import redirect
from flask_login import current_user

from core.common.constants import PrivilegeCodes
from finance_erp.audit_extension import AuditedModelView
from finance_erp.domain.auth.models import UserGroupModel


class BaseAdminViewForNavisionModel(AuditedModelView):
    list_template = "base_list.html"
    can_export = True
    search_case_insensitive = False
    column_display_pk = True
    form_excluded_columns = ("password", "created_at", "modified_at", "deleted")

    def is_accessible(self):
        if app.config["ENV"] in ["local", "development"]:
            return True
        return current_user.is_authenticated

    def has_privilege(self, privilege_name):
        if app.config["ENV"] in ["local", "development"]:
            return True
        user_email = current_user.get_id()
        user_group = UserGroupModel.query.filter_by(email=user_email).first()

        if not user_group or not user_group.role:
            return False

        for privilege in user_group.role.privileges:
            if privilege.name == privilege_name:
                return True

        return False

    def get_actions_list(self):
        actions_list, actions_descriptions = super().get_actions_list()
        if not self.has_privilege(PrivilegeCodes.CAN_PUSH_DATA_TO_BIZC):
            actions_list = [action for action in actions_list if action[0] != "push"]
            actions_descriptions.pop("push", None)

        if not self.has_privilege(PrivilegeCodes.CAN_PULL_DATA_FROM_SOURCES):
            actions_list = [action for action in actions_list if action[0] != "pull"]
            actions_descriptions.pop("pull", None)

        return actions_list, actions_descriptions

    def inaccessible_callback(self, name, **kwargs):
        return redirect("/erp/login")

    def get_row_high_light_class(self, row):
        return (
            "success"
            if getattr(row, "pushed", getattr(row, "status", "N/A") == "pushed")
            else "danger"
            if getattr(row, "last_push_attempt_at", False)
            else "warning"
            if getattr(row, "erp_remarks", False)
            else "active"
        )

    def render(self, template, **kwargs):
        kwargs["get_row_high_light_class"] = self.get_row_high_light_class
        return super().render(template, **kwargs)

    def on_form_prefill(self, form, id):
        """
        This method can be overridden to prefill form fields in edit mode.
        Here we prefill `role_id` and `privilege_id` for the `UserGroupForm` and `RolesPrivilegesForm`.
        """
        if id:
            model_obj = self.session.query(self.model).get(id)
            if model_obj:
                for field in form:
                    if hasattr(model_obj, field.name):
                        # Assign model's attribute to form field if it exists
                        field.data = getattr(model_obj, field.name)
