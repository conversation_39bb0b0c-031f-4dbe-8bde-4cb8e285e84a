from flask import current_app as app
from flask import redirect
from flask_admin import BaseView, expose
from flask_login import current_user

from core.common.constants import PrivilegeCodes
from finance_erp.domain.auth.models import UserGroupModel


class InvoiceDownloadView(BaseView):
    def is_accessible(self):
        if app.config["ENV"] in ["local", "development"]:
            return True
        if not current_user.is_authenticated:
            return False
        return self.has_privilege(PrivilegeCodes.CAN_DOWNLOAD_INVOICES_AND_CREDIT_NOTES)

    def has_privilege(self, privilege_name):
        user_email = current_user.email
        user_group = UserGroupModel.query.filter_by(email=user_email).first()

        if not user_group or not user_group.role:
            return False

        for privilege in user_group.role.privileges:
            if privilege.name == privilege_name:
                return True

        return False

    def inaccessible_callback(self, name, **kwargs):
        return redirect("/erp/login")

    @staticmethod
    def get_admin_email():
        if app.config["ENV"] in ["local", "development"]:
            return "<EMAIL>"
        return current_user.email

    @expose("/", methods=("GET", "POST"))
    def create_view(self, **kwargs):
        admin_email = self.get_admin_email()
        return self.render(
            "invoice/invoice_download_filter.html", admin_email=admin_email
        )
