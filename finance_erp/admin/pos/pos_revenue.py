from wtforms import form

from finance_erp.admin.base.admin_view import BaseAdminViewForNavisionModel


class POSRevenueItemForm(form.Form):
    pass


class POSRevenueItemView(BaseAdminViewForNavisionModel):
    can_create = False
    can_edit = False
    can_delete = False
    column_list = (
        "uu_id",
        "hotel_id",
        "interface_id",
        "interface_name",
        "bill_id",
        "reservation_id",
        "guest_name",
        "amount",
        "tax",
        "sku_category",
        "hsn_code",
        "payment_method",
        "pos_bill_date",
        "pos_bill_time",
        "revenue_center",
        "serving_time",
        "workstation_id",
        "waiter_id",
        "created_at",
        "modified_at",
    )

    column_labels = {
        "uu_id": "UUID",
        "hotel_id": "Hotel ID",
        "interface_id": "Interface ID",
        "interface_name": "Interface Name",
        "bill_id": "Bill ID",
        "reservation_id": "Reservation ID",
        "guest_name": "Guest Name",
        "amount": "Amount",
        "tax": "Tax",
        "sku_category": "SKU Category",
        "hsn_code": "HSN Code",
        "payment_method": "Payment Method",
        "pos_bill_date": "POS Bill Date",
        "pos_bill_time": "POS Bill Time",
        "verified": "Verified",
        "revenue_center": "Revenue Center",
        "serving_time": "Serving Time",
        "workstation_id": "Workstation ID",
        "waiter_id": "Waiter ID",
        "created_at": "Created At",
        "modified_at": "Modified At",
    }

    form = POSRevenueItemForm

    column_default_sort = ("modified_at", True)

    column_searchable_list = ("uu_id", "bill_id", "reservation_id")

    column_filters = [
        "hotel_id",
        "interface_id",
        "bill_id",
        "reservation_id",
        "pos_bill_date",
    ]
