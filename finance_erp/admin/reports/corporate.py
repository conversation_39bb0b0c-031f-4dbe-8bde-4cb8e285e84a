from flask import current_app as app
from flask import flash
from flask_admin import BaseView, expose
from flask_admin.actions import action
from flask_admin.babel import gettext, ngettext
from flask_login import current_user
from werkzeug.utils import redirect
from wtforms import form

from core.common.constants import PrivilegeCodes
from finance_erp.admin.base.admin_view import BaseAdminViewForNavisionModel
from finance_erp.application.corporate.command_handler.push_corporate_report import (
    BulkPushCorporateReportCommandHandler,
)
from finance_erp.application.corporate.dtos.bulk_corporate_push_request_dto import (
    BulkCorporatePushRequestDto,
)
from finance_erp.audit_extension import audit_ext
from finance_erp.common.constants import NavisionReports
from finance_erp.domain.auth.models import UserGroupModel
from object_registry import locate_instance


class CorporateReportForm(form.Form):
    pass


class CorporateView(BaseAdminViewForNavisionModel):
    can_create = False
    can_edit = False
    can_delete = False
    column_list = (
        "customer_legal_name",
        "corporate_code",
        "last_push_attempt_at",
        "erp_remarks",
        "status",
        "customer_trading_name",
        "address",
        "city",
        "phone_number",
        "post_code",
        "email",
        "credit_limit",
        "country_code",
        "pan",
        "state_code",
        "gstin",
        "gst_customer_type",
        "tan_number",
        "created_at",
        "modified_at",
    )

    column_labels = {
        "customer_legal_name": "LegalName",
        "customer_trading_name": "TradingName",
        "address": "Address",
        "city": "City",
        "phone_number": "PhoneNumber",
        "post_code": "PostCode",
        "email": "Email",
        "credit_limit": "CreditLimit",
        "country_code": "CountryCode",
        "pan": "PANNo",
        "state_code": "State",
        "gstin": "GSTRegistrationNo",
        "gst_customer_type": "GSTCustomerType",
        "corporate_code": "AthenaCode",
        "tan_number": "TANNo",
    }

    form = CorporateReportForm

    column_default_sort = ("modified_at", True)

    column_searchable_list = ("corporate_code",)

    column_filters = ["status", "corporate_code"]

    @audit_ext.manager.capture_trail(user_action="PushCorporateDataBizC")
    @action("push", "Push", "Are you sure you want to push items?")
    def action_push(self, ids):
        try:
            flash(gettext("Data push in progress.."))
            handler: BulkPushCorporateReportCommandHandler = locate_instance(
                BulkPushCorporateReportCommandHandler
            )
            request_dto = BulkCorporatePushRequestDto(corporate_codes=ids)
            handler.handle(request_dto, NavisionReports.CORPORATE_REPORT)
            flash(gettext("Data push completed"))
        except Exception as ex:
            if not self.handle_view_exception(ex):
                raise
            flash(gettext("Failed to approve users. %(error)s", error=str(ex)), "error")


class IngestCorporatesView(BaseView):
    def is_accessible(self):
        if app.config["ENV"] in ["local", "development"]:
            return True
        if not current_user.is_authenticated:
            return False
        return self.has_privilege(PrivilegeCodes.CAN_PULL_DATA_FROM_SOURCES)

    def has_privilege(self, privilege_name):
        user_email = current_user.email
        user_group = UserGroupModel.query.filter_by(email=user_email).first()

        if not user_group or not user_group.role:
            return False

        for privilege in user_group.role.privileges:
            if privilege.name == privilege_name:
                return True

        return False

    def inaccessible_callback(self, name, **kwargs):
        return redirect("/erp/login")

    @expose("/", methods=("GET", "POST"))
    def create_view(self):
        user_email = current_user.email
        user_group = UserGroupModel.query.filter_by(email=user_email).first()
        user_info = {
            "user_id": user_group.id,
            "user_name": user_email,
            "role": user_group.role.name,
        }
        return self.render("corporate/ingest_corporates.html", user_info=user_info)
