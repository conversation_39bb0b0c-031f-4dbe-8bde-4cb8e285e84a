from flask import current_app as app
from flask import flash
from flask_admin import BaseView, expose
from flask_admin.actions import action
from flask_admin.babel import gettext
from flask_login import current_user
from werkzeug.utils import redirect
from wtforms import form

from core.common.constants import PrivilegeCodes
from finance_erp.admin.base.admin_view import BaseAdminViewForNavisionModel
from finance_erp.application.hotel.command_handler.push_hotel_report import (
    BulkPushHotelReportCommandHandler,
)
from finance_erp.application.hotel.dtos.bulk_hotel_push_request_dto import (
    BulkHotelPushRequestDto,
)
from finance_erp.audit_extension import audit_ext
from finance_erp.common.constants import NavisionReports
from finance_erp.domain.auth.models import UserGroupModel
from object_registry import locate_instance


class HotelReportForm(form.Form):
    pass


class HotelView(BaseAdminViewForNavisionModel):
    can_create = False
    can_edit = False
    can_delete = False
    column_list = (
        "vendor_name",
        "hotel_code",
        "last_push_attempt_at",
        "erp_remarks",
        "status",
        "search_name",
        "address",
        "city",
        "country_code",
        "pan",
        "state_code",
        "gstin",
        "gst_vendor_type",
        "msme",
        "cost_center_id",
        "created_at",
        "modified_at",
    )

    column_labels = {
        "vendor_name": "LegalName",
        "search_name": "Name",
        "address": "Address",
        "city": "City",
        "country_code": "CountryCode",
        "pan": "PANNo",
        "state_code": "State",
        "gstin": "GSTRegistrationNo",
        "gst_vendor_type": "GSTVendorType",
        "hotel_code": "HotelCode",
        "msme": "MSME",
        "cost_center_id": "CostCenterID",
    }

    form = HotelReportForm

    column_default_sort = ("modified_at", True)

    column_searchable_list = ("hotel_code",)

    column_filters = ["created_at", "status"]

    @audit_ext.manager.capture_trail(user_action="PushHotelDataBizC")
    @action("push", "Push", "Are you sure you want to push items?")
    def action_push(self, ids):
        try:
            flash(gettext("Data push in progress.."))
            handler: BulkPushHotelReportCommandHandler = locate_instance(
                BulkPushHotelReportCommandHandler
            )
            request_dto = BulkHotelPushRequestDto(hotel_codes=ids)
            handler.handle(request_dto, NavisionReports.HOTEL_REPORT)
            flash(gettext("Data push completed"))
        except Exception as ex:
            if not self.handle_view_exception(ex):
                raise
            flash(gettext("Failed to approve users. %(error)s", error=str(ex)), "error")


class IngestHotelsView(BaseView):
    def is_accessible(self):
        if app.config["ENV"] in ["local", "development"]:
            return True
        if not current_user.is_authenticated:
            return False
        return self.has_privilege(PrivilegeCodes.CAN_PULL_DATA_FROM_SOURCES)

    def has_privilege(self, privilege_name):
        user_email = current_user.email
        user_group = UserGroupModel.query.filter_by(email=user_email).first()

        if not user_group or not user_group.role:
            return False

        for privilege in user_group.role.privileges:
            if privilege.name == privilege_name:
                return True

        return False

    def inaccessible_callback(self, name, **kwargs):
        return redirect("/erp/login")

    @expose("/", methods=("GET", "POST"))
    def create_view(self):
        user_email = current_user.email
        user_group = UserGroupModel.query.filter_by(email=user_email).first()
        user_info = {
            "user_id": user_group.id,
            "user_name": user_email,
            "role": user_group.role.name,
        }
        return self.render("hotel/ingest_hotels.html", user_info=user_info)
