from flask_admin.actions import action
from wtforms import form

from finance_erp.admin.base.admin_view import BaseAdminViewForNavisionModel


class OtaCommissionReportForm(form.Form):
    pass


class OtaCommissionReportView(BaseAdminViewForNavisionModel):
    can_create = False
    can_edit = False
    can_delete = False
    column_list = (
        "booking_created_date",
        "reference_number",
        "last_push_attempt_at",
        "erp_remarks",
        "status",
        "paid_at_ota",
        "commission_amount",
        "tds_percentage",
        "hotel_code",
        "check_in",
        "check_out",
        "pretax_room_rent",
        "guest_name",
        "ota_name",
        "commission_percent",
        "mop",
        "posting_date",
        "created_at",
        "modified_at",
    )

    column_labels = {
        "posting_date": "PostingDate",
        "paid_at_ota": "GBV",
        "commission_amount": "CommissionAmount",
        "tds_percentage": "TDSPer",
        "reference_number": "BookingID",
        "hotel_code": "Hotelcode",
        "check_in": "CheckInDate",
        "check_out": "CheckOutDate",
        "pretax_room_rent": "NBV",
        "guest_name": "GuestName",
        "ota_name": "OTAName",
        "commission_percent": "CommissionPer",
        "mop": "MOP",
        "booking_created_date": "BookingCreationDate",
    }

    form = OtaCommissionReportForm

    column_default_sort = ("modified_at", True)

    column_searchable_list = ("reference_number",)

    column_filters = ["booking_created_date", "status"]

    @action("push", "Push", "Are you sure you want to push items?")
    def action_push(self, ids):
        print(ids)
