from flask_admin.actions import action
from wtforms import form

from finance_erp.admin.base.admin_view import BaseAdminViewForNavisionModel


class PGPaymentReportForm(form.Form):
    pass


class PGPaymentView(BaseAdminViewForNavisionModel):
    can_create = False
    can_edit = False
    can_delete = False
    column_list = (
        "hotel_code",
        "posting_date",
        "payment_date",
        "pg_transaction_id",
        "reference_number",
        "pg_charges",
        "pg_tax",
        "platform_fees",
        "paid_by",
        "paid_to",
        "payment_type",
        "payment_amount",
        "paymode",
        "paymode_type",
        "payor_entity",
        "booker_entity",
        "booking_owner",
        "athena_code",
        "payor_name",
        "hotel_name",
        "invoice_id",
        "check_in",
        "check_out",
        "channel",
        "sub_channel",
        "original_booking_amount",
        "is_advance",
        "refund_reason",
        "uu_id",
        "aggregation_id",
        "aggregated_at",
        "last_push_attempt_at",
        "erp_remarks",
        "status",
        "created_at",
        "modified_at",
    )

    column_labels = {
        "posting_date": "PostingDate",
        "payment_date": "PaymentDate",
        "pg_charges": "PGCharges",
        "payment_amount": "PaymentAmount",
        "pg_transaction_id": "PaymentGatewayTransactionId",
        "reference_number": "BookingID",
        "hotel_code": "HotelCode",
        "hotel_name": "HotelName",
        "pg_tax": "PGTaxes",
        "platform_fees": "PlatformFees",
        "paid_by": "PaidBy",
        "paid_to": "PaidTo",
        "payment_type": "PaidType",
        "paymode": "Paymode",
        "paymode_type": "PaymodeType",
        "payor_entity": "PayorEntity",
        "booker_entity": "BookerEntity",
        "booking_owner": "BookingOwner",
        "athena_code": "AthenaCode",
        "payor_name": "PayorName",
        "invoice_id": "InvoiceId",
        "check_in": "CheckinDate",
        "check_out": "CheckoutDate",
        "channel": "Channel",
        "sub_channel": "SubChannel",
        "original_booking_amount": "OriginalBookingAmount",
        "is_advance": "IsAdvance",
        "refund_reason": "RefundReason",
        "uu_id": "UUID",
        "aggregation_id": "AggregationId",
    }

    form = PGPaymentReportForm

    column_default_sort = ("modified_at", True)

    column_searchable_list = ("pg_transaction_id",)

    column_filters = [
        "posting_date",
        "payment_date",
        "status",
        "reference_number",
        "hotel_code",
        "uu_id",
        "aggregation_id",
    ]

    @action("push", "Push", "Are you sure you want to push items?")
    def action_push(self, ids):
        print(ids)
