from flask import flash
from flask_admin.actions import action
from flask_admin.babel import gettext
from sqlalchemy.orm import Query
from wtforms import form

from finance_erp.admin.base.admin_view import BaseAdminViewForNavisionModel
from finance_erp.application.payments.commands.resync_payment_data import (
    ResyncPaymentDataHandler,
)
from finance_erp.audit_extension import audit_ext
from finance_erp.common.constants import MAX_NUMBER_OF_RECORDS_THAT_CAN_BE_RE_SYNCED
from finance_erp.common.decorators import session_manager
from finance_erp.common.exception import (
    ARServiceException,
    CRSServiceException,
    DataAlreadyPushedException,
    PaymentAggregationException,
    PaymentDataUpdateException,
)
from object_registry import locate_instance


class PGPaymentSummaryForm(form.Form):
    pass


class PGPaymentSummaryView(BaseAdminViewForNavisionModel):
    can_create = False
    can_edit = False
    can_delete = False
    column_list = (
        "hotel_code",
        "posting_date",
        "payment_date",
        "pg_transaction_id",
        "reference_number",
        "pg_charges",
        "pg_tax",
        "platform_fees",
        "paid_by",
        "paid_to",
        "payment_type",
        "payment_amount",
        "paymode",
        "paymode_type",
        "payor_entity",
        "booker_entity",
        "booking_owner",
        "athena_code",
        "payor_name",
        "hotel_name",
        "invoice_id",
        "check_in",
        "check_out",
        "channel",
        "sub_channel",
        "refund_reason",
        "original_booking_amount",
        "is_advance",
        "uu_id",
        "last_push_attempt_at",
        "erp_remarks",
        "status",
        "created_at",
        "modified_at",
    )

    column_labels = {
        "posting_date": "PostingDate",
        "payment_date": "PaymentDate",
        "pg_charges": "PGCharges",
        "payment_amount": "PaymentAmount",
        "pg_transaction_id": "PaymentGatewayTransactionId",
        "reference_number": "BookingID",
        "hotel_code": "HotelCode",
        "hotel_name": "HotelName",
        "pg_tax": "PGTaxes",
        "platform_fees": "PlatformFees",
        "paid_by": "PaidBy",
        "paid_to": "PaidTo",
        "payment_type": "PaidType",
        "paymode": "Paymode",
        "paymode_type": "PaymodeType",
        "payor_entity": "PayorEntity",
        "booker_entity": "BookerEntity",
        "booking_owner": "BookingOwner",
        "athena_code": "AthenaCode",
        "payor_name": "PayorName",
        "invoice_id": "InvoiceId",
        "check_in": "CheckinDate",
        "check_out": "CheckoutDate",
        "channel": "Channel",
        "sub_channel": "SubChannel",
        "refund_reason": "RefundReason",
        "original_booking_amount": "OriginalBookingAmount",
        "is_advance": "IsAdvance",
        "uu_id": "External Document No",
    }

    form = PGPaymentSummaryForm

    column_default_sort = ("modified_at", True)

    column_searchable_list = ("uu_id",)

    column_filters = [
        "posting_date",
        "payment_date",
        "status",
        "reference_number",
        "hotel_code",
        "uu_id",
    ]

    def get_query(self) -> Query:
        return super().get_query().filter_by(deleted=False)

    def get_count_query(self) -> Query:
        return super().get_count_query().filter_by(deleted=False)

    @action("push", "Push", "Are you sure you want to push items?")
    def action_push(self, ids):
        print(ids)

    @audit_ext.manager.capture_trail(user_action="PullPaymentData")
    @session_manager(commit=True)
    @action("pull", "Resync", "Are you sure you want to re-sync items?")
    def action_pull(self, ids):
        try:
            if len(ids) > MAX_NUMBER_OF_RECORDS_THAT_CAN_BE_RE_SYNCED:
                flash(
                    f"Error: Please select records less than {MAX_NUMBER_OF_RECORDS_THAT_CAN_BE_RE_SYNCED}",
                    "error",
                )
                return
            flash(gettext("Data Re sync in progress.."))
            resync_payment_data_handler = locate_instance(ResyncPaymentDataHandler)
            resync_payment_data_handler.handle(ids)
            flash(
                gettext(
                    "Payment data Re synced. New summary has been generated for the selected records"
                )
            )
        except Exception as ex:
            flash(gettext("Failed to resync data. %(error)s", error=str(ex)), "error")
