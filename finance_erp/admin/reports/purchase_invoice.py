from flask import flash
from flask_admin.actions import action
from flask_admin.babel import gettext
from wtforms import form

from finance_erp.admin.base.admin_view import BaseAdminViewForNavisionModel
from finance_erp.application.invoice.command_handler.resync_purchase_report_data import (
    ResyncPurchaseDataHandler,
)
from finance_erp.audit_extension import audit_ext
from finance_erp.common.constants import MAX_NUMBER_OF_RECORDS_THAT_CAN_BE_RE_SYNCED
from finance_erp.common.decorators import session_manager
from object_registry import locate_instance


class PurchaseReportForm(form.Form):
    pass


class PurchaseInvoiceView(BaseAdminViewForNavisionModel):
    can_create = False
    can_edit = False
    can_delete = False
    column_list = (
        "entry_type",
        "posting_date",
        "unique_ref_id",
        "invoice_number",
        "last_push_attempt_at",
        "erp_remarks",
        "purchase_type",
        "status",
        "unit_price",
        "tax_percentage",
        "customer_invoice_number",
        "reference_number",
        "hotel_name",
        "check_in",
        "check_out",
        "stay_days",
        "room_type",
        "occupancy",
        "guest_name",
        "uvid_date",
        "total_invoice_amount",
        "hotel_code",
        "order_date",
        "state_code",
        "source",
        "sub_source",
        "vendor_number",
        "due_date",
        "remark",
        "hsn_code",
        "original_invoice_number",
        "source_created_on",
        "structure",
        "nature_of_supply",
        "gst_vendor_type",
        "created_at",
        "modified_at",
    )

    column_labels = {
        "vendor_number": "Vendor No",
        "due_date": "Due Date",
        "source_created_on": "Source Created On",
        "gst_vendor_type": "GST Vendor Type",
        "erp_remarks": "erp_remarks",
        "purchase_type": "Purchase Type",
        "hsn_code": "HSN/SAC Code",
        "entry_type": "Entry Type",
        "order_date": "Order Date",
        "posting_date": "Posting Date",
        "reference_number": "Booking Reference No",
        "state_code": "State Code",
        "structure": "Structure",
        "nature_of_supply": "Nature of Supply",
        "unit_price": "Unit Price",
        "tax_percentage": "GST Group Code",
        "hotel_name": "Hotel Name",
        "check_in": "Check-In",
        "check_out": "Check-Out",
        "stay_days": "Total Days",
        "room_type": "Room Type",
        "occupancy": "No of Pax",
        "guest_name": "Guest Name",
        "uvid_date": "UVID Date",
        "invoice_number": "UVID No",
        "total_invoice_amount": "UV Amount",
        "hotel_code": "Hotel Code",
        "unique_ref_id": "Transaction Ref ID",
        "source": "Source",
        "sub_source": "SubSource",
        "original_invoice_number": "Original Inv No",
        "customer_invoice_number": "Sales Inv No",
    }

    form = PurchaseReportForm

    column_default_sort = ("posting_date", True)

    column_searchable_list = ("invoice_number", "customer_invoice_number")

    column_filters = [
        "invoice_number",
        "posting_date",
        "status",
        "reference_number",
        "customer_invoice_number",
        "hotel_code",
    ]

    @action("push", "Push", "Are you sure you want to push items?")
    def action_push(self, ids):
        print(ids)

    @audit_ext.manager.capture_trail(user_action="PullPurchaseData")
    @session_manager(commit=True)
    @action("pull", "Resync", "Are you sure you want to re-sync items?")
    def action_pull(self, ids):
        try:
            sanitized_ids = [id.replace("..", ".").strip() for id in ids if id]
            print(f"Received IDs: {ids}")
            if len(ids) > MAX_NUMBER_OF_RECORDS_THAT_CAN_BE_RE_SYNCED:
                flash(
                    f"Error: Please select records less than {MAX_NUMBER_OF_RECORDS_THAT_CAN_BE_RE_SYNCED}",
                    "error",
                )
                return
            flash(gettext("Data Re sync in progress.."))
            resync_purchase_data_handler = locate_instance(ResyncPurchaseDataHandler)
            resync_purchase_data_handler.handle(sanitized_ids)
            flash(gettext("Purchase data resynced"))

        except Exception as ex:
            flash(
                gettext("Failed to resync purchase data. %(error)s", error=str(ex)),
                "error",
            )
