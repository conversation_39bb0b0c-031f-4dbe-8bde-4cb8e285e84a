from flask_admin.actions import action
from wtforms import form

from finance_erp.admin.base.admin_view import BaseAdminViewForNavisionModel


class SalesReportForm(form.Form):
    pass


class SalesInvoiceView(BaseAdminViewForNavisionModel):
    can_create = False
    can_edit = False
    can_delete = False
    column_list = (
        "entry_type",
        "invoice_number",
        "posting_date",
        "unique_ref_id",
        "aggregation_id",
        "last_push_attempt_at",
        "erp_remarks",
        "status",
        "unit_price",
        "tax_percentage",
        "tax_type",
        "cgst",
        "sgst",
        "igst",
        "buy_side_invoice_number",
        "uvid_date",
        "hotel_name",
        "check_in",
        "check_out",
        "stay_days",
        "room_type",
        "occupancy",
        "guest_name",
        "remarks",
        "hsn_code",
        "customer_number",
        "structure",
        "nature_of_supply",
        "order_date",
        "state_code",
        "reference_number",
        "total_invoice_amount",
        "hotel_code",
        "source",
        "sub_source",
        "gst_customer_type",
        "invoice_charge_type",
        "billed_to_legal_name",
        "billed_to_state_code",
        "billed_to_gstin",
        "source_created_on",
        "original_invoice_number",
        "created_at",
        "modified_at",
        "aggregated_at",
        "booking_owner_legal_entity_id",
    )

    column_labels = {
        "entry_type": "EntryType",
        "order_date": "OrderDate",
        "posting_date": "PostingDate",
        "reference_number": "BookingRefNo",
        "state_code": "State",
        "structure": "Structure",
        "nature_of_supply": "NatureofSupply",
        "unit_price": "UnitPrice",
        "tax_percentage": "GSTGroupCode",
        "tax_type": "TaxType",
        "cgst": "cgst",
        "sgst": "sgst",
        "igst": "igst",
        "hotel_name": "HotelName",
        "check_in": "Check-In",
        "check_out": "Check-Out",
        "stay_days": "TotalDays",
        "room_type": "RoomType",
        "occupancy": "NoofPax",
        "guest_name": "GuestName",
        "uvid_date": "UVIDDate",
        "invoice_number": "UVIDNo",
        "total_invoice_amount": "UVAmount",
        "hotel_code": "Hotelcode",
        "unique_ref_id": "TransactionRefId",
        "aggregation_id": "AggregationId",
        "source": "Source",
        "sub_source": "SubSource",
        "customer_number": "CustomerNo",
        "gst_customer_type": "GSTCustomerType",
        "billed_to_state_code": "GSTBilltoStateCode",
        "billed_to_gstin": "CustGSTRegNo",
        "remarks": "Remarks",
        "billed_to_legal_name": "CustomerName",
        "hsn_code": "HSN_SACCode",
        "original_invoice_number": "OriginalInvoiceNo",
        "invoice_charge_type": "Type",
        "buy_side_invoice_number": "Buy Side Invoice Number",
        "booking_owner_legal_entity_id": "Booking Owner Legal Entity Id",
    }

    form = SalesReportForm

    column_default_sort = ("posting_date", True)

    column_searchable_list = ("invoice_number",)

    column_filters = [
        "invoice_number",
        "posting_date",
        "status",
        "buy_side_invoice_number",
        "customer_number",
        "hotel_code",
        "reference_number",
        "aggregation_id",
    ]

    @action("push", "Push", "Are you sure you want to push items?")
    def action_push(self, ids):
        print(ids)
