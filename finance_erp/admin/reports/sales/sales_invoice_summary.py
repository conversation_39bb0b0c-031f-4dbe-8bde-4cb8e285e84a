from flask import flash
from flask_admin.actions import action
from flask_admin.babel import gettext
from sqlalchemy.orm import Query
from wtforms import form

from finance_erp.admin.base.admin_view import BaseAdminViewForNavisionModel
from finance_erp.application.invoice.command_handler.bulk_update_sales_record import (
    BulkUpdateSalesReportCommandHandler,
)
from finance_erp.application.invoice.command_handler.resync_sales_report_data import (
    ResyncSalesDataHandler,
)
from finance_erp.audit_extension import audit_ext
from finance_erp.common.constants import (
    MAX_NUMBER_OF_RECORDS_THAT_CAN_BE_RE_SYNCED,
    NavisionReports,
)
from finance_erp.common.decorators import session_manager
from finance_erp.common.exception import (
    CRSServiceException,
    DataAlreadyPushedException,
    SalesDataAggregationException,
    SalesDataUpdateException,
)
from finance_erp.common.schema.sales import SalesInvoiceIngestSchema
from finance_erp.domain.reseller.models import SalesInvoiceStatus
from finance_erp.domain.reseller.repository.sales_invoice_repository import (
    SalesInvoiceRepository,
)
from finance_erp.domain.reseller.repository.sales_summary_repository import (
    SalesInvoiceSummaryRepository,
)
from finance_erp.infrastructure.external_clients.crs_service_client import (
    CrsServiceClient,
)
from object_registry import locate_instance


class SalesSummaryForm(form.Form):
    pass


class SalesInvoiceSummaryView(BaseAdminViewForNavisionModel):
    can_create = False
    can_edit = False
    can_delete = False
    column_list = (
        "entry_type",
        "posting_date",
        "unique_ref_id",
        "last_push_attempt_at",
        "erp_remarks",
        "status",
        "unit_price",
        "tax_percentage",
        "tax_type",
        "cgst",
        "sgst",
        "igst",
        "uvid_date",
        "hsn_code",
        "order_date",
        "created_at",
        "modified_at",
    )

    column_labels = {
        "entry_type": "EntryType",
        "order_date": "OrderDate",
        "posting_date": "PostingDate",
        "unit_price": "UnitPrice",
        "tax_percentage": "GSTGroupCode",
        "tax_type": "TaxType",
        "cgst": "cgst",
        "sgst": "sgst",
        "igst": "igst",
        "uvid_date": "UVIDDate",
        "unique_ref_id": "TransactionRefId",
        "hsn_code": "HSN_SACCode",
    }

    form = SalesSummaryForm

    column_default_sort = ("posting_date", True)

    column_searchable_list = ("unique_ref_id",)

    column_filters = [
        "posting_date",
        "status",
    ]

    def get_query(self) -> Query:
        return super().get_query().filter_by(deleted=False)

    def get_count_query(self) -> Query:
        return super().get_count_query().filter_by(deleted=False)

    @action("push", "Push", "Are you sure you want to push items?")
    def action_push(self, ids):
        print(ids)

    @audit_ext.manager.capture_trail(user_action="PullSalesData")
    @session_manager(commit=True)
    @action("pull", "Resync", "Are you sure you want to re-sync items?")
    def action_pull(self, ids):
        try:
            if len(ids) > MAX_NUMBER_OF_RECORDS_THAT_CAN_BE_RE_SYNCED:
                flash(
                    f"Error: Please select records less than {MAX_NUMBER_OF_RECORDS_THAT_CAN_BE_RE_SYNCED}",
                    "error",
                )
                return
            flash(gettext("Data Re sync in progress.."))
            resync_sales_data_handler = locate_instance(ResyncSalesDataHandler)
            resync_sales_data_handler.handle(ids)
            flash(
                gettext(
                    "Sales data Re synced. New summary has been generated for the selected records"
                )
            )
        except Exception as ex:
            flash(
                gettext("Failed to resync sales data. %(error)s", error=str(ex)),
                "error",
            )
