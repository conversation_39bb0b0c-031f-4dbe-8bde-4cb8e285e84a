from flask_admin.actions import action
from wtforms import form

from finance_erp.admin.base.admin_view import BaseAdminViewForNavisionModel


class ExpenseReportForm(form.Form):
    pass


class ExpenseView(BaseAdminViewForNavisionModel):
    can_create = False
    can_edit = False
    can_delete = False
    column_list = (
        "posting_date",
        "pretax_amount",
        "tds_per",
        "uu_id",
        "last_push_attempt_at",
        "erp_remarks",
        "status",
        "hotel_code",
        "invoice_number",
        "invoice_amount",
        "invoice_date",
        "entry_type",
        "hsn_code",
        "doc_type",
        "remarks",
        "created_at",
        "modified_at",
    )

    column_labels = {
        "posting_date": "PostingDate",
        "pretax_amount": "PTaxAmount",
        "tds_per": "TDSPer",
        "hotel_code": "HotelCode",
        "invoice_number": "VendorInvoiceNo",
        "invoice_amount": "InvoiceAmount",
        "invoice_date": "InvoiceDate",
        "entry_type": "EntryType",
        "hsn_code": "HSNSACCode",
        "doc_type": "DocType",
        "remarks": "Remarks",
    }

    form = ExpenseReportForm

    column_default_sort = ("modified_at", True)

    column_searchable_list = ("hotel_code",)

    column_filters = ["posting_date", "status"]

    @action("push", "Push", "Are you sure you want to push items?")
    def action_push(self, ids):
        print(ids)
