from flask_admin.actions import action
from wtforms import form

from finance_erp.admin.base.admin_view import BaseAdminViewForNavisionModel


class HotelAdjustmentReportForm(form.Form):
    pass


class HotelAdjustmentView(BaseAdminViewForNavisionModel):
    can_create = False
    can_edit = False
    can_delete = False
    column_list = (
        "hotel_code",
        "posting_date",
        "amount",
        "uu_id",
        "last_push_attempt_at",
        "erp_remarks",
        "status",
        "invoice_number",
        "invoice_date",
        "invoice_amount",
        "entry_type",
        "adjustment_type",
        "doc_type",
        "remarks",
        "created_at",
        "modified_at",
    )
    column_labels = {
        "hotel_code": "HotelCode",
        "posting_date": "PostingDate",
        "amount": "JVAmount",
        "invoice_number": "InvoiceNo",
        "invoice_date": "InvoiceDate",
        "invoice_amount": "InvoiceAmount",
        "remarks": "Remarks",
        "adjustment_type": "AdjustmentType",
        "entry_type": "EntryType",
        "doc_type": "DocType",
    }

    form = HotelAdjustmentReportForm

    column_default_sort = ("modified_at", True)

    column_searchable_list = ("hotel_code",)

    column_filters = ["posting_date", "status"]

    @action("push", "Push", "Are you sure you want to push items?")
    def action_push(self, ids):
        print(ids)
