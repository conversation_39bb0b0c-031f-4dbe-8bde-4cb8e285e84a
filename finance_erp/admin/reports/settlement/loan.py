from flask_admin.actions import action
from wtforms import form

from finance_erp.admin.base.admin_view import BaseAdminViewForNavisionModel


class LoanReportForm(form.Form):
    pass


class LoanView(BaseAdminViewForNavisionModel):
    can_create = False
    can_edit = False
    can_delete = False
    column_list = (
        "hotel_code",
        "posting_date",
        "loan_amount",
        "uu_id",
        "last_push_attempt_at",
        "erp_remarks",
        "status",
        "entry_type",
        "doc_type",
        "remarks",
        "created_at",
        "modified_at",
    )

    column_labels = {
        "hotel_code": "HotelCode",
        "posting_date": "PostingDate",
        "loan_amount": "LoanAmount",
        "remarks": "Remarks",
        "entry_type": "EntryType",
        "doc_type": "DocType",
    }

    form = LoanReportForm

    column_default_sort = ("modified_at", True)

    column_searchable_list = ("hotel_code",)

    column_filters = ["posting_date", "status"]

    @action("push", "Push", "Are you sure you want to push items?")
    def action_push(self, ids):
        print(ids)
