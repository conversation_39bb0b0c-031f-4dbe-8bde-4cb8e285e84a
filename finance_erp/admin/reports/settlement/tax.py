from flask_admin.actions import action
from wtforms import form

from finance_erp.admin.base.admin_view import BaseAdminViewForNavisionModel


class TaxReportForm(form.Form):
    pass


class TaxView(BaseAdminViewForNavisionModel):
    can_create = False
    can_edit = False
    can_delete = False
    column_list = (
        "hotel_code",
        "uu_id",
        "last_push_attempt_at",
        "erp_remarks",
        "status",
        "entry_type",
        "doc_type",
        "posting_date",
        "cgst_amount",
        "sgst_amount",
        "total_amount",
        "created_at",
        "modified_at",
    )

    column_labels = {
        "hotel_code": "HotelCode",
        "posting_date": "PostingDate",
        "cgst_amount": "CGSTAmt",
        "sgst_amount": "SGSTAmt",
        "total_amount": "Amount",
        "entry_type": "EntryType",
        "doc_type": "DocType",
    }

    form = TaxReportForm

    column_default_sort = ("modified_at", True)

    column_searchable_list = ("hotel_code",)

    column_filters = ["posting_date", "status"]

    @action("verify", "Verify", "Are you sure you want to verify items?")
    def action_verify(self, ids):
        print(ids)

    @action("push", "Push", "Are you sure you want to push items?")
    def action_push(self, ids):
        print(ids)
