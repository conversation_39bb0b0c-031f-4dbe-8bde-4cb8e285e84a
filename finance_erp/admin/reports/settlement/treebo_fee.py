from flask_admin.actions import action
from wtforms import form

from finance_erp.admin.base.admin_view import BaseAdminViewForNavisionModel


class TreeboFeeReportForm(form.Form):
    pass


class TreeboFeeView(BaseAdminViewForNavisionModel):
    can_create = False
    can_edit = False
    can_delete = False
    column_list = (
        "settlement_date",
        "description",
        "amount",
        "uu_id",
        "last_push_attempt_at",
        "erp_remarks",
        "status",
        "hotel_code",
        "gst_percent",
        "entry_type",
        "hsn_code",
        "doc_type",
        "remarks",
        "created_at",
        "modified_at",
    )

    column_labels = {
        "hotel_code": "HotelCode",
        "settlement_date": "SettlementDate",
        "remarks": "Remarks",
        "description": "Description",
        "amount": "UnitAmount",
        "gst_percent": "GSTPercent",
        "entry_type": "EntryType",
        "hsn_code": "HSNSACCode",
        "doc_type": "DocType",
    }

    form = TreeboFeeReportForm

    column_default_sort = ("modified_at", True)

    column_searchable_list = ("hotel_code",)

    column_filters = ["settlement_date", "status"]

    @action("push", "Push", "Are you sure you want to push items?")
    def action_push(self, ids):
        print(ids)
