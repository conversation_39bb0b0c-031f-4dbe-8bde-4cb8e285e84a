from flask_admin.actions import action
from wtforms import form

from finance_erp.admin.base.admin_view import BaseAdminViewForNavisionModel


class TaCommissionReportForm(form.Form):
    pass


class TaCommissionReportView(BaseAdminViewForNavisionModel):
    can_create = False
    can_edit = False
    can_delete = False
    column_list = (
        "booking_created_date",
        "reference_number",
        "last_push_attempt_at",
        "erp_remarks",
        "status",
        "entry_type",
        "commission_amount",
        "hotel_code",
        "check_in",
        "check_out",
        "pretax_room_rent",
        "ta_name",
        "ta_sh_profile_code",
        "guest_name",
        "commission_percent",
        "paid_at_ota",
        "tds_percentage",
        "mop",
        "posting_date",
        "created_at",
        "modified_at",
    )

    column_labels = {
        "posting_date": "PostingDate",
        "commission_amount": "CommissionAmount",
        "reference_number": "BookingID",
        "hotel_code": "Hotelcode",
        "check_in": "CheckInDate",
        "check_out": "CheckOutDate",
        "pretax_room_rent": "NBV",
        "ta_name": "TAName",
        "ta_sh_profile_code": "TASHCode",
        "guest_name": "GuestName",
        "mop": "MOP",
        "tds_percentage": "TDSPer",
        "paid_at_ota": "GBV",
        "entry_type": "Entry Type",
        "commission_percent": "CommissionPer",
        "booking_created_date": "BookingCreationDate",
    }

    form = TaCommissionReportForm

    column_default_sort = ("modified_at", True)

    column_searchable_list = ("reference_number",)

    column_filters = ["booking_created_date", "status"]

    @action("push", "Push", "Are you sure you want to push items?")
    def action_push(self, ids):
        print(ids)
