from flask import Blueprint

from finance_erp.api.back_office.bulk_ingest_financial_data import (
    BulkIngestFinancialDataView,
)
from finance_erp.api.back_office.ledgers import LedgersFileView
from finance_erp.api.back_office.refresh_transaction_master_data import (
    RefreshTransactionMasterView,
)
from finance_erp.api.corporate.bulk_ingest_corporate_report import (
    BulkIngestCorporateReportView,
)
from finance_erp.api.corporate.bulk_pull_corporate_data import (
    BulkPullCorporateReportView,
)
from finance_erp.api.corporate.bulk_push_corporate_data import (
    BulkPushCorporateReportView,
)
from finance_erp.api.corporate.bulk_update_corporate_data import (
    BulkUpdateCorporateReportView,
)
from finance_erp.api.corporate.corporate_billing_scheduler import (
    CorporateBillingSchedulerView,
)
from finance_erp.api.corporate.get_corporate_data import FetchCorporateReportView
from finance_erp.api.hotel.bulk_ingest_hotel_report import BulkIngestHotelReportView
from finance_erp.api.hotel.bulk_pull_hotel_data import BulkPullHotelReportView
from finance_erp.api.hotel.bulk_push_hotel_data import BulkPushHotelReportView
from finance_erp.api.hotel.bulk_update_hotel_data import BulkUpdateHotelReportView
from finance_erp.api.hotel.get_hotel_data import FetchHotelReportView
from finance_erp.api.invoice.download_invoices import DownloadInvoiceView
from finance_erp.api.ota.bulk_ingest_ota_report import BulkIngestOTACommissionReportView
from finance_erp.api.ota.bulk_push_ota_data import BulkPushOTACommissionReportView
from finance_erp.api.ota.bulk_update_ota_data import BulkUpdateOTACommissionReportView
from finance_erp.api.ota.generate_ota_report import GenerateOTACommissionReportView
from finance_erp.api.ota.get_ota_data import FetchOTACommissionReportView
from finance_erp.api.payment.bulk_ingest_payment_report import (
    BulkIngestPaymentReportView,
)
from finance_erp.api.payment.bulk_push_payment_data import BulkPushPaymentReportView
from finance_erp.api.payment.bulk_update_payment_data import BulkUpdatePaymentReportView
from finance_erp.api.payment.generate_payment_report import GeneratePaymentReportView
from finance_erp.api.payment.generate_payment_summary import GeneratePaymentSummaryView
from finance_erp.api.payment.get_payment_data import FetchPaymentReportView
from finance_erp.api.pos.revenue_item.bulk_ingest_pos_revenue_item import (
    BulkIngestPOSRevenueItemView,
)
from finance_erp.api.pos.revenue_item.get_pos_revenue_item import GetPOSRevenueItemsView
from finance_erp.api.purchase.bulk_ingest_purchase_report import (
    BulkIngestPurchaseReportView,
)
from finance_erp.api.purchase.bulk_push_purchase_data import (
    BulkReportPurchaseReportView,
)
from finance_erp.api.purchase.bulk_update_purchase_data import (
    BulkUpdatePurchaseReportView,
)
from finance_erp.api.purchase.generate_purchase_invoice_report import (
    GeneratePurchaseReportView,
)
from finance_erp.api.purchase.get_purchase_data import FetchPurchaseReportView
from finance_erp.api.sales.bulk_ingest_sales_report import BulkIngestSalesReportView
from finance_erp.api.sales.bulk_push_sales_data import BulkPushSalesReportView
from finance_erp.api.sales.bulk_update_sales_data import BulkUpdateSalesReportView
from finance_erp.api.sales.generate_sales_invoice_report import GenerateSalesReportView
from finance_erp.api.sales.generate_sales_summary import GenerateSalesSummaryView
from finance_erp.api.sales.get_sales_data import FetchSalesReportView
from finance_erp.api.settlement.expense.bulk_ingest_expense_report import (
    BulkIngestExpenseReportView,
)
from finance_erp.api.settlement.expense.bulk_push_expense_data import (
    BulkPushExpenseReportView,
)
from finance_erp.api.settlement.expense.bulk_update_expense_data import (
    BulkUpdateExpenseReportView,
)
from finance_erp.api.settlement.expense.generate_expense_report import (
    GenerateExpenseReportView,
)
from finance_erp.api.settlement.expense.get_expense_data import FetchExpenseReportView
from finance_erp.api.settlement.hotel_adjustment.bulk_ingest_hotel_adjustment_report import (
    BulkIngestHotelAdjustmentReportView,
)
from finance_erp.api.settlement.hotel_adjustment.bulk_push_hotel_adjustment_data import (
    BulkPushHotelAdjustmentReportView,
)
from finance_erp.api.settlement.hotel_adjustment.bulk_update_hotel_adjustment_data import (
    BulkUpdateHotelAdjustmentReportView,
)
from finance_erp.api.settlement.hotel_adjustment.generate_hotel_adjustment_report import (
    GenerateHotelAdjustmentReportView,
)
from finance_erp.api.settlement.hotel_adjustment.get_hotel_adjustment_data import (
    FetchHotelAdjustmentReportView,
)
from finance_erp.api.settlement.loan.bulk_ingest_loan_report import (
    BulkIngestLoanReportView,
)
from finance_erp.api.settlement.loan.bulk_push_loan_data import BulkPushLoanReportView
from finance_erp.api.settlement.loan.bulk_update_loan_data import (
    BulkUpdateLoanReportView,
)
from finance_erp.api.settlement.loan.generate_loan_report import GenerateLoanReportView
from finance_erp.api.settlement.loan.get_loan_data import FetchLoanReportView
from finance_erp.api.settlement.tax.bulk_ingest_tax_report import (
    BulkIngestTaxReportView,
)
from finance_erp.api.settlement.tax.bulk_push_tax_data import BulkPushTaxReportView
from finance_erp.api.settlement.tax.bulk_update_tax_data import BulkUpdateTaxReportView
from finance_erp.api.settlement.tax.generate_tax_report import GenerateTaxReportView
from finance_erp.api.settlement.tax.get_tax_data import FetchTaxReportView
from finance_erp.api.settlement.treebo_fee.bulk_ingest_treebo_fee_report import (
    BulkIngestTreeboFeeReportView,
)
from finance_erp.api.settlement.treebo_fee.bulk_push_treebo_fee_data import (
    BulkPushTreeboFeeReportView,
)
from finance_erp.api.settlement.treebo_fee.bulk_update_treebo_fee_data import (
    BulkUpdateTreeboFeeReportView,
)
from finance_erp.api.settlement.treebo_fee.generate_treebo_fee_report import (
    GenerateTreeboFeeReportView,
)
from finance_erp.api.settlement.treebo_fee.get_treebo_fee_data import (
    FetchTreeboFeeReportView,
)
from finance_erp.api.ta_commission.bulk_ingest_ta_commission_report import (
    BulkIngestTACommissionReportView,
)
from finance_erp.api.ta_commission.bulk_push_ta_commission_data import (
    BulkPushTACommissionReportView,
)
from finance_erp.api.ta_commission.bulk_update_ta_commission_data import (
    BulkUpdateTACommissionReportView,
)
from finance_erp.api.ta_commission.generate_ta_commission_report import (
    GenerateTACommissionReportView,
)
from finance_erp.api.ta_commission.get_ta_commission_data import (
    FetchTACommissionReportView,
)

purchase_record_blue_print = Blueprint(
    "purchase_data", __name__, url_prefix="/api/v1/purchase-data"
)

purchase_record_blue_print.add_url_rule(
    "generate", view_func=GeneratePurchaseReportView.as_view("generate_purchase_record")
)

purchase_record_blue_print.add_url_rule(
    "ingest", view_func=BulkIngestPurchaseReportView.as_view("ingest_purchase_record")
)
purchase_record_blue_print.add_url_rule(
    "ingest-async",
    view_func=BulkIngestPurchaseReportView.as_view(
        "ingest_purchase_record_async", _async=True
    ),
)
purchase_record_blue_print.add_url_rule(
    "bulk-update",
    view_func=BulkUpdatePurchaseReportView.as_view("bulk_update_purchase_record"),
)

purchase_record_blue_print.add_url_rule(
    "bulk-push",
    view_func=BulkReportPurchaseReportView.as_view("bulk_push_purchase_record"),
)

purchase_record_blue_print.add_url_rule(
    "bulk-push-async",
    view_func=BulkReportPurchaseReportView.as_view(
        "bulk_push_purchase_record_async", _async=True
    ),
)

purchase_record_blue_print.add_url_rule(
    "", view_func=FetchPurchaseReportView.as_view("fetch_purchase_record")
)

payment_blue_print = Blueprint(
    "payment_data", __name__, url_prefix="/api/v1/payment-data"
)

payment_blue_print.add_url_rule(
    "generate", view_func=GeneratePaymentReportView.as_view("generate_payment_record")
)
payment_blue_print.add_url_rule(
    "ingest", view_func=BulkIngestPaymentReportView.as_view("ingest_payment_record")
)
payment_blue_print.add_url_rule(
    "ingest-async",
    view_func=BulkIngestPaymentReportView.as_view(
        "ingest_payment_record_async", _async=True
    ),
)

payment_blue_print.add_url_rule(
    "", view_func=FetchPaymentReportView.as_view("fetch_payment_record")
)

payment_blue_print.add_url_rule(
    "bulk-update",
    view_func=BulkUpdatePaymentReportView.as_view("bulk_update_payment_record"),
)

payment_blue_print.add_url_rule(
    "bulk-push", view_func=BulkPushPaymentReportView.as_view("bulk_push_payment_record")
)

payment_blue_print.add_url_rule(
    "bulk-push-async",
    view_func=BulkPushPaymentReportView.as_view(
        "bulk_push_payment_record_async", _async=True
    ),
)

payment_summary_blue_print = Blueprint(
    "pg_payments_summary", __name__, url_prefix="/api/v1/pg_payments-summary"
)

payment_summary_blue_print.add_url_rule(
    "generate", view_func=GeneratePaymentSummaryView.as_view("generate_payment_summary")
)

payment_summary_blue_print.add_url_rule(
    "generate-async",
    view_func=GeneratePaymentSummaryView.as_view(
        "generate_payment_summary_async", _async=True
    ),
)

ota_blue_print = Blueprint(
    "ota_commission_data", __name__, url_prefix="/api/v1/ota-commission-data"
)

ota_blue_print.add_url_rule(
    "generate",
    view_func=GenerateOTACommissionReportView.as_view("generate_ota_commission_record"),
)
ota_blue_print.add_url_rule(
    "ingest",
    view_func=BulkIngestOTACommissionReportView.as_view("ingest_ota_commission_record"),
)
ota_blue_print.add_url_rule(
    "ingest-async",
    view_func=BulkIngestOTACommissionReportView.as_view(
        "ingest_ota_commission_record_async", _async=True
    ),
)

ota_blue_print.add_url_rule(
    "", view_func=FetchOTACommissionReportView.as_view("fetch_ota_commission_record")
)

ota_blue_print.add_url_rule(
    "bulk-update",
    view_func=BulkUpdateOTACommissionReportView.as_view(
        "bulk_update_ota_commission_record"
    ),
)

ota_blue_print.add_url_rule(
    "bulk-push",
    view_func=BulkPushOTACommissionReportView.as_view("bulk_push_ota_record"),
)

ota_blue_print.add_url_rule(
    "bulk-push-async",
    view_func=BulkPushOTACommissionReportView.as_view(
        "bulk_push_ota_record_async", _async=True
    ),
)

ota_blue_print.add_url_rule(
    "generate", view_func=GenerateOTACommissionReportView.as_view("generate_ota_record")
)

sales_record_blue_print = Blueprint(
    "sales_data", __name__, url_prefix="/api/v1/sales-data"
)

sales_record_blue_print.add_url_rule(
    "generate", view_func=GenerateSalesReportView.as_view("generate_sales_record")
)

sales_record_blue_print.add_url_rule(
    "ingest", view_func=BulkIngestSalesReportView.as_view("ingest_sales_record")
)
sales_record_blue_print.add_url_rule(
    "ingest-async",
    view_func=BulkIngestSalesReportView.as_view(
        "ingest_sales_record_async", _async=True
    ),
)
sales_record_blue_print.add_url_rule(
    "bulk-update",
    view_func=BulkUpdateSalesReportView.as_view("bulk_update_sales_record"),
)

sales_record_blue_print.add_url_rule(
    "bulk-push", view_func=BulkPushSalesReportView.as_view("bulk_push_sales_record")
)

sales_record_blue_print.add_url_rule(
    "bulk-push-async",
    view_func=BulkPushSalesReportView.as_view(
        "bulk_push_sales_record_async", _async=True
    ),
)

sales_record_blue_print.add_url_rule(
    "", view_func=FetchSalesReportView.as_view("fetch_purchase_record")
)

sales_summary_record_blue_print = Blueprint(
    "sales_summary", __name__, url_prefix="/api/v1/sales-summary"
)

sales_summary_record_blue_print.add_url_rule(
    "generate", view_func=GenerateSalesSummaryView.as_view("generate_sales_summary")
)

sales_summary_record_blue_print.add_url_rule(
    "generate-async",
    view_func=GenerateSalesSummaryView.as_view(
        "generate_sales_summary_async", _async=True
    ),
)

corporate_blue_print = Blueprint(
    "corporate_data", __name__, url_prefix="/api/v1/corporate-data"
)

corporate_blue_print.add_url_rule(
    "ingest", view_func=BulkIngestCorporateReportView.as_view("ingest_corporate_record")
)
corporate_blue_print.add_url_rule(
    "ingest-async",
    view_func=BulkIngestCorporateReportView.as_view(
        "ingest_corporate_record_async", _async=True
    ),
)

corporate_blue_print.add_url_rule(
    "", view_func=FetchCorporateReportView.as_view("fetch_corporate_record")
)

corporate_blue_print.add_url_rule(
    "schedule-for-billing",
    view_func=CorporateBillingSchedulerView.as_view("schedule_for_billing"),
)

corporate_blue_print.add_url_rule(
    "bulk-update",
    view_func=BulkUpdateCorporateReportView.as_view("bulk_update_corporate_record"),
)

corporate_blue_print.add_url_rule(
    "bulk-push",
    view_func=BulkPushCorporateReportView.as_view("bulk_push_corporate_record"),
)

corporate_blue_print.add_url_rule(
    "bulk-pull",
    view_func=BulkPullCorporateReportView.as_view("bulk_pull_corporate_record"),
)

corporate_blue_print.add_url_rule(
    "bulk-push-async",
    view_func=BulkPushCorporateReportView.as_view(
        "bulk_push_corporate_record_async", _async=True
    ),
)
corporate_blue_print.add_url_rule(
    "bulk-pull-async",
    view_func=BulkPullCorporateReportView.as_view(
        "bulk_pull_corporate_record_async", _async=True
    ),
)

hotel_blue_print = Blueprint("hotel_data", __name__, url_prefix="/api/v1/hotel-data")

hotel_blue_print.add_url_rule(
    "ingest", view_func=BulkIngestHotelReportView.as_view("ingest_hotel_record")
)
hotel_blue_print.add_url_rule(
    "ingest-async",
    view_func=BulkIngestHotelReportView.as_view(
        "ingest_corporate_record_async", _async=True
    ),
)

hotel_blue_print.add_url_rule(
    "", view_func=FetchHotelReportView.as_view("fetch_hotel_record")
)

hotel_blue_print.add_url_rule(
    "bulk-update",
    view_func=BulkUpdateHotelReportView.as_view("bulk_update_hotel_record"),
)

hotel_blue_print.add_url_rule(
    "bulk-push", view_func=BulkPushHotelReportView.as_view("bulk_push_hotel_record")
)

hotel_blue_print.add_url_rule(
    "bulk-pull", view_func=BulkPullHotelReportView.as_view("bulk_pull_hotel_record")
)

hotel_blue_print.add_url_rule(
    "bulk-push-async",
    view_func=BulkPushHotelReportView.as_view(
        "bulk_push_hotel_record_async", _async=True
    ),
)
hotel_blue_print.add_url_rule(
    "bulk-pull-async",
    view_func=BulkPullHotelReportView.as_view(
        "bulk_pull_hotel_record_async", _async=True
    ),
)

settlement_treebo_fee_blue_print = Blueprint(
    "settlement_treebo_fee_data",
    __name__,
    url_prefix="/api/v1/settlement/treebo-fee-data",
)

settlement_treebo_fee_blue_print.add_url_rule(
    "generate",
    view_func=GenerateTreeboFeeReportView.as_view("generate_treebo_fee_record"),
)
settlement_treebo_fee_blue_print.add_url_rule(
    "ingest",
    view_func=BulkIngestTreeboFeeReportView.as_view("ingest_treebo_fee_record"),
)
settlement_treebo_fee_blue_print.add_url_rule(
    "ingest-async",
    view_func=BulkIngestTreeboFeeReportView.as_view(
        "ingest_treebo_fee_record_async", _async=True
    ),
)

settlement_treebo_fee_blue_print.add_url_rule(
    "", view_func=FetchTreeboFeeReportView.as_view("fetch_treebo_fee_record")
)

settlement_treebo_fee_blue_print.add_url_rule(
    "bulk-update",
    view_func=BulkUpdateTreeboFeeReportView.as_view("bulk_update_treebo_fee_record"),
)

settlement_treebo_fee_blue_print.add_url_rule(
    "bulk-push",
    view_func=BulkPushTreeboFeeReportView.as_view("bulk_push_treebo_fee_record"),
)

settlement_treebo_fee_blue_print.add_url_rule(
    "bulk-push-async",
    view_func=BulkPushTreeboFeeReportView.as_view(
        "bulk_push_treebo_fee_record_async", _async=True
    ),
)

settlement_expense_blue_print = Blueprint(
    "settlement_expense_data", __name__, url_prefix="/api/v1/settlement/expense-data"
)

settlement_expense_blue_print.add_url_rule(
    "generate", view_func=GenerateExpenseReportView.as_view("generate_expense_record")
)
settlement_expense_blue_print.add_url_rule(
    "ingest", view_func=BulkIngestExpenseReportView.as_view("ingest_expense_record")
)
settlement_expense_blue_print.add_url_rule(
    "ingest-async",
    view_func=BulkIngestExpenseReportView.as_view(
        "ingest_expense_record_async", _async=True
    ),
)

settlement_expense_blue_print.add_url_rule(
    "", view_func=FetchExpenseReportView.as_view("fetch_expense_record")
)

settlement_expense_blue_print.add_url_rule(
    "bulk-update",
    view_func=BulkUpdateExpenseReportView.as_view("bulk_update_expense_record"),
)

settlement_expense_blue_print.add_url_rule(
    "bulk-push", view_func=BulkPushExpenseReportView.as_view("bulk_push_expense_record")
)

settlement_expense_blue_print.add_url_rule(
    "bulk-push-async",
    view_func=BulkPushExpenseReportView.as_view(
        "bulk_push_expense_record_async", _async=True
    ),
)

settlement_loan_blue_print = Blueprint(
    "settlement_loan_data", __name__, url_prefix="/api/v1/settlement/loan-data"
)

settlement_loan_blue_print.add_url_rule(
    "generate", view_func=GenerateLoanReportView.as_view("generate_loan_record")
)
settlement_loan_blue_print.add_url_rule(
    "ingest", view_func=BulkIngestLoanReportView.as_view("ingest_loan_record")
)
settlement_loan_blue_print.add_url_rule(
    "ingest-async",
    view_func=BulkIngestLoanReportView.as_view("ingest_loan_record_async", _async=True),
)

settlement_loan_blue_print.add_url_rule(
    "", view_func=FetchLoanReportView.as_view("fetch_loan_record")
)

settlement_loan_blue_print.add_url_rule(
    "bulk-update",
    view_func=BulkUpdateLoanReportView.as_view("bulk_settlement_loan_record"),
)

settlement_loan_blue_print.add_url_rule(
    "bulk-push", view_func=BulkPushLoanReportView.as_view("bulk_push_loan_record")
)

settlement_loan_blue_print.add_url_rule(
    "bulk-push-async",
    view_func=BulkPushLoanReportView.as_view(
        "bulk_push_loan_record_async", _async=True
    ),
)

settlement_tax_blue_print = Blueprint(
    "settlement_tax_data", __name__, url_prefix="/api/v1/settlement/tax-data"
)

settlement_tax_blue_print.add_url_rule(
    "generate", view_func=GenerateTaxReportView.as_view("generate_tax_record")
)
settlement_tax_blue_print.add_url_rule(
    "ingest", view_func=BulkIngestTaxReportView.as_view("ingest_tax_record")
)
settlement_tax_blue_print.add_url_rule(
    "ingest-async",
    view_func=BulkIngestTaxReportView.as_view("ingest_tax_record_async", _async=True),
)

settlement_tax_blue_print.add_url_rule(
    "", view_func=FetchTaxReportView.as_view("fetch_tax_record")
)

settlement_tax_blue_print.add_url_rule(
    "bulk-update",
    view_func=BulkUpdateTaxReportView.as_view("bulk_settlement_tax_record"),
)

settlement_tax_blue_print.add_url_rule(
    "bulk-push", view_func=BulkPushTaxReportView.as_view("bulk_push_tax_record")
)

settlement_tax_blue_print.add_url_rule(
    "bulk-push-async",
    view_func=BulkPushTaxReportView.as_view("bulk_push_tax_record_async", _async=True),
)

settlement_hotel_adjustment_blue_print = Blueprint(
    "settlement_hotel_adjustment_data",
    __name__,
    url_prefix="/api/v1/settlement/hotel-adjustment-data",
)

settlement_hotel_adjustment_blue_print.add_url_rule(
    "generate",
    view_func=GenerateHotelAdjustmentReportView.as_view(
        "generate_hotel_adjustment_record"
    ),
)
settlement_hotel_adjustment_blue_print.add_url_rule(
    "ingest",
    view_func=BulkIngestHotelAdjustmentReportView.as_view(
        "ingest_hotel_adjustment_record"
    ),
)
settlement_hotel_adjustment_blue_print.add_url_rule(
    "ingest-async",
    view_func=BulkIngestHotelAdjustmentReportView.as_view(
        "ingest_hotel_adjustment_record_async", _async=True
    ),
)

settlement_hotel_adjustment_blue_print.add_url_rule(
    "",
    view_func=FetchHotelAdjustmentReportView.as_view("fetch_hotel_adjustment_record"),
)

settlement_hotel_adjustment_blue_print.add_url_rule(
    "bulk-update",
    view_func=BulkUpdateHotelAdjustmentReportView.as_view(
        "bulk_update_hotel_adjustment_record"
    ),
)

settlement_hotel_adjustment_blue_print.add_url_rule(
    "bulk-push",
    view_func=BulkPushHotelAdjustmentReportView.as_view(
        "bulk_push_hotel_adjustment_record"
    ),
)

settlement_hotel_adjustment_blue_print.add_url_rule(
    "bulk-push-async",
    view_func=BulkPushHotelAdjustmentReportView.as_view(
        "bulk_push_hotel_adjustment_record_async", _async=True
    ),
)

ta_commission_blue_print = Blueprint(
    "ta_commission_data", __name__, url_prefix="/api/v1/ta-commission-data"
)

ta_commission_blue_print.add_url_rule(
    "generate",
    view_func=GenerateTACommissionReportView.as_view("generate_ta_commission_record"),
)
ta_commission_blue_print.add_url_rule(
    "ingest",
    view_func=BulkIngestTACommissionReportView.as_view("ingest_ta_commission_record"),
)
ta_commission_blue_print.add_url_rule(
    "ingest-async",
    view_func=BulkIngestTACommissionReportView.as_view(
        "ingest_ta_commission_record_async", _async=True
    ),
)

ta_commission_blue_print.add_url_rule(
    "", view_func=FetchTACommissionReportView.as_view("fetch_ta_commission_record")
)

ta_commission_blue_print.add_url_rule(
    "bulk-update",
    view_func=BulkUpdateTACommissionReportView.as_view(
        "bulk_update_ta_commission_record"
    ),
)

ta_commission_blue_print.add_url_rule(
    "bulk-push", view_func=BulkPushTACommissionReportView.as_view("bulk_push_ta_record")
)

ta_commission_blue_print.add_url_rule(
    "bulk-push-async",
    view_func=BulkPushTACommissionReportView.as_view(
        "bulk_push_ta_record_async", _async=True
    ),
)

pos_blue_print = Blueprint("pos", __name__, url_prefix="/api/v1/pos")
pos_blue_print.add_url_rule(
    "revenue-item/ingest",
    view_func=BulkIngestPOSRevenueItemView.as_view("ingest_pos_revenue_item"),
)
pos_blue_print.add_url_rule(
    "revenue-item/ingest-async",
    view_func=BulkIngestPOSRevenueItemView.as_view(
        "ingest_pos_revenue_item_async", _async=True
    ),
)
pos_blue_print.add_url_rule(
    "revenue-item", view_func=GetPOSRevenueItemsView.as_view("fetch_pos_revenue_item")
)


back_office_blue_print = Blueprint(
    "backoffice", __name__, url_prefix="/api/v1/back-office"
)
back_office_blue_print.add_url_rule(
    "ledgers", view_func=LedgersFileView.as_view("ledgers_file")
)
back_office_blue_print.add_url_rule(
    "ledgers/generate-async",
    view_func=LedgersFileView.as_view("ledgers_file_generate_async", _async=True),
)
back_office_blue_print.add_url_rule(
    "financial-data/ingest",
    view_func=BulkIngestFinancialDataView.as_view("ingest_back_office_raw_data"),
)
back_office_blue_print.add_url_rule(
    "financial-data/ingest-async",
    view_func=BulkIngestFinancialDataView.as_view(
        "ingest_back_office_raw_data_async", _async=True
    ),
)
back_office_blue_print.add_url_rule(
    "refresh-transaction-master",
    view_func=RefreshTransactionMasterView.as_view("refresh_transaction_master"),
)

invoice_blue_print = Blueprint("invoice", __name__, url_prefix="/api/v1/invoice")
invoice_blue_print.add_url_rule(
    "download",
    view_func=DownloadInvoiceView.as_view("download-invoice"),
)
