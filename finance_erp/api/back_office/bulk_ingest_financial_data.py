import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.application.back_office.comand_handlers.bulk_ingest_financial_data import (
    BulkIngestFinancialDataCommandHandler,
)
from finance_erp.common.decorators import session_manager
from finance_erp.common.schema.back_office.financial_data import FinancialDataSchema
from object_registry import inject

logger = logging.getLogger(__name__)


class BulkIngestFinancialDataView(TreeboBaseAPI):
    @session_manager(commit=True)
    @schema_wrapper_parser(FinancialDataSchema)
    @inject(command_handler=BulkIngestFinancialDataCommandHandler)
    def post(
        self,
        command_handler: BulkIngestFinancialDataCommandHandler,
        parsed_request,
        **kwargs
    ):
        logger.info("Parsed input data", parsed_request)
        if self._async:
            command_handler.handle_async_execution_request(parsed_request)
        else:
            command_handler.handle(parsed_request)
        return {"data": "Ingested Successfully"}
