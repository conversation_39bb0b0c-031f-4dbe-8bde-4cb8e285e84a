import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import RequestTypes, schema_wrapper_parser
from finance_erp.api.schema.request.back_office.refresh_transaction_master_request_schema import (
    GetRefreshTransactionMasterRequestSchema,
    RefreshTransactionMasterRequestSchema,
)
from finance_erp.application.back_office.comand_handlers.get_transaction_master import (
    FetchTransactionMasterCommandHandler,
)
from finance_erp.application.back_office.comand_handlers.update_transaction_master import (
    UpdateTransactionMasterCommandHandler,
)
from finance_erp.application.back_office.dtos.transaction_master_dto import (
    TransactionMasterDto,
)
from finance_erp.audit_extension import audit_ext
from finance_erp.common.decorators import session_manager
from finance_erp.common.schema.back_office.transaction_master import (
    TransactionMasterResponseSchema,
)
from finance_erp.common.schema.common import serialize_with_schema
from object_registry import inject

logger = logging.getLogger(__name__)


class RefreshTransactionMasterView(TreeboBaseAPI):
    @audit_ext.manager.capture_trail(user_action="RefreshTransactionMaster")
    @session_manager(commit=True)
    @schema_wrapper_parser(RefreshTransactionMasterRequestSchema)
    @inject(command_handler=UpdateTransactionMasterCommandHandler)
    def post(
        self,
        command_handler: UpdateTransactionMasterCommandHandler,
        parsed_request,
        **kwargs
    ):
        request_dto = TransactionMasterDto(**parsed_request)
        command_handler.handle(request_dto)
        return {"message": "Transaction Master Updated Successfully"}

    @schema_wrapper_parser(
        GetRefreshTransactionMasterRequestSchema, param_type=RequestTypes.ARGS
    )
    @inject(command_handler=FetchTransactionMasterCommandHandler)
    def get(
        self,
        command_handler: FetchTransactionMasterCommandHandler,
        parsed_request,
        **kwargs
    ):
        request_dto = TransactionMasterDto(**parsed_request)
        data = command_handler.handle(request_dto)
        response = serialize_with_schema(
            TransactionMasterResponseSchema, data=data, many=True
        )
        return {"data": response}
