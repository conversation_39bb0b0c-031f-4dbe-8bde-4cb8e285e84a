import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.application.corporate.command_handler.bulk_ingest_corporate_report import (
    BulkIngestCorporateReportCommandHandler,
)
from finance_erp.common.constants import NavisionReports
from finance_erp.common.decorators import session_manager
from finance_erp.common.schema.corporate import CorporatePushSchema
from finance_erp.domain.company_profile.dto.corporate_dto import CorporateDto
from object_registry import inject

logger = logging.getLogger(__name__)


class BulkIngestCorporateReportView(TreeboBaseAPI):
    @session_manager(commit=True)
    @schema_wrapper_parser(CorporatePushSchema, many=True)
    @inject(command_handler=BulkIngestCorporateReportCommandHandler)
    def post(
        self,
        command_handler: BulkIngestCorporateReportCommandHandler,
        parsed_request,
        **kwargs
    ):
        if self._async:
            response = command_handler.handle_async_execution_request(
                parsed_request, report_name=NavisionReports.CORPORATE_REPORT
            )
        else:
            data_to_ingest = [CorporateDto.from_dict(item) for item in parsed_request]
            response = command_handler.handle(
                data_to_ingest, report_name=NavisionReports.CORPORATE_REPORT
            )
        return {"data": response}
