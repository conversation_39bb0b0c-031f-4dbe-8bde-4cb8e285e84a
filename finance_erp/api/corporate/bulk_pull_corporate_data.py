import logging

from treebo_commons.utils import dateutils

from core.common.api.treebo_api import TreeboBaseAPI
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.api.schema.request.corporate.bulk_pull_request import (
    BulkPullCorporateReportRequestSchema,
)
from finance_erp.application.corporate.command_handler.pull_corporate_report import (
    BulkPullCorporateReportCommandHandler,
)
from finance_erp.application.corporate.dtos.bulk_corporate_pull_request_dto import (
    BulkCorporatePullRequestDto,
)
from finance_erp.audit_extension import audit_ext
from finance_erp.common.constants import NavisionReports
from finance_erp.common.decorators import session_manager
from object_registry import inject

logger = logging.getLogger(__name__)


class BulkPullCorporateReportView(TreeboBaseAPI):
    @audit_ext.manager.capture_trail(user_action="BulkIngestCorporates")
    @session_manager(commit=True)
    @schema_wrapper_parser(BulkPullCorporateReportRequestSchema)
    @inject(command_handler=BulkPullCorporateReportCommandHandler)
    def post(
        self,
        command_handler: BulkPullCorporateReportCommandHandler,
        parsed_request,
        **kwargs
    ):
        if not parsed_request.get("date") and not parsed_request.get(
            "legal_entity_ids"
        ):
            parsed_request["date"] = dateutils.date_to_ymd_str(
                dateutils.subtract(dateutils.current_datetime(), days=1)
            )
        if self._async:
            response = command_handler.handle_async_execution_request(
                parsed_request, report_name=NavisionReports.CORPORATE_REPORT
            )
        else:
            request_dto = BulkCorporatePullRequestDto(**parsed_request)
            response = command_handler.handle(
                request_dto, report_name=NavisionReports.CORPORATE_REPORT
            )
        return {"data": response}
