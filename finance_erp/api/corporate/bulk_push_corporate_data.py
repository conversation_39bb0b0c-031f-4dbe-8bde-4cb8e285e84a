import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.api.schema.request.corporate.bulk_push_request import (
    BulkPushCorporateReportRequestSchema,
)
from finance_erp.application.corporate.command_handler.push_corporate_report import (
    BulkPushCorporateReportCommandHandler,
)
from finance_erp.application.corporate.dtos.bulk_corporate_push_request_dto import (
    BulkCorporatePushRequestDto,
)
from finance_erp.common.constants import NavisionReports
from object_registry import inject

logger = logging.getLogger(__name__)


class BulkPushCorporateReportView(TreeboBaseAPI):
    @schema_wrapper_parser(BulkPushCorporateReportRequestSchema)
    @inject(command_handler=BulkPushCorporateReportCommandHandler)
    def post(
        self,
        command_handler: BulkPushCorporateReportCommandHandler,
        parsed_request,
        **kwargs
    ):
        if self._async:
            response = command_handler.handle_async_execution_request(
                parsed_request, report_name=NavisionReports.CORPORATE_REPORT
            )
        else:
            request_dto = BulkCorporatePushRequestDto(**parsed_request)
            response = command_handler.handle(
                request_dto, report_name=NavisionReports.CORPORATE_REPORT
            )
        return {"data": response}
