import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.application.corporate.command_handler.bulk_update_corporate_record import (
    BulkUpdateCorporateReportCommandHandler,
)
from finance_erp.common.decorators import session_manager
from finance_erp.common.schema.common import serialize_with_schema
from finance_erp.common.schema.corporate import (
    CorporateResponseSchema,
    CorporateUpdateSchema,
)
from object_registry import inject

logger = logging.getLogger(__name__)


class BulkUpdateCorporateReportView(TreeboBaseAPI):
    @session_manager(commit=True)
    @schema_wrapper_parser(CorporateUpdateSchema, many=True)
    @inject(command_handler=BulkUpdateCorporateReportCommandHandler)
    def patch(
        self,
        command_handler: BulkUpdateCorporateReportCommandHandler,
        parsed_request,
        **kwargs
    ):
        entities = command_handler.handle(parsed_request)
        response = serialize_with_schema(
            CorporateResponseSchema, data=entities, many=True
        )
        return {"data": response}
