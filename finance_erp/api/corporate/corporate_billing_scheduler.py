import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.application.corporate.billing_service.corporate_billing_agent import (
    CorporateBillingAgent,
)
from finance_erp.application.corporate.dtos.billing_scheduler_request_dto import (
    BillingSchedulerRequestDto,
)
from finance_erp.common.decorators import session_manager
from finance_erp.common.schema.corporate import CorporateBillSchedulingRequest
from finance_erp.infrastructure.alerting.slack_alert_service_client import (
    SlackAlertServiceClient,
)
from object_registry import inject

logger = logging.getLogger(__name__)


class CorporateBillingSchedulerView(TreeboBaseAPI):
    @session_manager(commit=True)
    @schema_wrapper_parser(CorporateBillSchedulingRequest)
    @inject(
        corporate_billing_agent=CorporateBillingAgent,
        slack_notifier=SlackAlertServiceClient,
    )
    def post(
        self,
        corporate_billing_agent: CorporateBillingAgent,
        slack_notifier: SlackAlertServiceClient,
        parsed_request,
        **kwargs,
    ):
        try:
            billing_request = BillingSchedulerRequestDto(**parsed_request)
            corporate_billing_agent.schedule_billing_jobs(billing_request)
            return {"data": {"message": "Billing jobs scheduled successfully"}}
        except Exception as e:
            slack_notifier.send_alert(f"Error while scheduling billing jobs: {str(e)}")
            raise e
