import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import RequestTypes, schema_wrapper_parser
from finance_erp.api.schema.request.corporate.get_corporate_data_schema import (
    GetCorporateReportRequestSchema,
)
from finance_erp.application.corporate.command_handler.get_corporate_report import (
    FetchCorporateCommandHandler,
)
from finance_erp.application.corporate.dtos.get_corporate_report_dto import (
    GetCorporateReportDto,
)
from finance_erp.common.schema.common import serialize_with_schema
from finance_erp.common.schema.corporate import CorporateResponseSchema
from object_registry import inject

logger = logging.getLogger(__name__)


class FetchCorporateReportView(TreeboBaseAPI):
    @schema_wrapper_parser(
        GetCorporateReportRequestSchema, param_type=RequestTypes.ARGS
    )
    @inject(command_handler=FetchCorporateCommandHandler)
    def get(
        self, command_handler: FetchCorporateCommandHandler, parsed_request, **kwargs
    ):
        request_dto = GetCorporateReportDto(**parsed_request)
        data = command_handler.handle(request_dto)
        response = serialize_with_schema(CorporateResponseSchema, data=data, many=True)
        return {"data": response}
