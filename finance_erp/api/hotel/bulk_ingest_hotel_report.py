import logging

from core.common.api.treebo_api import TreeboBase<PERSON><PERSON>
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.application.hotel.command_handler.bulk_ingest_hotel_report import (
    BulkIngestHotelReportCommandHandler,
)
from finance_erp.common.constants import NavisionReports
from finance_erp.common.decorators import session_manager
from finance_erp.common.schema.hotel import HotelPushSchema
from object_registry import inject

logger = logging.getLogger(__name__)


class BulkIngestHotelReportView(TreeboBaseAPI):
    @session_manager(commit=True)
    @inject(command_handler=BulkIngestHotelReportCommandHandler)
    @schema_wrapper_parser(HotelPushSchema, many=True)
    def post(
        self,
        command_handler: BulkIngestHotelReportCommandHandler,
        parsed_request,
        **kwargs
    ):
        if self._async:
            response = command_handler.handle_async_execution_request(
                parsed_request, report_name=NavisionReports.HOTEL_REPORT
            )
        else:
            response = command_handler.handle(
                parsed_request, report_name=NavisionReports.HOTEL_REPORT
            )
        return {"data": response}
