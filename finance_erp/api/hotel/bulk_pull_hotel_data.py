import logging

from core.common.api.treebo_api import TreeboBaseAPI
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.api.schema.request.hotel.bulk_pull_request import (
    BulkPullHotelReportRequestSchema,
)
from finance_erp.application.hotel.command_handler.pull_hotel_report import (
    BulkPullHotelReportCommandHandler,
)
from finance_erp.application.hotel.dtos.bulk_hotel_pull_request_dto import (
    BulkHotelPullRequestDto,
)
from finance_erp.audit_extension import audit_ext
from finance_erp.common.constants import NavisionReports
from finance_erp.common.decorators import session_manager
from object_registry import inject

logger = logging.getLogger(__name__)


class BulkPullHotelReportView(TreeboBaseAPI):
    @audit_ext.manager.capture_trail(user_action="BulkIngestHotelData")
    @session_manager(commit=True)
    @schema_wrapper_parser(BulkPullHotelReportRequestSchema)
    @inject(command_handler=BulkPullHotelReportCommandHandler)
    def post(
        self,
        command_handler: BulkPullHotelReportCommandHandler,
        parsed_request,
        **kwargs
    ):
        if self._async:
            response = command_handler.handle_async_execution_request(
                parsed_request, report_name=NavisionReports.HOTEL_REPORT
            )
        else:
            request_dto = BulkHotelPullRequestDto(**parsed_request)
            response = command_handler.handle(
                request_dto, report_name=NavisionReports.HOTEL_REPORT
            )
        return {"data": response}
