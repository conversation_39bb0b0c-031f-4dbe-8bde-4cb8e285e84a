import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.api.schema.request.hotel.bulk_push_request import (
    BulkPushHotelReportRequestSchema,
)
from finance_erp.application.hotel.command_handler.push_hotel_report import (
    BulkPushHotelReportCommandHandler,
)
from finance_erp.application.hotel.dtos.bulk_hotel_push_request_dto import (
    BulkHotelPushRequestDto,
)
from finance_erp.common.constants import NavisionReports
from object_registry import inject

logger = logging.getLogger(__name__)


class BulkPushHotelReportView(TreeboBaseAPI):
    @schema_wrapper_parser(BulkPushHotelReportRequestSchema)
    @inject(command_handler=BulkPushHotelReportCommandHandler)
    def post(
        self,
        command_handler: BulkPushHotelReportCommandHandler,
        parsed_request,
        **kwargs
    ):
        if self._async:
            response = command_handler.handle_async_execution_request(
                parsed_request, report_name=NavisionReports.HOTEL_REPORT
            )
        else:
            request_dto = BulkHotelPushRequestDto(**parsed_request)
            response = command_handler.handle(
                request_dto, report_name=NavisionReports.HOTEL_REPORT
            )
        return {"data": response}
