import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.application.hotel.command_handler.bulk_update_hotel_record import (
    BulkUpdateHotelReportCommandHandler,
)
from finance_erp.common.decorators import session_manager
from finance_erp.common.schema.common import serialize_with_schema
from finance_erp.common.schema.hotel import HotelResponseSchema, HotelUpdateSchema
from object_registry import inject

logger = logging.getLogger(__name__)


class BulkUpdateHotelReportView(TreeboBaseAPI):
    @session_manager(commit=True)
    @schema_wrapper_parser(HotelUpdateSchema, many=True)
    @inject(command_handler=BulkUpdateHotelReportCommandHandler)
    def patch(
        self,
        command_handler: BulkUpdateHotelReportCommandHandler,
        parsed_request,
        **kwargs
    ):
        entities = command_handler.handle(parsed_request)
        response = serialize_with_schema(HotelResponseSchema, data=entities, many=True)
        return {"data": response}
