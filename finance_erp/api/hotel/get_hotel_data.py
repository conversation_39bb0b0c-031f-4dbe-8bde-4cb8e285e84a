import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import RequestTypes, schema_wrapper_parser
from finance_erp.api.schema.request.hotel.get_hotel_data_schema import (
    GetHotelReportRequestSchema,
)
from finance_erp.application.hotel.command_handler.get_hotel_report import (
    FetchHotelCommandHandler,
)
from finance_erp.application.hotel.dtos.get_hotel_report_dto import GetHotelReportDto
from finance_erp.common.schema.common import serialize_with_schema
from finance_erp.common.schema.hotel import HotelResponseSchema
from object_registry import inject

logger = logging.getLogger(__name__)


class FetchHotelReportView(TreeboBaseAPI):
    @schema_wrapper_parser(GetHotelReportRequestSchema, param_type=RequestTypes.ARGS)
    @inject(command_handler=FetchHotelCommandHandler)
    def get(self, command_handler: FetchHotelCommandHandler, parsed_request, **kwargs):
        request_dto = GetHotelReportDto(**parsed_request)
        data = command_handler.handle(request_dto)
        response = serialize_with_schema(HotelResponseSchema, data=data, many=True)
        return {"data": response}
