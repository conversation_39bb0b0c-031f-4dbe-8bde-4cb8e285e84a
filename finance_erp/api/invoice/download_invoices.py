import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.api.schema.request.invoice.download_invoice_schema import (
    DownloadInvoiceRequestSchema,
)
from finance_erp.application.invoice.command_handler.download_invoice import (
    DownloadInvoiceCommandHandler,
)
from finance_erp.application.invoice.dtos.download_invoice_dto import (
    DownloadInvoiceRequestDto,
)
from object_registry import inject

logger = logging.getLogger(__name__)


class DownloadInvoiceView(TreeboBaseAPI):
    @schema_wrapper_parser(DownloadInvoiceRequestSchema)
    @inject(command_handler=DownloadInvoiceCommandHandler)
    def post(
        self,
        command_handler: DownloadInvoiceCommandHandler,
        parsed_request,
    ):
        request_dto = DownloadInvoiceRequestDto(**parsed_request)
        try:
            command_handler.handle(request_dto)
        except Exception as e:
            msg = f"Invoice Download Failed with error: {e.args[0]}"
            logger.exception(msg)
            raise e
        return {"data": "Invoices will be delivered in email"}
