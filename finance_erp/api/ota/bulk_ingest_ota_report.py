import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.application.ota.commands.bulk_ingest_ota_report import (
    BulkIngestOTACommissionReportCommandHandler,
)
from finance_erp.common.constants import NavisionReports
from finance_erp.common.decorators import session_manager
from finance_erp.common.schema.ota import OTACommissionPushSchema
from object_registry import inject

logger = logging.getLogger(__name__)


class BulkIngestOTACommissionReportView(TreeboBaseAPI):
    @session_manager(commit=True)
    @schema_wrapper_parser(OTACommissionPushSchema, many=True)
    @inject(command_handler=BulkIngestOTACommissionReportCommandHandler)
    def post(
        self,
        command_handler: BulkIngestOTACommissionReportCommandHandler,
        parsed_request,
        **kwargs
    ):
        if self._async:
            response = command_handler.handle_async_execution_request(
                parsed_request, report_name=NavisionReports.OTA_COMMISSION_REPORT
            )
        else:
            response = command_handler.handle(
                parsed_request, report_name=NavisionReports.OTA_COMMISSION_REPORT
            )
        return {"data": response}
