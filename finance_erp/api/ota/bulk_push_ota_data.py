import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.api.schema.request.ota.bulk_push_request import (
    BulkPushOTAReportRequestSchema,
)
from finance_erp.application.ota.commands.push_ota_report import (
    BulkPushOTACommissionReportCommandHandler,
)
from finance_erp.application.ota.dtos.bulk_ota_push_request_dto import (
    BulkOTACommissionPushRequestDto,
)
from finance_erp.common.constants import NavisionReports
from object_registry import inject

logger = logging.getLogger(__name__)


class BulkPushOTACommissionReportView(TreeboBaseAPI):
    @schema_wrapper_parser(BulkPushOTAReportRequestSchema)
    @inject(command_handler=BulkPushOTACommissionReportCommandHandler)
    def post(
        self,
        command_handler: BulkPushOTACommissionReportCommandHandler,
        parsed_request,
        **kwargs
    ):
        if self._async:
            response = command_handler.handle_async_execution_request(
                parsed_request, report_name=NavisionReports.OTA_COMMISSION_REPORT
            )
        else:
            request_dto = BulkOTACommissionPushRequestDto(**parsed_request)
            response = command_handler.handle(
                request_dto, report_name=NavisionReports.OTA_COMMISSION_REPORT
            )
        return {"data": response}
