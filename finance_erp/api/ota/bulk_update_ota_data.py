import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.application.ota.commands.bulk_update_ota_record import (
    BulkUpdateOTACommissionReportCommandHandler,
)
from finance_erp.common.decorators import session_manager
from finance_erp.common.schema.common import serialize_with_schema
from finance_erp.common.schema.ota import (
    OTACommissionResponseSchema,
    OTACommissionUpdateSchema,
)
from object_registry import inject

logger = logging.getLogger(__name__)


class BulkUpdateOTACommissionReportView(TreeboBaseAPI):
    @session_manager(commit=True)
    @schema_wrapper_parser(OTACommissionUpdateSchema, many=True)
    @inject(command_handler=BulkUpdateOTACommissionReportCommandHandler)
    def patch(
        self,
        command_handler: BulkUpdateOTACommissionReportCommandHandler,
        parsed_request,
        **kwargs
    ):
        aggregates = command_handler.handle(parsed_request)
        response = serialize_with_schema(
            OTACommissionResponseSchema, data=aggregates, many=True
        )
        return {"data": response}
