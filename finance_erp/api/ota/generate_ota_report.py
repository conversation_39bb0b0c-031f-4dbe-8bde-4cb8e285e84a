import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.api.schema.request.ota.generate_ota_schema import (
    GenerateOTAReportRequestSchema,
)
from finance_erp.application.ota.commands.generate_ota_data import (
    OTACommissionReportGeneratorCommandHandler,
)
from finance_erp.application.ota.dtos.generate_ota_report_dto import (
    GenerateOTACommissionReportDto,
)
from object_registry import inject

logger = logging.getLogger(__name__)


class GenerateOTACommissionReportView(TreeboBaseAPI):
    @schema_wrapper_parser(GenerateOTAReportRequestSchema)
    @inject(command_handler=OTACommissionReportGeneratorCommandHandler)
    def post(
        self,
        command_handler: OTACommissionReportGeneratorCommandHandler,
        parsed_request,
        **kwargs,
    ):
        request_dto = GenerateOTACommissionReportDto(**parsed_request)
        try:
            logger.info(f"Started ota commission report generation")
            return command_handler.handle(request_dto)
        except Exception as e:
            msg = f"Report push has failed with error: {e.args[0]}"
            logger.exception(msg)
            raise e
