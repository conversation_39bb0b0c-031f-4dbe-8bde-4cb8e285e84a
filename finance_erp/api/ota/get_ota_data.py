import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import RequestTypes, schema_wrapper_parser
from finance_erp.api.schema.request.ota.get_ota_data_schema import (
    GetOTAReportRequestSchema,
)
from finance_erp.application.ota.commands.get_ota_report import (
    FetchOTACommissionsCommandHandler,
)
from finance_erp.application.ota.dtos.get_ota_report_dto import (
    GetOTACommissionReportDto,
)
from finance_erp.common.schema.common import serialize_with_schema
from finance_erp.common.schema.ota import OTACommissionResponseSchema
from object_registry import inject

logger = logging.getLogger(__name__)


class FetchOTACommissionReportView(TreeboBaseAPI):
    @schema_wrapper_parser(GetOTAReportRequestSchema, param_type=RequestTypes.ARGS)
    @inject(command_handler=FetchOTACommissionsCommandHandler)
    def get(
        self,
        command_handler: FetchOTACommissionsCommandHandler,
        parsed_request,
        **kwargs
    ):
        request_dto = GetOTACommissionReportDto(**parsed_request)
        data = command_handler.handle(request_dto)
        response = serialize_with_schema(
            OTACommissionResponseSchema, data=data, many=True
        )
        return {"data": response}
