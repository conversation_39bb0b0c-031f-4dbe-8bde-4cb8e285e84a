import logging

from core.common.api.treebo_api import Tree<PERSON>B<PERSON><PERSON><PERSON>
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.application.payments.commands.bulk_ingest_payment_report import (
    BulkIngestPaymentReportCommandHandler,
)
from finance_erp.common.constants import NavisionReports
from finance_erp.common.decorators import session_manager
from finance_erp.common.schema.payment import PGTransactionPushSchema
from object_registry import inject

logger = logging.getLogger(__name__)


class BulkIngestPaymentReportView(TreeboBaseAPI):
    @session_manager(commit=True)
    @schema_wrapper_parser(PGTransactionPushSchema, many=True)
    @inject(command_handler=BulkIngestPaymentReportCommandHandler)
    def post(
        self,
        command_handler: BulkIngestPaymentReportCommandHandler,
        parsed_request,
        **kwargs
    ):
        if self._async:
            response = command_handler.handle_async_execution_request(
                parsed_request, report_name=NavisionReports.PAYMENT_GATEWAY_REPORT
            )
        else:
            response = command_handler.handle(
                parsed_request, report_name=NavisionReports.PAYMENT_GATEWAY_REPORT
            )
        return {"data": response}
