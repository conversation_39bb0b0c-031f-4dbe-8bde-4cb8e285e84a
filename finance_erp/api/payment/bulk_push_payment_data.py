import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.api.schema.request.payment.bulk_push_request import (
    BulkPushPaymentReportRequestSchema,
)
from finance_erp.application.payments.commands.push_payment_summary_report import (
    BulkPushPaymentReportCommandHandler,
)
from finance_erp.application.payments.dtos.bulk_payment_push_request_dto import (
    BulkPaymentPushRequestDto,
)
from finance_erp.common.constants import NavisionReports
from object_registry import inject

logger = logging.getLogger(__name__)


class BulkPushPaymentReportView(TreeboBaseAPI):
    @schema_wrapper_parser(BulkPushPaymentReportRequestSchema)
    @inject(command_handler=BulkPushPaymentReportCommandHandler)
    def post(
        self,
        command_handler: BulkPushPaymentReportCommandHandler,
        parsed_request,
        **kwargs
    ):
        if self._async:
            response = command_handler.handle_async_execution_request(
                parsed_request, report_name=NavisionReports.PAYMENT_GATEWAY_REPORT
            )
        else:
            request_dto = BulkPaymentPushRequestDto(**parsed_request)
            response = command_handler.handle(
                request_dto, report_name=NavisionReports.PAYMENT_GATEWAY_REPORT
            )
        return {"data": response}
