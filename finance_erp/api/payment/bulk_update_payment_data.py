import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.application.payments.commands.bulk_update_payment_record import (
    BulkUpdatePaymentReportCommandHandler,
)
from finance_erp.common.decorators import session_manager
from finance_erp.common.schema.common import serialize_with_schema
from finance_erp.common.schema.payment import (
    PGTransactionResponseSchema,
    PGTransactionUpdateSchema,
)
from object_registry import inject

logger = logging.getLogger(__name__)


class BulkUpdatePaymentReportView(TreeboBaseAPI):
    @session_manager(commit=True)
    @schema_wrapper_parser(PGTransactionUpdateSchema, many=True)
    @inject(command_handler=BulkUpdatePaymentReportCommandHandler)
    def patch(
        self,
        command_handler: BulkUpdatePaymentReportCommandHandler,
        parsed_request,
        **kwargs
    ):
        entities = command_handler.handle(parsed_request)
        response = serialize_with_schema(
            PGTransactionResponseSchema, data=entities, many=True
        )
        return {"data": response}
