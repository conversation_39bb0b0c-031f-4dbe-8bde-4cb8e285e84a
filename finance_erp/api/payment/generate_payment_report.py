import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.api.schema.request.payment.generate_payment_schema import (
    GeneratePaymentReportRequestSchema,
)
from finance_erp.application.payments.commands.generate_payment_data import (
    PaymentReportGeneratorCommandHandler,
)
from finance_erp.application.payments.dtos.generate_payment_report_dto import (
    GeneratePaymentReportDto,
)
from object_registry import inject

logger = logging.getLogger(__name__)


class GeneratePaymentReportView(TreeboBaseAPI):
    @schema_wrapper_parser(GeneratePaymentReportRequestSchema)
    @inject(command_handler=PaymentReportGeneratorCommandHandler)
    def post(
        self,
        command_handler: PaymentReportGeneratorCommandHandler,
        parsed_request,
        **kwargs,
    ):
        request_dto = GeneratePaymentReportDto(**parsed_request)
        try:

            logger.info(f"Started payment report generation")
            return command_handler.handle(request_dto)
        except Exception as e:
            msg = f"Report push has failed with error: {e.args[0]}"
            logger.exception(msg)
            raise e
