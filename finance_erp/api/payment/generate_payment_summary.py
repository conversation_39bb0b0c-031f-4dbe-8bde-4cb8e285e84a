import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.api.schema.request.payment.generate_payment_schema import (
    GeneratePaymentSummaryRequestSchema,
)
from finance_erp.application.common.dtos.data_generation_base_dto import (
    DataGenerationBaseDto,
)
from finance_erp.application.payments.commands.generate_payment_summary import (
    PaymentSummaryGenerationHandler,
)
from object_registry import inject

logger = logging.getLogger(__name__)


class GeneratePaymentSummaryView(TreeboBaseAPI):
    @schema_wrapper_parser(GeneratePaymentSummaryRequestSchema)
    @inject(command_handler=PaymentSummaryGenerationHandler)
    def post(
        self,
        command_handler: PaymentSummaryGenerationHandler,
        parsed_request,
        **kwargs,
    ):
        if self._async:
            response = command_handler.handle_async_execution_request(parsed_request)
        else:
            request_dto = DataGenerationBaseDto(**parsed_request)
            response = command_handler.handle(request_dto)
        return {"data": response}
