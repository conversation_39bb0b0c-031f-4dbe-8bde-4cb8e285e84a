import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import RequestTypes, schema_wrapper_parser
from finance_erp.api.schema.request.payment.get_payment_data_schema import (
    GetPaymentReportRequestSchema,
)
from finance_erp.application.payments.commands.get_paymnet_report import (
    FetchPaymentsCommandHandler,
)
from finance_erp.application.payments.dtos.get_payement_report_dto import (
    GetPaymentReportDto,
)
from finance_erp.common.schema.common import serialize_with_schema
from finance_erp.common.schema.payment import PGTransactionResponseSchema
from object_registry import inject

logger = logging.getLogger(__name__)


class FetchPaymentReportView(TreeboBaseAPI):
    @schema_wrapper_parser(GetPaymentReportRequestSchema, param_type=RequestTypes.ARGS)
    @inject(command_handler=FetchPaymentsCommandHandler)
    def get(
        self, command_handler: FetchPaymentsCommandHandler, parsed_request, **kwargs
    ):
        request_dto = GetPaymentReportDto(**parsed_request)
        data = command_handler.handle(request_dto)
        response = serialize_with_schema(
            PGTransactionResponseSchema, data=data, many=True
        )
        return {"data": response}
