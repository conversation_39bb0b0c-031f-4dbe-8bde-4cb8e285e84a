import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.application.pos.commands.bulk_ingest_pos_revenue import (
    BulkIngestPOSRevenueCommandHandler,
)
from finance_erp.common.constants import EntityTypes
from finance_erp.common.decorators import session_manager
from finance_erp.common.schema.common import serialize_with_schema
from finance_erp.common.schema.pos.pos_revenue import (
    POSRevenueItemRequestSchema,
    POSRevenueItemResponseSchema,
)
from object_registry import inject

logger = logging.getLogger(__name__)


class BulkIngestPOSRevenueItemView(TreeboBaseAPI):
    @session_manager(commit=True)
    @schema_wrapper_parser(POSRevenueItemRequestSchema, many=True)
    @inject(command_handler=BulkIngestPOSRevenueCommandHandler)
    def post(
        self,
        command_handler: BulkIngestPOSRevenueCommandHandler,
        parsed_request,
        **kwargs
    ):
        if self._async:
            response = command_handler.handle_async_execution_request(
                parsed_request, report_name=EntityTypes.POS_REVENUE
            )
        else:
            data = command_handler.handle(
                parsed_request, report_name=EntityTypes.POS_REVENUE
            )
            response = serialize_with_schema(
                POSRevenueItemResponseSchema, data=data, many=True
            )

        return {"data": response}
