import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import RequestTypes, schema_wrapper_parser
from finance_erp.application.pos.queries.pos_revenue_query_handler import (
    GetPOSRevenueItemsCommandHandler,
)
from finance_erp.common.schema.common import serialize_with_schema
from finance_erp.common.schema.pos.pos_revenue import (
    POSRevenueItemResponseSchema,
    POSRevenueItemSearchSchema,
)
from object_registry import inject

logger = logging.getLogger(__name__)


class GetPOSRevenueItemsView(TreeboBaseAPI):
    @schema_wrapper_parser(POSRevenueItemSearchSchema, param_type=RequestTypes.ARGS)
    @inject(command_handler=GetPOSRevenueItemsCommandHandler)
    def get(
        self,
        command_handler: GetPOSRevenueItemsCommandHandler,
        parsed_request,
        **kwargs
    ):
        limit = parsed_request.get("limit")
        offset = parsed_request.get("offset")
        data = command_handler.handle(parsed_request, limit, offset)
        response = serialize_with_schema(
            POSRevenueItemResponseSchema, data=data, many=True
        )
        return dict(data=response, limit=limit, offset=offset)
