import logging

from core.common.api.treebo_api import TreeboBase<PERSON><PERSON>
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.application.invoice.command_handler.bulk_ingest_purchase_report import (
    BulkIngestPurchaseReportCommandHandler,
)
from finance_erp.common.constants import NavisionReports
from finance_erp.common.decorators import session_manager
from finance_erp.common.schema.purchase import PurchaseInvoiceIngestSchema
from object_registry import inject

logger = logging.getLogger(__name__)


class BulkIngestPurchaseReportView(TreeboBaseAPI):
    @session_manager(commit=True)
    @schema_wrapper_parser(PurchaseInvoiceIngestSchema, many=True)
    @inject(command_handler=BulkIngestPurchaseReportCommandHandler)
    def post(
        self,
        command_handler: BulkIngestPurchaseReportCommandHandler,
        parsed_request,
        **kwargs
    ):
        if self._async:
            response = command_handler.handle_async_execution_request(
                parsed_request, report_name=NavisionReports.PURCHASE_INVOICE_REPORT
            )
        else:
            response = command_handler.handle(
                parsed_request, report_name=NavisionReports.PURCHASE_INVOICE_REPORT
            )
        return {
            "data": response,
        }
