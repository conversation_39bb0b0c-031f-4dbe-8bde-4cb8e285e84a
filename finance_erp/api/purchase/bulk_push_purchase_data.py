import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.api.schema.request.purchase.bulk_push_request import (
    BulkPushPurchaseReportRequestSchema,
)
from finance_erp.application.invoice.command_handler.push_purchase_report import (
    BulkPushPurchaseReportCommandHandler,
)
from finance_erp.application.invoice.dtos.bulk_purchase_push_request_dto import (
    BulkPurchasePushRequestDto,
)
from finance_erp.common.constants import NavisionReports
from object_registry import inject

logger = logging.getLogger(__name__)


class BulkReportPurchaseReportView(TreeboBaseAPI):
    @schema_wrapper_parser(BulkPushPurchaseReportRequestSchema)
    @inject(command_handler=BulkPushPurchaseReportCommandHandler)
    def post(
        self,
        command_handler: BulkPushPurchaseReportCommandHandler,
        parsed_request,
        **kwargs
    ):
        if self._async:
            response = command_handler.handle_async_execution_request(
                parsed_request, report_name=NavisionReports.PURCHASE_INVOICE_REPORT
            )
        else:
            request_dto = BulkPurchasePushRequestDto(**parsed_request)
            response = command_handler.handle(
                request_dto, report_name=NavisionReports.PURCHASE_INVOICE_REPORT
            )
        return {"data": response}
