import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.application.invoice.command_handler.bulk_update_purchase_record import (
    BulkUpdatePurchaseReportCommandHandler,
)
from finance_erp.common.decorators import session_manager
from finance_erp.common.schema.common import serialize_with_schema
from finance_erp.common.schema.purchase import (
    PurchaseInvoiceResponseSchema,
    PurchaseInvoiceUpdateSchema,
)
from object_registry import inject

logger = logging.getLogger(__name__)


class BulkUpdatePurchaseReportView(TreeboBaseAPI):
    @session_manager(commit=True)
    @schema_wrapper_parser(PurchaseInvoiceUpdateSchema, many=True)
    @inject(command_handler=BulkUpdatePurchaseReportCommandHandler)
    def patch(
        self,
        command_handler: BulkUpdatePurchaseReportCommandHandler,
        parsed_request,
        **kwargs
    ):
        entities = command_handler.handle(parsed_request)
        response = serialize_with_schema(
            PurchaseInvoiceResponseSchema, data=entities, many=True
        )
        return {"data": response}
