import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.api.schema.request.purchase.generate_purchase_report import (
    GeneratePurchaseReportRequestSchema,
)
from finance_erp.application.invoice.command_handler.generate_purchase_report import (
    PurchaseReportGeneratorCommandHandler,
)
from finance_erp.application.invoice.dtos.generate_purchase_report_dto import (
    GeneratePurchaseReportDto,
)
from object_registry import inject

logger = logging.getLogger(__name__)


class GeneratePurchaseReportView(TreeboBaseAPI):
    @schema_wrapper_parser(GeneratePurchaseReportRequestSchema)
    @inject(command_handler=PurchaseReportGeneratorCommandHandler)
    def post(
        self,
        command_handler: PurchaseReportGeneratorCommandHandler,
        parsed_request,
        **kwargs,
    ):
        request_dto = GeneratePurchaseReportDto(**parsed_request)
        try:
            logger.info(f"Started purchase report generation")
            return command_handler.handle(request_dto)
        except Exception as e:
            msg = f"Report generation push has failed with error: {e.args[0]}"
            logger.exception(msg)
            raise e
