import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import RequestTypes, schema_wrapper_parser
from finance_erp.api.schema.request.purchase.get_purchase_data import (
    GetPurchaseReportRequestSchema,
)
from finance_erp.application.invoice.command_handler.get_purchase_report import (
    FetchPurchaseReportCommandHandler,
)
from finance_erp.application.invoice.dtos.get_purchase_reprt_dto import (
    GetPurchaseReportDto,
)
from finance_erp.common.schema.common import serialize_with_schema
from finance_erp.common.schema.purchase import PurchaseInvoiceResponseSchema
from object_registry import inject

logger = logging.getLogger(__name__)


class FetchPurchaseReportView(TreeboBaseAPI):
    @schema_wrapper_parser(GetPurchaseReportRequestSchema, param_type=RequestTypes.ARGS)
    @inject(command_handler=FetchPurchaseReportCommandHandler)
    def get(
        self,
        command_handler: FetchPurchaseReportCommandHandler,
        parsed_request,
        **kwargs
    ):
        request_dto = GetPurchaseReportDto(**parsed_request)
        data = command_handler.handle(request_dto)
        response = serialize_with_schema(
            PurchaseInvoiceResponseSchema, data=data, many=True
        )
        return {"data": response}
