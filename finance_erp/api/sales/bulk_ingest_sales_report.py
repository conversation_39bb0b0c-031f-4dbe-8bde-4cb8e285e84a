import logging

from core.common.api.treebo_api import TreeboBase<PERSON><PERSON>
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.application.invoice.command_handler.bulk_ingest_sales_report import (
    BulkIngestSalesReportCommandHandler,
)
from finance_erp.common.constants import NavisionReports
from finance_erp.common.decorators import session_manager
from finance_erp.common.schema.sales import SalesInvoiceIngestSchema
from object_registry import inject

logger = logging.getLogger(__name__)


class BulkIngestSalesReportView(TreeboBaseAPI):
    @session_manager(commit=True)
    @schema_wrapper_parser(SalesInvoiceIngestSchema, many=True)
    @inject(command_handler=BulkIngestSalesReportCommandHandler)
    def post(
        self,
        command_handler: BulkIngestSalesReportCommandHandler,
        parsed_request,
        **kwargs
    ):
        if self._async:
            response = command_handler.handle_async_execution_request(
                parsed_request, report_name=NavisionReports.CUSTOMER_INVOICE_REPORT
            )
        else:
            response = command_handler.handle(
                parsed_request, report_name=NavisionReports.CUSTOMER_INVOICE_REPORT
            )
        return {
            "data": response,
        }
