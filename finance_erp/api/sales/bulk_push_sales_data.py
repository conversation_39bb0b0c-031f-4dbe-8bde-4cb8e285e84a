import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.api.schema.request.sales.bulk_push_request import (
    BulkPushSalesReportRequestSchema,
)
from finance_erp.application.invoice.command_handler.push_sales_summary_report import (
    BulkPushSalesSummaryReportCommandHandler,
)
from finance_erp.application.invoice.dtos.bulk_sales_push_request_dto import (
    BulkSalesPushRequestDto,
)
from finance_erp.common.constants import NavisionReports
from object_registry import inject

logger = logging.getLogger(__name__)


class BulkPushSalesReportView(TreeboBaseAPI):
    @schema_wrapper_parser(BulkPushSalesReportRequestSchema)
    @inject(command_handler=BulkPushSalesSummaryReportCommandHandler)
    def post(
        self,
        command_handler: BulkPushSalesSummaryReportCommandHandler,
        parsed_request,
        **kwargs
    ):
        if self._async:
            response = command_handler.handle_async_execution_request(
                parsed_request,
                report_name=NavisionReports.CUSTOMER_INVOICE_SUMMARY_REPORT,
            )
        else:
            request_dto = BulkSalesPushRequestDto(**parsed_request)
            response = command_handler.handle(
                request_dto, report_name=NavisionReports.CUSTOMER_INVOICE_SUMMARY_REPORT
            )
        return {"data": response}
