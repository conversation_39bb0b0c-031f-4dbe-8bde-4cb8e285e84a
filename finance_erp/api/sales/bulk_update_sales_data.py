import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.application.invoice.command_handler.bulk_update_sales_record import (
    BulkUpdateSalesReportCommandHandler,
)
from finance_erp.common.decorators import session_manager
from finance_erp.common.schema.common import serialize_with_schema
from finance_erp.common.schema.sales import (
    SalesInvoiceResponseSchema,
    SalesInvoiceUpdateSchema,
)
from object_registry import inject

logger = logging.getLogger(__name__)


class BulkUpdateSalesReportView(TreeboBaseAPI):
    @session_manager(commit=True)
    @schema_wrapper_parser(SalesInvoiceUpdateSchema, many=True)
    @inject(command_handler=BulkUpdateSalesReportCommandHandler)
    def patch(
        self,
        command_handler: BulkUpdateSalesReportCommandHandler,
        parsed_request,
        **kwargs
    ):
        aggregates = command_handler.handle(parsed_request)
        response = serialize_with_schema(
            SalesInvoiceResponseSchema, data=aggregates, many=True
        )
        return {"data": response}
