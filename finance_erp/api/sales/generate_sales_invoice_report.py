import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.api.schema.request.sales.generate_sales_report import (
    GenerateSalesReportRequestSchema,
)
from finance_erp.application.invoice.command_handler.generate_sales_report import (
    SalesReportGeneratorCommandHandler,
)
from finance_erp.application.invoice.dtos.generate_sales_report_dto import (
    GenerateSalesReportDto,
)
from object_registry import inject

logger = logging.getLogger(__name__)


class GenerateSalesReportView(TreeboBaseAPI):
    @schema_wrapper_parser(GenerateSalesReportRequestSchema)
    @inject(command_handler=SalesReportGeneratorCommandHandler)
    def post(
        self,
        command_handler: SalesReportGeneratorCommandHandler,
        parsed_request,
        **kwargs,
    ):
        request_dto = GenerateSalesReportDto(**parsed_request)
        try:
            logger.info(f"Started sales report generation")
            return command_handler.handle(request_dto)
        except Exception as e:
            msg = f"Report generation push has failed with error: {e.args[0]}"
            logger.exception(msg)
            raise e
