import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.api.schema.request.sales.generate_sales_report import (
    GenerateSalesSummaryRequestSchema,
)
from finance_erp.application.common.dtos.data_generation_base_dto import (
    DataGenerationBaseDto,
)
from finance_erp.application.invoice.command_handler.generate_sales_summary import (
    SalesInvoiceSummaryGenerationHandler,
)
from object_registry import inject

logger = logging.getLogger(__name__)


class GenerateSalesSummaryView(TreeboBaseAPI):
    @schema_wrapper_parser(GenerateSalesSummaryRequestSchema)
    @inject(command_handler=SalesInvoiceSummaryGenerationHandler)
    def post(
        self,
        command_handler: SalesInvoiceSummaryGenerationHandler,
        parsed_request,
        **kwargs,
    ):
        if self._async:
            response = command_handler.handle_async_execution_request(parsed_request)
        else:
            request_dto = DataGenerationBaseDto(**parsed_request)
            response = command_handler.handle(request_dto)
        return {"data": response}
