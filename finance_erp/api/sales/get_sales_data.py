import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import RequestTypes, schema_wrapper_parser
from finance_erp.api.schema.request.sales.get_sales_data import (
    GetSalesReportRequestSchema,
)
from finance_erp.application.invoice.command_handler.get_sales_report import (
    FetchSalesReportCommandHandler,
)
from finance_erp.application.invoice.dtos.get_sales_reprt_dto import GetSalesReportDto
from finance_erp.common.schema.common import serialize_with_schema
from finance_erp.common.schema.sales import SalesInvoiceResponseSchema
from object_registry import inject

logger = logging.getLogger(__name__)


class FetchSalesReportView(TreeboBaseAPI):
    @schema_wrapper_parser(GetSalesReportRequestSchema, param_type=RequestTypes.ARGS)
    @inject(command_handler=FetchSalesReportCommandHandler)
    def get(
        self, command_handler: <PERSON>tchSalesReportCommandHandler, parsed_request, **kwargs
    ):
        request_dto = GetSalesReportDto(**parsed_request)
        data = command_handler.handle(request_dto)
        response = serialize_with_schema(
            SalesInvoiceResponseSchema, data=data, many=True
        )
        return {"data": response}
