from datetime import date
from typing import Literal, Optional

from pydantic import BaseModel, Field, field_validator
from treebo_commons.utils.dateutils import ymd_str_to_date

from finance_erp.domain.back_office.constants import IntegratedERPs

ErpNameType = Literal[IntegratedERPs.PROLOGIC, IntegratedERPs.BUSINESS_CENTRAL]


class GetLedgersFileRequestSchema(BaseModel):
    start_date: date
    end_date: date
    erp_name: ErpNameType = Field(default=IntegratedERPs.PROLOGIC)

    @field_validator("start_date", mode="before")
    def parse_start_date(cls, v):
        return ymd_str_to_date(v)

    @field_validator("end_date", mode="before")
    def parse_end_date(cls, v):
        return ymd_str_to_date(v)


class LedgersFileGenerationRequestSchema(BaseModel):
    date: date
    refresh_transaction_master: Optional[bool] = False
    erp_name: ErpNameType = Field(default=IntegratedERPs.PROLOGIC)

    @field_validator("date", mode="before")
    def parse_date(cls, v):
        if isinstance(v, str):
            return ymd_str_to_date(v)
        return v
