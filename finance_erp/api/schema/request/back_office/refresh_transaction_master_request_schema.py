from typing import Literal, Optional

from pydantic import BaseModel, Field

from finance_erp.domain.back_office.constants import IntegratedERPs

ErpNameType = Literal[IntegratedERPs.PROLOGIC, IntegratedERPs.BUSINESS_CENTRAL]


class RefreshTransactionMasterRequestSchema(BaseModel):
    hotel_id: str
    erp_name: Optional[str] = None


class GetRefreshTransactionMasterRequestSchema(BaseModel):
    hotel_id: str
    erp_name: ErpNameType = Field(default=IntegratedERPs.PROLOGIC)
