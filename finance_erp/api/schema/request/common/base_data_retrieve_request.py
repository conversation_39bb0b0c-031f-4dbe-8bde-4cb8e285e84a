from datetime import date
from typing import Optional

from pydantic import BaseModel, field_validator
from treebo_commons.utils.dateutils import ymd_str_to_date


class DataRetrieveBaseRequestSchema(BaseModel):
    from_date: Optional[date] = None
    to_date: Optional[date] = None

    @field_validator("from_date", mode="before")
    def parse_from_date(cls, v):
        if isinstance(v, str):
            return ymd_str_to_date(v)
        return v

    @field_validator("to_date", mode="before")
    def parse_to_date(cls, v):
        if isinstance(v, str):
            return ymd_str_to_date(v)
        return v

    class Config:
        validate_by_name = True
        from_attributes = True
