from datetime import date
from typing import List, Optional

from pydantic import BaseModel, EmailStr, field_validator, model_validator

from finance_erp.common.constants import InvoiceTypes


class DownloadInvoiceRequestSchema(BaseModel):
    start_date: Optional[date] = None
    end_date: Optional[date] = None
    booker_legal_entity_ids: Optional[List[str]] = None
    hotel_id: Optional[str] = None
    invoice_type: Optional[List[str]] = None
    fetch_credit_notes: bool = False
    user_email_id: EmailStr
    invoice_numbers: Optional[List[str]] = None
    booking_reference_numbers: Optional[List[str]] = None

    @field_validator("invoice_type", mode="before")
    def validate_invoice_type(cls, v):
        if v and any(item not in InvoiceTypes.all() for item in v):
            raise ValueError(f"{v} contains invalid invoice type(s)")
        return v

    @model_validator(mode="before")
    def check_required_fields(cls, values):
        ble_ids = values.get("booker_legal_entity_ids")
        invoice_nums = values.get("invoice_numbers")
        booking_refs = values.get("booking_reference_numbers")

        non_empty_count = sum(
            bool(field) for field in [ble_ids, invoice_nums, booking_refs]
        )

        if non_empty_count != 1:
            raise ValueError(
                "Exactly one of Booker Legal Entity IDs, Invoice Numbers or Booking Reference Numbers must be provided."
            )

        return values
