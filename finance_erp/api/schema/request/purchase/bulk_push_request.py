from typing import List, Optional

from pydantic import Field

from finance_erp.api.schema.request.common.base_data_push_request import (
    BulkPushBaseRequestSchema,
)


class BulkPushPurchaseReportRequestSchema(BulkPushBaseRequestSchema):
    unique_ref_ids: Optional[List[str]] = Field(default=None, alias="TransactionRefIds")

    class Config:
        validate_by_name = True
        from_attributes = True
