import logging

from core.common.api.treebo_api import TreeboBase<PERSON><PERSON>
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.application.settlement.expense.commands.bulk_ingest_expense_report import (
    BulkIngestExpenseReportCommandHandler,
)
from finance_erp.common.constants import NavisionReports
from finance_erp.common.decorators import session_manager
from finance_erp.common.schema.settlement.expense import ExpensePushSchema
from object_registry import inject

logger = logging.getLogger(__name__)


class BulkIngestExpenseReportView(TreeboBaseAPI):
    @session_manager(commit=True)
    @schema_wrapper_parser(ExpensePushSchema, many=True)
    @inject(command_handler=BulkIngestExpenseReportCommandHandler)
    def post(
        self,
        command_handler: BulkIngestExpenseReportCommandHandler,
        parsed_request,
        **kwargs
    ):
        if self._async:
            response = command_handler.handle_async_execution_request(
                parsed_request, report_name=NavisionReports.EXPENSE_REPORT
            )
        else:
            response = command_handler.handle(
                parsed_request, report_name=NavisionReports.EXPENSE_REPORT
            )
        return {"data": response}
