import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.api.schema.request.settlement.expense.bulk_push_request import (
    BulkPushExpenseReportRequestSchema,
)
from finance_erp.application.settlement.expense.commands.push_expense_report import (
    BulkPushExpenseReportCommandHandler,
)
from finance_erp.application.settlement.expense.dtos.bulk_expense_push_request_dto import (
    BulkExpensePushRequestDto,
)
from finance_erp.common.constants import NavisionReports
from object_registry import inject

logger = logging.getLogger(__name__)


class BulkPushExpenseReportView(TreeboBaseAPI):
    @schema_wrapper_parser(BulkPushExpenseReportRequestSchema)
    @inject(command_handler=BulkPushExpenseReportCommandHandler)
    def post(
        self,
        command_handler: BulkPushExpenseReportCommandHandler,
        parsed_request,
        **kwargs
    ):
        if self._async:
            response = command_handler.handle_async_execution_request(
                parsed_request, report_name=NavisionReports.EXPENSE_REPORT
            )
        else:
            request_dto = BulkExpensePushRequestDto(**parsed_request)
            response = command_handler.handle(
                request_dto, report_name=NavisionReports.EXPENSE_REPORT
            )
        return {"data": response}
