import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.application.settlement.expense.commands.bulk_update_expense_record import (
    BulkUpdateExpenseReportCommandHandler,
)
from finance_erp.common.decorators import session_manager
from finance_erp.common.schema.common import serialize_with_schema
from finance_erp.common.schema.settlement.expense import (
    ExpenseResponseSchema,
    ExpenseUpdateSchema,
)
from object_registry import inject

logger = logging.getLogger(__name__)


class BulkUpdateExpenseReportView(TreeboBaseAPI):
    @session_manager(commit=True)
    @schema_wrapper_parser(ExpenseUpdateSchema, many=True)
    @inject(command_handler=BulkUpdateExpenseReportCommandHandler)
    def patch(
        self,
        command_handler: BulkUpdateExpenseReportCommandHandler,
        parsed_request,
        **kwargs
    ):
        entities = command_handler.handle(parsed_request)
        response = serialize_with_schema(
            ExpenseResponseSchema, data=entities, many=True
        )
        return {"data": response}
