import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.api.schema.request.settlement.expense.generate_expense_schema import (
    GenerateExpenseReportResponseSchema,
)
from finance_erp.application.settlement.expense.commands.generate_expense_data import (
    ExpenseReportGeneratorCommandHandler,
)
from finance_erp.application.settlement.expense.dtos.generate_expense_report_dto import (
    GenerateExpenseReportDto,
)
from object_registry import inject

logger = logging.getLogger(__name__)


class GenerateExpenseReportView(TreeboBaseAPI):
    @schema_wrapper_parser(GenerateExpenseReportResponseSchema)
    @inject(command_handler=ExpenseReportGeneratorCommandHandler)
    def post(
        self,
        command_handler: ExpenseReportGeneratorCommandHandler,
        parsed_request,
        **kwargs,
    ):
        request_dto = GenerateExpenseReportDto(**parsed_request)
        try:
            logger.info(f"Started settlement expense report generation")
            return command_handler.handle(request_dto)
        except Exception as e:
            msg = f"Report push has failed with error: {e.args[0]}"
            logger.exception(msg)
            raise e
