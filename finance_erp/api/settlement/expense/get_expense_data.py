import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import RequestTypes, schema_wrapper_parser
from finance_erp.api.schema.request.settlement.expense.get_expense_data_schema import (
    GetExpenseReportRequestModel,
)
from finance_erp.application.settlement.expense.commands.get_expense_report import (
    FetchExpenseCommandHandler,
)
from finance_erp.application.settlement.expense.dtos.get_expense_report_dto import (
    GetExpenseReportDto,
)
from finance_erp.common.schema.common import serialize_with_schema
from finance_erp.common.schema.settlement.expense import ExpenseResponseSchema
from object_registry import inject

logger = logging.getLogger(__name__)


class FetchExpenseReportView(TreeboBaseAPI):
    @schema_wrapper_parser(GetExpenseReportRequestModel, param_type=RequestTypes.ARGS)
    @inject(command_handler=FetchExpenseCommandHandler)
    def get(
        self, command_handler: FetchExpenseCommandHandler, parsed_request, **kwargs
    ):
        request_dto = GetExpenseReportDto(**parsed_request)
        data = command_handler.handle(request_dto)
        response = serialize_with_schema(ExpenseResponseSchema, data=data, many=True)
        return {"data": response}
