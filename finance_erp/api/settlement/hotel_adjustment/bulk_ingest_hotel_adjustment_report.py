import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.application.settlement.hotel_adjustment.commands.bulk_ingest_hotel_adjustment_report import (
    BulkIngestHotelAdjustmentReportCommandHandler,
)
from finance_erp.common.constants import NavisionReports
from finance_erp.common.decorators import session_manager
from finance_erp.common.schema.settlement.hotel_adjustment import (
    HotelAdjustmentPushSchema,
)
from object_registry import inject

logger = logging.getLogger(__name__)


class BulkIngestHotelAdjustmentReportView(TreeboBaseAPI):
    @session_manager(commit=True)
    @schema_wrapper_parser(HotelAdjustmentPushSchema, many=True)
    @inject(command_handler=BulkIngestHotelAdjustmentReportCommandHandler)
    def post(
        self,
        command_handler: BulkIngestHotelAdjustmentReportCommandHandler,
        parsed_request,
        **kwargs
    ):
        print("parsed_request", parsed_request)
        if self._async:
            response = command_handler.handle_async_execution_request(
                parsed_request, report_name=NavisionReports.HOTEL_ADJUSTMENT_REPORT
            )
        else:
            response = command_handler.handle(
                parsed_request, report_name=NavisionReports.HOTEL_ADJUSTMENT_REPORT
            )
        return {"data": response}
