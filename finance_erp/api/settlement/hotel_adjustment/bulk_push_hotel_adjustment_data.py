import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.api.schema.request.settlement.hotel_adjustment.bulk_push_request import (
    BulkPushHotelAdjustmentReportRequestSchema,
)
from finance_erp.application.settlement.hotel_adjustment.commands.push_hotel_adjustment_report import (
    BulkPushHotelAdjustmentReportCommandHandler,
)
from finance_erp.application.settlement.hotel_adjustment.dtos.bulk_hotel_adjustment_push_request_dto import (
    BulkHotelAdjustmentPushRequestDto,
)
from finance_erp.common.constants import NavisionReports
from object_registry import inject

logger = logging.getLogger(__name__)


class BulkPushHotelAdjustmentReportView(TreeboBaseAPI):
    @schema_wrapper_parser(BulkPushHotelAdjustmentReportRequestSchema)
    @inject(command_handler=BulkPushHotelAdjustmentReportCommandHandler)
    def post(
        self,
        command_handler: BulkPushHotelAdjustmentReportCommandHandler,
        parsed_request,
        **kwargs
    ):
        if self._async:
            response = command_handler.handle_async_execution_request(
                parsed_request, report_name=NavisionReports.HOTEL_ADJUSTMENT_REPORT
            )
        else:
            request_dto = BulkHotelAdjustmentPushRequestDto(**parsed_request)
            response = command_handler.handle(
                request_dto, report_name=NavisionReports.HOTEL_ADJUSTMENT_REPORT
            )
        return {"data": response}
