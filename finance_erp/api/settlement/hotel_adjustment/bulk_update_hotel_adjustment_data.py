import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.application.settlement.hotel_adjustment.commands.bulk_update_hotel_adjustment_record import (
    BulkUpdateHotelAdjustmentReportCommandHandler,
)
from finance_erp.common.decorators import session_manager
from finance_erp.common.schema.common import serialize_with_schema
from finance_erp.common.schema.settlement.hotel_adjustment import (
    HotelAdjustmentResponseSchema,
    HotelAdjustmentUpdateSchema,
)
from object_registry import inject

logger = logging.getLogger(__name__)


class BulkUpdateHotelAdjustmentReportView(TreeboBaseAPI):
    @session_manager(commit=True)
    @schema_wrapper_parser(HotelAdjustmentUpdateSchema, many=True)
    @inject(command_handler=BulkUpdateHotelAdjustmentReportCommandHandler)
    def patch(
        self,
        command_handler: BulkUpdateHotelAdjustmentReportCommandHandler,
        parsed_request,
        **kwargs
    ):
        entities = command_handler.handle(parsed_request)
        response = serialize_with_schema(
            HotelAdjustmentResponseSchema, data=entities, many=True
        )
        return {"data": response}
