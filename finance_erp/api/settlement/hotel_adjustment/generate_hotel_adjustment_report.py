import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.api.schema.request.settlement.hotel_adjustment.generate_hotel_adjustment_schema import (
    GenerateHotelAdjustmentReportRequestSchema,
)
from finance_erp.application.settlement.hotel_adjustment.commands.generate_hotel_adjustment_data import (
    HotelAdjustmentReportGeneratorCommandHandler,
)
from finance_erp.application.settlement.hotel_adjustment.dtos.generate_hotel_adjustment_report_dto import (
    GenerateHotelAdjustmentReportDto,
)
from object_registry import inject

logger = logging.getLogger(__name__)


class GenerateHotelAdjustmentReportView(TreeboBaseAPI):
    @schema_wrapper_parser(GenerateHotelAdjustmentReportRequestSchema)
    @inject(command_handler=HotelAdjustmentReportGeneratorCommandHandler)
    def post(
        self,
        command_handler: HotelAdjustmentReportGenerator<PERSON>ommandHand<PERSON>,
        parsed_request,
        **kwargs,
    ):
        request_dto = GenerateHotelAdjustmentReportDto(**parsed_request)
        try:

            logger.info(f"Started settlement hotel adjustment report generation")
            return command_handler.handle(request_dto)
        except Exception as e:
            msg = f"Report push has failed with error: {e.args[0]}"
            logger.exception(msg)
            raise e
