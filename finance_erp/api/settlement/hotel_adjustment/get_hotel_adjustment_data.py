import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import RequestTypes, schema_wrapper_parser
from finance_erp.api.schema.request.settlement.hotel_adjustment.get_hotel_adjustment_data_schema import (
    GetHotelAdjustmentReportRequestSchema,
)
from finance_erp.application.settlement.hotel_adjustment.commands.get_hotel_adjustment_report import (
    FetchHotelAdjustmentCommandHandler,
)
from finance_erp.application.settlement.hotel_adjustment.dtos.get_hotel_adjustment_report_dto import (
    GetHotelAdjustmentReportDto,
)
from finance_erp.common.schema.common import serialize_with_schema
from finance_erp.common.schema.settlement.hotel_adjustment import (
    HotelAdjustmentResponseSchema,
)
from object_registry import inject

logger = logging.getLogger(__name__)


class FetchHotelAdjustmentReportView(TreeboBaseAPI):
    @schema_wrapper_parser(
        GetHotelAdjustmentReportRequestSchema, param_type=RequestTypes.ARGS
    )
    @inject(command_handler=FetchHotelAdjustmentCommandHandler)
    def get(
        self,
        command_handler: FetchHotelAdjustmentCommandHandler,
        parsed_request,
        **kwargs
    ):
        request_dto = GetHotelAdjustmentReportDto(**parsed_request)
        data = command_handler.handle(request_dto)
        response = serialize_with_schema(HotelAdjustmentResponseSchema, data, many=True)
        return {"data": response}
