import logging

from core.common.api.treebo_api import TreeboBase<PERSON><PERSON>
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.application.settlement.loan.commands.bulk_ingest_loan_report import (
    BulkIngestLoanReportCommandHandler,
)
from finance_erp.common.constants import NavisionReports
from finance_erp.common.decorators import session_manager
from finance_erp.common.schema.settlement.loan import LoanPushSchema
from object_registry import inject

logger = logging.getLogger(__name__)


class BulkIngestLoanReportView(TreeboBaseAPI):
    @session_manager(commit=True)
    @schema_wrapper_parser(LoanPushSchema, many=True)
    @inject(command_handler=BulkIngestLoanReportCommandHandler)
    def post(
        self,
        command_handler: BulkIngestLoanReportCommandHandler,
        parsed_request,
        **kwargs
    ):
        if self._async:
            response = command_handler.handle_async_execution_request(
                parsed_request, report_name=NavisionReports.LOAN_REPORT
            )
        else:
            response = command_handler.handle(
                parsed_request, report_name=NavisionReports.LOAN_REPORT
            )
        return {"data": response}
