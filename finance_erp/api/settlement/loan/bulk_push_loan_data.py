import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.api.schema.request.settlement.loan.bulk_push_request import (
    BulkPushLoanReportRequestSchema,
)
from finance_erp.application.settlement.loan.commands.push_loan_report import (
    BulkPushLoanReportCommandHandler,
)
from finance_erp.application.settlement.loan.dtos.bulk_loan_push_request_dto import (
    BulkLoanPushRequestDto,
)
from finance_erp.common.constants import NavisionReports
from object_registry import inject

logger = logging.getLogger(__name__)


class BulkPushLoanReportView(TreeboBaseAPI):
    @schema_wrapper_parser(BulkPushLoanReportRequestSchema)
    @inject(command_handler=BulkPushLoanReportCommandHandler)
    def post(
        self,
        command_handler: BulkPushLoanReportCommandHandler,
        parsed_request,
        **kwargs
    ):
        if self._async:
            response = command_handler.handle_async_execution_request(
                parsed_request, report_name=NavisionReports.LOAN_REPORT
            )
        else:
            request_dto = BulkLoanPushRequestDto(**parsed_request)
            response = command_handler.handle(
                request_dto, report_name=NavisionReports.LOAN_REPORT
            )
        return {"data": response}
