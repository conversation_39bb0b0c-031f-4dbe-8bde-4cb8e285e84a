import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.application.settlement.loan.commands.bulk_update_loan_record import (
    BulkUpdateLoanReportCommandHandler,
)
from finance_erp.common.decorators import session_manager
from finance_erp.common.schema.common import serialize_with_schema
from finance_erp.common.schema.settlement.loan import (
    LoanResponseSchema,
    LoanUpdateSchema,
)
from object_registry import inject

logger = logging.getLogger(__name__)


class BulkUpdateLoanReportView(TreeboBaseAPI):
    @session_manager(commit=True)
    @schema_wrapper_parser(LoanUpdateSchema, many=True)
    @inject(command_handler=BulkUpdateLoanReportCommandHandler)
    def patch(
        self,
        command_handler: BulkUpdateLoanReportCommandHandler,
        parsed_request,
        **kwargs
    ):
        entities = command_handler.handle(parsed_request)
        response = serialize_with_schema(LoanResponseSchema, data=entities, many=True)
        return {"data": response}
