import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.api.schema.request.settlement.loan.generate_loan_schema import (
    GenerateLoanReportRequestSchema,
)
from finance_erp.application.settlement.loan.commands.generate_loan_data import (
    LoanReportGeneratorCommandHandler,
)
from finance_erp.application.settlement.loan.dtos.generate_loan_report_dto import (
    GenerateLoanReportDto,
)
from object_registry import inject

logger = logging.getLogger(__name__)


class GenerateLoanReportView(TreeboBaseAPI):
    @schema_wrapper_parser(GenerateLoanReportRequestSchema)
    @inject(command_handler=LoanReportGeneratorCommandHandler)
    def post(
        self,
        command_handler: LoanReportGeneratorCommandHandler,
        parsed_request,
        **kwargs,
    ):
        request_dto = GenerateLoanReportDto(**parsed_request)
        try:

            logger.info(f"Started settlement loan report generation")
            return command_handler.handle(request_dto)
        except Exception as e:
            msg = f"Report push has failed with error: {e.args[0]}"
            logger.exception(msg)
            raise e
