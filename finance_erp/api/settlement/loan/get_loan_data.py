import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import RequestTypes, schema_wrapper_parser
from finance_erp.api.schema.request.settlement.loan.get_loan_data_schema import (
    GetLoanReportRequestSchema,
)
from finance_erp.application.settlement.loan.commands.get_loan_report import (
    FetchLoanCommandHandler,
)
from finance_erp.application.settlement.loan.dtos.get_loan_report_dto import (
    GetLoanReportDto,
)
from finance_erp.common.schema.common import serialize_with_schema
from finance_erp.common.schema.settlement.loan import LoanResponseSchema
from object_registry import inject

logger = logging.getLogger(__name__)


class FetchLoanReportView(TreeboBaseAPI):
    @schema_wrapper_parser(GetLoanReportRequestSchema, param_type=RequestTypes.ARGS)
    @inject(command_handler=FetchLoanCommandHandler)
    def get(self, command_handler: <PERSON>tchLoanCommandHand<PERSON>, parsed_request, **kwargs):
        request_dto = GetLoanReportDto(**parsed_request)
        data = command_handler.handle(request_dto)
        response = serialize_with_schema(LoanResponseSchema, data=data, many=True)
        return {"data": response}
