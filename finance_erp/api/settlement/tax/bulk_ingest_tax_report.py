import logging

from core.common.api.treebo_api import TreeboBase<PERSON><PERSON>
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.application.settlement.tax.commands.bulk_ingest_tax_report import (
    BulkIngestTaxReportCommandHandler,
)
from finance_erp.common.constants import NavisionReports
from finance_erp.common.decorators import session_manager
from finance_erp.common.schema.settlement.tax import TaxPushSchema
from object_registry import inject

logger = logging.getLogger(__name__)


class BulkIngestTaxReportView(TreeboBaseAPI):
    @session_manager(commit=True)
    @schema_wrapper_parser(TaxPushSchema, many=True)
    @inject(command_handler=BulkIngestTaxReportCommandHandler)
    def post(
        self,
        command_handler: BulkIngestTaxReportCommandHandler,
        parsed_request,
        **kwargs
    ):
        if self._async:
            response = command_handler.handle_async_execution_request(
                parsed_request, report_name=NavisionReports.TAX_REPORT
            )
        else:
            response = command_handler.handle(
                parsed_request, report_name=NavisionReports.TAX_REPORT
            )
        return {"data": response}
