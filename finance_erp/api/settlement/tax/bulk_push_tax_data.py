import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.api.schema.request.settlement.tax.bulk_push_request import (
    BulkPushTaxReportRequestSchema,
)
from finance_erp.application.settlement.tax.commands.push_tax_report import (
    BulkPushTaxReportCommandHandler,
)
from finance_erp.application.settlement.tax.dtos.bulk_tax_push_request_dto import (
    BulkTaxPushRequestDto,
)
from finance_erp.common.constants import NavisionReports
from object_registry import inject

logger = logging.getLogger(__name__)


class BulkPushTaxReportView(TreeboBaseAPI):
    @schema_wrapper_parser(BulkPushTaxReportRequestSchema)
    @inject(command_handler=BulkPushTaxReportCommandHandler)
    def post(
        self, command_handler: BulkPushTaxReportCommandHandler, parsed_request, **kwargs
    ):
        if self._async:
            response = command_handler.handle_async_execution_request(
                parsed_request, report_name=NavisionReports.TAX_REPORT
            )
        else:
            request_dto = BulkTaxPushRequestDto(**parsed_request)
            response = command_handler.handle(
                request_dto, report_name=NavisionReports.TAX_REPORT
            )
        return {"data": response}
