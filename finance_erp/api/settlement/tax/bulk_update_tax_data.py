import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.application.settlement.tax.commands.bulk_update_tax_record import (
    BulkUpdateTaxReportCommandHandler,
)
from finance_erp.common.decorators import session_manager
from finance_erp.common.schema.common import serialize_with_schema
from finance_erp.common.schema.settlement.tax import TaxResponseSchema, TaxUpdateSchema
from object_registry import inject

logger = logging.getLogger(__name__)


class BulkUpdateTaxReportView(TreeboBaseAPI):
    @session_manager(commit=True)
    @schema_wrapper_parser(TaxUpdateSchema, many=True)
    @inject(command_handler=BulkUpdateTaxReportCommandHandler)
    def patch(
        self,
        command_handler: BulkUpdateTaxReportCommandHandler,
        parsed_request,
        **kwargs
    ):
        entities = command_handler.handle(parsed_request)
        response = serialize_with_schema(TaxResponseSchema, data=entities, many=True)
        return {"data": response}
