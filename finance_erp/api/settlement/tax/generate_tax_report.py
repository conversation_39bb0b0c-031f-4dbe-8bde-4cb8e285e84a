import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.api.schema.request.settlement.tax.generate_tax_schema import (
    GenerateTaxReportRequestSchema,
)
from finance_erp.application.settlement.tax.commands.generate_tax_data import (
    TaxReportGeneratorCommandHandler,
)
from finance_erp.application.settlement.tax.dtos.generate_tax_report_dto import (
    GenerateTaxReportDto,
)
from object_registry import inject

logger = logging.getLogger(__name__)


class GenerateTaxReportView(TreeboBaseAPI):
    @schema_wrapper_parser(GenerateTaxReportRequestSchema)
    @inject(command_handler=TaxReportGeneratorCommandHandler)
    def post(
        self,
        command_handler: TaxReportGeneratorCommandHandler,
        parsed_request,
        **kwargs,
    ):
        request_dto = GenerateTaxReportDto(**parsed_request)
        try:

            logger.info(f"Started settlement tax report generation")
            return command_handler.handle(request_dto)
        except Exception as e:
            msg = f"Report push has failed with error: {e.args[0]}"
            logger.exception(msg)
            raise e
