import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import RequestTypes, schema_wrapper_parser
from finance_erp.api.schema.request.settlement.tax.get_tax_data_schema import (
    GetTaxReportRequestSchema,
)
from finance_erp.application.settlement.tax.commands.get_tax_report import (
    FetchTaxCommandHandler,
)
from finance_erp.application.settlement.tax.dtos.get_tax_report_dto import (
    GetTaxReportDto,
)
from finance_erp.common.schema.common import serialize_with_schema
from finance_erp.common.schema.settlement.tax import TaxResponseSchema
from object_registry import inject

logger = logging.getLogger(__name__)


class FetchTaxReportView(TreeboBaseAPI):
    @schema_wrapper_parser(GetTaxReportRequestSchema, param_type=RequestTypes.ARGS)
    @inject(command_handler=FetchTaxCommandHandler)
    def get(self, command_handler: FetchTaxCommandHand<PERSON>, parsed_request, **kwargs):
        request_dto = GetTaxReportDto(**parsed_request)
        data = command_handler.handle(request_dto)
        response = serialize_with_schema(TaxResponseSchema, data=data, many=True)
        return {"data": response}
