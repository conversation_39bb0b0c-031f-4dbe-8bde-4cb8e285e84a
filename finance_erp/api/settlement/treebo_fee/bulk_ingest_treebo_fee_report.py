import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.application.settlement.treebo_fee.commands.bulk_ingest_treebo_fee_report import (
    BulkIngestTreeboFeeReportCommandHandler,
)
from finance_erp.common.constants import NavisionReports
from finance_erp.common.decorators import session_manager
from finance_erp.common.schema.settlement.treebo_fee import TreeboFeePushSchema
from object_registry import inject

logger = logging.getLogger(__name__)


class BulkIngestTreeboFeeReportView(TreeboBaseAPI):
    @session_manager(commit=True)
    @schema_wrapper_parser(TreeboFeePushSchema, many=True)
    @inject(command_handler=BulkIngestTreeboFeeReportCommandHandler)
    def post(
        self,
        command_handler: BulkIngestTreeboFeeReportCommandHandler,
        parsed_request,
        **kwargs
    ):
        if self._async:
            response = command_handler.handle_async_execution_request(
                parsed_request, report_name=NavisionReports.TREEBO_FEE_REPORT
            )
        else:
            response = command_handler.handle(
                parsed_request, report_name=NavisionReports.TREEBO_FEE_REPORT
            )
        return {"data": response}
