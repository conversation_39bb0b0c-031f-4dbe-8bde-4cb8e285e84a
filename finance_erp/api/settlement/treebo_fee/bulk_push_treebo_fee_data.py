import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.api.schema.request.settlement.treebo_fee.bulk_push_request import (
    BulkPushTreeboFeeReportRequestSchema,
)
from finance_erp.application.settlement.treebo_fee.commands.push_treebo_fee_report import (
    BulkPushTreeboFeeReportCommandHandler,
)
from finance_erp.application.settlement.treebo_fee.dtos.bulk_treebo_fee_push_request_dto import (
    BulkTreeboFeePushRequestDto,
)
from finance_erp.common.constants import NavisionReports
from object_registry import inject

logger = logging.getLogger(__name__)


class BulkPushTreeboFeeReportView(TreeboBaseAPI):
    @schema_wrapper_parser(BulkPushTreeboFeeReportRequestSchema)
    @inject(command_handler=BulkPushTreeboFeeReportCommandHandler)
    def post(
        self,
        command_handler: BulkPushTreeboFeeReportCommandHandler,
        parsed_request,
        **kwargs
    ):
        if self._async:
            response = command_handler.handle_async_execution_request(
                parsed_request, report_name=NavisionReports.TREEBO_FEE_REPORT
            )
        else:
            request_dto = BulkTreeboFeePushRequestDto(**parsed_request)
            response = command_handler.handle(
                request_dto, report_name=NavisionReports.TREEBO_FEE_REPORT
            )
        return {"data": response}
