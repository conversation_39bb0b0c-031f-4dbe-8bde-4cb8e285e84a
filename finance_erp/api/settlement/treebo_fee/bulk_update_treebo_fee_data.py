import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.application.settlement.treebo_fee.commands.bulk_update_treebo_fee_record import (
    BulkUpdateTreeboFeeReportCommandHandler,
)
from finance_erp.common.decorators import session_manager
from finance_erp.common.schema.common import serialize_with_schema
from finance_erp.common.schema.settlement.treebo_fee import (
    TreeboFeeResponseSchema,
    TreeboFeeUpdateSchema,
)
from object_registry import inject

logger = logging.getLogger(__name__)


class BulkUpdateTreeboFeeReportView(TreeboBaseAPI):
    @session_manager(commit=True)
    @schema_wrapper_parser(TreeboFeeUpdateSchema, many=True)
    @inject(command_handler=BulkUpdateTreeboFeeReportCommandHandler)
    def patch(
        self,
        command_handler: BulkUpdateTreeboFeeReportCommandHand<PERSON>,
        parsed_request,
        **kwargs
    ):
        entities = command_handler.handle(parsed_request)
        response = serialize_with_schema(
            TreeboFeeResponseSchema, data=entities, many=True
        )
        return {"data": response}
