import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.api.schema.request.settlement.treebo_fee.generate_treebo_fee_schema import (
    GenerateTreeboFeeReportRequestSchema,
)
from finance_erp.application.settlement.treebo_fee.commands.generate_treebo_fee_data import (
    TreeboFeeReportGeneratorCommandHandler,
)
from finance_erp.application.settlement.treebo_fee.dtos.generate_treebo_fee_report_dto import (
    GenerateTreeboFeeReportDto,
)
from object_registry import inject

logger = logging.getLogger(__name__)


class GenerateTreeboFeeReportView(TreeboBaseAPI):
    @schema_wrapper_parser(GenerateTreeboFeeReportRequestSchema)
    @inject(command_handler=TreeboFeeReportGeneratorCommandHandler)
    def post(
        self,
        command_handler: TreeboFeeReportGeneratorCommandHandler,
        parsed_request,
        **kwargs,
    ):
        request_dto = GenerateTreeboFeeReportDto(**parsed_request)
        try:

            logger.info(f"Started settlement treebo fee report generation")
            return command_handler.handle(request_dto)
        except Exception as e:
            msg = f"Report push has failed with error: {e.args[0]}"
            logger.exception(msg)
            raise e
