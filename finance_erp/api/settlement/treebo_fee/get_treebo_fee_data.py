import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import RequestTypes, schema_wrapper_parser
from finance_erp.api.schema.request.settlement.treebo_fee.get_treebo_fee_data_schema import (
    GetTreeboFeeReportRequestSchema,
)
from finance_erp.application.settlement.treebo_fee.commands.get_treebo_fee_report import (
    FetchTreeboFeeCommandHandler,
)
from finance_erp.application.settlement.treebo_fee.dtos.get_treebo_fee_report_dto import (
    GetTreeboFeeReportDto,
)
from finance_erp.common.schema.common import serialize_with_schema
from finance_erp.common.schema.settlement.treebo_fee import TreeboFeeResponseSchema
from object_registry import inject

logger = logging.getLogger(__name__)


class FetchTreeboFeeReportView(TreeboBaseAPI):
    @schema_wrapper_parser(
        GetTreeboFeeReportRequestSchema, param_type=RequestTypes.ARGS
    )
    @inject(command_handler=FetchTreeboFeeCommandHandler)
    def get(
        self, command_handler: FetchTreeboFeeCommandHandler, parsed_request, **kwargs
    ):
        request_dto = GetTreeboFeeReportDto(**parsed_request)
        data = command_handler.handle(request_dto)
        response = serialize_with_schema(TreeboFeeResponseSchema, data=data, many=True)
        return {"data": response}
