import logging

from core.common.api.treebo_api import TreeboBase<PERSON><PERSON>
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.application.ta_commission.commands.bulk_ingest_ta_commission_report import (
    BulkIngestTACommissionReportCommandHandler,
)
from finance_erp.common.constants import NavisionReports
from finance_erp.common.decorators import session_manager
from finance_erp.common.schema.ta_commission import TACommissionBaseSchema
from object_registry import inject

logger = logging.getLogger(__name__)


class BulkIngestTACommissionReportView(TreeboBaseAPI):
    @session_manager(commit=True)
    @schema_wrapper_parser(TACommissionBaseSchema, many=True)
    @inject(command_handler=BulkIngestTACommissionReportCommandHandler)
    def post(
        self,
        command_handler: BulkIngestTACommissionReportCommandHandler,
        parsed_request,
        **kwargs
    ):
        if self._async:
            response = command_handler.handle_async_execution_request(
                parsed_request, report_name=NavisionReports.TA_COMMISSION_REPORT
            )
        else:
            response = command_handler.handle(
                parsed_request, report_name=NavisionReports.TA_COMMISSION_REPORT
            )
        return {"data": response}
