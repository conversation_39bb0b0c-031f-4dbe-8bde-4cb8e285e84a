import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.api.schema.request.ta_commission.bulk_push_request import (
    BulkPushTAReportRequestSchema,
)
from finance_erp.application.ta_commission.commands.push_ta_commission_report import (
    BulkPushTACommissionReportCommandHandler,
)
from finance_erp.application.ta_commission.dtos.bulk_ta_commission_push_request_dto import (
    BulkTACommissionPushRequestDto,
)
from finance_erp.common.constants import NavisionReports
from object_registry import inject

logger = logging.getLogger(__name__)


class BulkPushTACommissionReportView(TreeboBaseAPI):
    @schema_wrapper_parser(BulkPushTAReportRequestSchema)
    @inject(command_handler=BulkPushTACommissionReportCommandHandler)
    def post(
        self,
        command_handler: BulkPushTACommissionReportCommandHandler,
        parsed_request,
        **kwargs
    ):
        if self._async:
            response = command_handler.handle_async_execution_request(
                parsed_request, report_name=NavisionReports.TA_COMMISSION_REPORT
            )
        else:
            request_dto = BulkTACommissionPushRequestDto(**parsed_request)
            response = command_handler.handle(
                request_dto, report_name=NavisionReports.TA_COMMISSION_REPORT
            )
        return {"data": response}
