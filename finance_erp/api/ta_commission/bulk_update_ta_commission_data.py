import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.application.ta_commission.commands.bulk_update_ta_commission_record import (
    BulkUpdateTACommissionReportCommandHandler,
)
from finance_erp.common.decorators import session_manager
from finance_erp.common.schema.common import serialize_with_schema
from finance_erp.common.schema.ta_commission import (
    TACommissionResponseSchema,
    TACommissionUpdateSchema,
)
from object_registry import inject

logger = logging.getLogger(__name__)


class BulkUpdateTACommissionReportView(TreeboBaseAPI):
    @session_manager(commit=True)
    @schema_wrapper_parser(TACommissionUpdateSchema, many=True)
    @inject(command_handler=BulkUpdate<PERSON>CommissionReportCommandHandler)
    def patch(
        self,
        command_handler: BulkUpdateTACommissionReportCommandHandler,
        parsed_request,
        **kwargs
    ):
        aggregates = command_handler.handle(parsed_request)
        response = serialize_with_schema(
            TACommissionResponseSchema, data=aggregates, many=True
        )
        return {"data": response}
