import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.api.schema.request.ta_commission.generate_ta_commission_schema import (
    GenerateTACommissionReportRequestSchema,
)
from finance_erp.application.ta_commission.commands.generate_ta_commission_data import (
    TACommissionReportGeneratorCommandHandler,
)
from finance_erp.application.ta_commission.dtos.generate_ta_commission_report_dto import (
    GenerateTACommissionReportDto,
)
from object_registry import inject

logger = logging.getLogger(__name__)


class GenerateTACommissionReportView(TreeboBaseAPI):
    @schema_wrapper_parser(GenerateTACommissionReportRequestSchema)
    @inject(command_handler=TACommissionReportGeneratorCommandHandler)
    def post(
        self,
        command_handler: TACommissionReportGeneratorCommandHandler,
        parsed_request,
        **kwargs,
    ):
        request_dto = GenerateTACommissionReportDto(**parsed_request)
        try:
            logger.info(f"Started ta commission report generation")
            return command_handler.handle(request_dto)
        except Exception as e:
            msg = f"Report push has failed with error: {e.args[0]}"
            logger.exception(msg)
            raise e
