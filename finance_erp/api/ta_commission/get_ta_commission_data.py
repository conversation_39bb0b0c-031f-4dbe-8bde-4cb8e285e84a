import logging

from core.common.api.treebo_api import TreeboBaseAP<PERSON>
from core.common.utils.request_parser import RequestTypes, schema_wrapper_parser
from finance_erp.api.schema.request.ta_commission.get_ta_commission_data_schema import (
    GetTACommissionReportRequestSchema,
)
from finance_erp.application.ta_commission.commands.get_ta_commission_report import (
    FetchTACommissionsCommandHandler,
)
from finance_erp.application.ta_commission.dtos.get_ta_commission_report_dto import (
    GetTACommissionReportDto,
)
from finance_erp.common.schema.common import serialize_with_schema
from finance_erp.common.schema.ta_commission import TACommissionResponseSchema
from object_registry import inject

logger = logging.getLogger(__name__)


class FetchTACommissionReportView(TreeboBaseAPI):
    @schema_wrapper_parser(
        GetTACommissionReportRequestSchema, param_type=RequestTypes.ARGS
    )
    @inject(command_handler=FetchTACommissionsCommandHandler)
    def get(
        self,
        command_handler: FetchTACommissionsCommandHandler,
        parsed_request,
        **kwargs
    ):
        request_dto = GetTACommissionReportDto(**parsed_request)
        data = command_handler.handle(request_dto)
        response = serialize_with_schema(
            TACommissionResponseSchema, data=data, many=True
        )
        return {"data": response}
