from ths_common.utils.collectionutils import chunks
from treebo_commons.utils import dateutils

from finance_erp.application.common.resilient_ingestion_handler import (
    ResilientDataIngestionCommandHandler,
)
from finance_erp.application.hotel_settings.tenant_settings import TenantSettings
from finance_erp.async_job.job_scheduler_service import JobSchedulerService
from finance_erp.common.constants import IngestionJobs
from finance_erp.domain.back_office.factory.allowance_factory import AllowanceFactory
from finance_erp.domain.back_office.factory.charge_factory import ChargeFactory
from finance_erp.domain.back_office.factory.folio_details_factory import (
    FolioDetailsFactory,
)
from finance_erp.domain.back_office.factory.payment_factory import PaymentFactory
from finance_erp.domain.back_office.repository.allowance_repository import (
    AllowanceRepository,
)
from finance_erp.domain.back_office.repository.charge_repository import ChargeRepository
from finance_erp.domain.back_office.repository.folio_details_repository import (
    FolioDetailsRepository,
)
from finance_erp.domain.back_office.repository.payment_repository import (
    PaymentRepository,
)
from object_registry import register_instance


@register_instance(
    dependencies=[
        AllowanceRepository,
        ChargeRepository,
        PaymentRepository,
        FolioDetailsRepository,
        TenantSettings,
        JobSchedulerService,
    ]
)
class BulkIngestFinancialDataCommandHandler(ResilientDataIngestionCommandHandler):
    CHUNK_SIZE = 1000

    def __init__(
        self,
        allowance_repository: AllowanceRepository,
        charge_repository: ChargeRepository,
        payment_repository: PaymentRepository,
        folio_details_repository: FolioDetailsRepository,
        tenant_settings: TenantSettings,
        job_scheduler: JobSchedulerService,
    ):
        super().__init__(IngestionJobs.BACKOFFICE_FINANCIAL_DATA_INGESTION)
        self.allowance_repository = allowance_repository
        self.charge_repository = charge_repository
        self.payment_repository = payment_repository
        self.folio_details_repository = folio_details_repository
        self.tenant_settings = tenant_settings
        self.job_scheduler = job_scheduler

    def _execute(self, request_data, event_id, from_async_job=False):
        self._ingest(request_data)

    def _ingest(self, data):
        """
        If data is present for existing date mark it deleted in bulk.
        Ingest financial data in chunks and record the ingestion event.
        """

        self._remove_any_existing_data(data)
        posting_date = dateutils.ymd_str_to_date(data["date"])

        self._ingest_details(
            data.get("allowance_details"),
            posting_date,
            AllowanceFactory,
            self.allowance_repository,
        )
        self._ingest_details(
            data.get("charge_details"),
            posting_date,
            ChargeFactory,
            self.charge_repository,
        )
        self._ingest_details(
            data.get("payment_details"),
            posting_date,
            PaymentFactory,
            self.payment_repository,
        )
        self._ingest_details(
            data.get("folio_details"),
            posting_date,
            FolioDetailsFactory,
            self.folio_details_repository,
        )
        self._trigger_backoffice_data_preparation(data)

    def _trigger_backoffice_data_preparation(self, data):
        hotel_id = data.get("hotel_id")
        date = data.get("date")
        erps = self.tenant_settings.get_enabled_backoffice_erps(hotel_id)
        for erp in erps:
            self.job_scheduler.schedule_backoffice_ledger_generation_job(
                date=str(date),
                hotel_id=hotel_id,
                refresh_transaction_master=True,
                erp_name=erp,
            )

    def _remove_any_existing_data(self, data):
        date = data.get("date")
        hotel_id = data.get("hotel_id")
        allowance_details = self.allowance_repository.fetch(hotel_id, posting_date=date)
        if allowance_details:
            for allowance_detail in allowance_details:
                allowance_detail.mark_as_deleted()
            self.allowance_repository.update_many(allowance_details)

        charge_details = self.charge_repository.fetch(hotel_id, posting_date=date)
        if charge_details:
            for charge_detail in charge_details:
                charge_detail.mark_as_deleted()
            self.charge_repository.update_many(charge_details)

        folio_details = self.folio_details_repository.fetch(hotel_id, posting_date=date)
        if folio_details:
            for folio_detail in folio_details:
                folio_detail.mark_as_deleted()
            self.folio_details_repository.update_many(folio_details)

        payment_details = self.payment_repository.fetch(hotel_id, posting_date=date)
        if payment_details:
            for payment_detail in payment_details:
                payment_detail.mark_as_deleted()
            self.payment_repository.update_many(payment_details)

    def _ingest_details(self, details, posting_date, factory, repository):
        """
        Ingest specific details (allowance, charge, or payment) into the repository.
        """
        if not details:
            return []

        for data_list in chunks(details, self.CHUNK_SIZE):
            data_to_insert = [
                factory.create_data_from_dict(data, posting_date) for data in data_list
            ]
            repository.insert_many(data_to_insert)
