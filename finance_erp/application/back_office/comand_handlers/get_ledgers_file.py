from finance_erp.application.back_office.dtos.ledgers_dto import GetLedgersFileDto
from finance_erp.domain.back_office.repository.ledgers_file import LedgersFileRepository
from finance_erp.infrastructure.external_clients.aws_service_client import (
    AwsServiceClient,
)
from object_registry import register_instance


@register_instance(dependencies=[LedgersFileRepository])
class FetchLedgersFileCommandHandler:
    def __init__(self, ledgers_file_repository: LedgersFileRepository):
        self.ledgers_file_repository = ledgers_file_repository

    def handle(self, input_criteria: GetLedgersFileDto):
        ledgers_record = self.ledgers_file_repository.get_file_details_for_date_range(
            input_criteria.hotel_id,
            input_criteria.start_date,
            input_criteria.end_date,
            input_criteria.erp_name,
        )
        ledgers_record = self._refresh_ledger_files_url_for(ledgers_record)
        return ledgers_record

    @staticmethod
    def _refresh_ledger_files_url_for(ledgers_record):
        for ledger_record in ledgers_record:
            if ledger_record.ledger_file_path:
                ledger_record.ledger_file_path = (
                    AwsServiceClient.get_presigned_url_from_s3_url(
                        ledger_record.ledger_file_path, 3600
                    )
                )
        return ledgers_record
