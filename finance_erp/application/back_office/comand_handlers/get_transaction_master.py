from typing import List, Optional

from finance_erp.application.back_office.dtos.transaction_master_dto import (
    TransactionMasterDetailsSchema,
    TransactionMasterDto,
)
from finance_erp.domain.back_office.repository.transaction_master import (
    TransactionMasterRepository,
)
from finance_erp.infrastructure.external_clients.catalog_service_client import (
    CatalogServiceClient,
)
from object_registry import register_instance


@register_instance(dependencies=[TransactionMasterRepository, CatalogServiceClient])
class FetchTransactionMasterCommandHandler:
    def __init__(
        self,
        transaction_master_repository: TransactionMasterRepository,
        catalog_service_client: CatalogServiceClient,
    ):
        self.transaction_master_repository = transaction_master_repository
        self.catalog_service_client = catalog_service_client

    def handle(
        self, input_criteria: TransactionMasterDto
    ) -> List[TransactionMasterDetailsSchema]:
        transaction_master_details = self.transaction_master_repository.load_all(
            input_criteria.hotel_id,
            input_criteria.erp_name,
        )
        sub_code_details = (
            self.catalog_service_client.get_transaction_master_sub_code_details(
                input_criteria.hotel_id
            )
        )
        return [
            TransactionMasterDetailsSchema(
                code=tm_detail.transaction_id,
                sub_code=sub_code,
                name=tm_detail.identifier_name,
            )
            for tm_detail in transaction_master_details
            if (
                sub_code := self._determine_sub_code(
                    tm_detail.transaction_type, sub_code_details
                )
            )
            is not None
        ]

    @staticmethod
    def _determine_sub_code(
        transaction_type: str, sub_code_details: dict
    ) -> Optional[str]:
        return sub_code_details.get("transaction_types", {}).get(transaction_type)
