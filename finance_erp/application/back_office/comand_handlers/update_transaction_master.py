import logging
from collections import defaultdict
from typing import List

from finance_erp.application.back_office.dtos.transaction_master_dto import (
    TransactionMasterDto,
)
from finance_erp.domain.back_office.constants import (
    FRONT_DESK,
    AccountMasterTypes,
    GLGroupCodes,
    GLRevenueTypes,
    IntegratedERPs,
    RoomLevelSkuDetails,
)
from finance_erp.domain.back_office.entity.transaction_master import (
    TransactionMasterEntity,
)
from finance_erp.domain.back_office.repository.transaction_master import (
    TransactionMasterRepository,
)
from finance_erp.domain.back_office.value_objects import TransactionMetaData
from finance_erp.infrastructure.external_clients.catalog_service_client import (
    CatalogServiceClient,
)
from finance_erp.infrastructure.external_clients.interface_exchange_service_client import (
    InterfaceExchangeServiceClient,
)
from finance_erp.infrastructure.external_clients.taxation_service_client import (
    TaxationServiceClient,
)
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        TransactionMasterRepository,
        InterfaceExchangeServiceClient,
        CatalogServiceClient,
        TaxationServiceClient,
    ]
)
class UpdateTransactionMasterCommandHandler:
    def __init__(
        self,
        transaction_master_repository: TransactionMasterRepository,
        interface_exchange_service_client: InterfaceExchangeServiceClient,
        catalog_service_client: CatalogServiceClient,
        taxation_service_client: TaxationServiceClient,
    ):
        self.transaction_master_repository = transaction_master_repository
        self.interface_exchange_service_client = interface_exchange_service_client
        self.catalog_service_client = catalog_service_client
        self.taxation_service_client = taxation_service_client

    def handle(self, request_data: TransactionMasterDto):
        mapped_interfaces = self._load_mapped_interfaces(request_data.hotel_id)
        payment_entries, refund_entries = self._get_payment_refund_txn_entries(
            request_data, mapped_interfaces
        )
        charge_entries, allowance_entries = self._get_charge_allowance_txn_entries(
            request_data, mapped_interfaces
        )

        fundamental_txn_entries = self._get_fundamental_txn_entries(request_data)

        all_new_txn_entries = (
            payment_entries
            + refund_entries
            + charge_entries
            + allowance_entries
            + fundamental_txn_entries
        )

        existing_txn_entries = self.transaction_master_repository.load_all(
            request_data.hotel_id, request_data.erp_name
        )
        existing_txn_entry_identifier_name_txn_mapping = {
            txn.identifier_name: txn for txn in existing_txn_entries
        }
        txn_master_to_upsert = []

        for txn in all_new_txn_entries:
            if (
                txn.identifier_name
                in existing_txn_entry_identifier_name_txn_mapping.keys()
            ):
                existing_txn: TransactionMasterEntity = (
                    existing_txn_entry_identifier_name_txn_mapping[txn.identifier_name]
                )
                existing_txn.update(txn)
                txn = existing_txn
            txn_master_to_upsert.append(txn)

        self.transaction_master_repository.create_or_update_records_bulk(
            txn_master_to_upsert
        )
        return txn_master_to_upsert

    def _get_payment_refund_txn_entries(self, request_data, mapped_interfaces):
        (
            pos_payment_txn_entries,
            pos_refund_txn_entries,
        ) = self._get_pos_payment_refund_entries(request_data, mapped_interfaces)
        (
            front_office_payment_txn_entries,
            front_office_refund_txn_entries,
        ) = self._get_front_office_payment_refund_entries(request_data)

        payment_txn_entries = pos_payment_txn_entries + front_office_payment_txn_entries
        refund_txn_entries = pos_refund_txn_entries + front_office_refund_txn_entries

        return payment_txn_entries, refund_txn_entries

    def _get_pos_payment_refund_entries(self, request_data, mapped_interfaces):
        return self._generate_payment_txn_entries_for_external_pos(
            request_data, mapped_interfaces, GLRevenueTypes.PAYMENT
        ), self._generate_payment_txn_entries_for_external_pos(
            request_data, mapped_interfaces, GLRevenueTypes.REFUND
        )

    def _generate_payment_txn_entries_for_external_pos(
        self, request_data, mapped_interfaces, transaction_type: str
    ) -> List[TransactionMasterEntity]:
        txn_entries = []
        for interface in mapped_interfaces:
            for revenue_center in interface.revenue_center_mappings:
                for payment in revenue_center.payment_mappings:
                    payment_mode, sub_type = (payment.internal_id.split("#") + [None])[
                        :2
                    ]
                    name = self._generate_name_for_txn_entry(
                        transaction_type=transaction_type,
                        payment_mode=payment_mode,
                        sub_type=sub_type,
                        revenue_center=revenue_center.internal_id,
                    )
                    txn_entries.append(
                        TransactionMasterEntity(
                            hotel_id=request_data.hotel_id,
                            erp_name=request_data.erp_name,
                            revenue_center=revenue_center.internal_id,
                            transaction_type=transaction_type,
                            identifier_name=name,
                            display_name=name,
                            identifier=payment_mode,
                            transaction_metadata=TransactionMetaData(
                                payment_mode_sub_type=sub_type
                            )
                            if sub_type
                            else None,
                        )
                    )
        return txn_entries

    def _get_front_office_payment_refund_entries(self, request_data):
        fo_enum_name_value_mapping = self._get_enum_name_value_mapping(request_data)
        fo_payment_modes = fo_enum_name_value_mapping["payment_mode"]
        fo_refund_modes = fo_enum_name_value_mapping["refund_mode"]

        return self._generate_payment_txn_entries_for_front_office(
            request_data,
            fo_enum_name_value_mapping,
            fo_payment_modes,
            GLRevenueTypes.PAYMENT,
        ), self._generate_payment_txn_entries_for_front_office(
            request_data,
            fo_enum_name_value_mapping,
            fo_refund_modes,
            GLRevenueTypes.REFUND,
        )

    def _get_enum_name_value_mapping(self, request_data):
        fo_enum_name_value_mapping = {}
        front_office_enums = self._load_front_office_enums(request_data.hotel_id)
        for enum in front_office_enums:
            fo_enum_name_value_mapping[enum.get("enum_name")] = enum.get("enum_values")
        return fo_enum_name_value_mapping

    def _generate_payment_txn_entries_for_front_office(
        self,
        request_data,
        fo_enum_name_value_mapping,
        fo_payment_modes,
        transaction_type: str,
    ):
        txn_entries = []
        for pay_mode in fo_payment_modes:
            sub_types = fo_enum_name_value_mapping.get(pay_mode)
            if sub_types:
                for sub_type in sub_types:
                    txn_entries.append(
                        self._create_front_office_payment_entry(
                            request_data, pay_mode, sub_type, transaction_type
                        )
                    )
            else:
                txn_entries.append(
                    self._create_front_office_payment_entry(
                        request_data, pay_mode, None, transaction_type
                    )
                )
        return txn_entries

    def _create_front_office_payment_entry(
        self, request_data, payment_mode, sub_type, transaction_type
    ):
        metadata = (
            TransactionMetaData(payment_mode_sub_type=sub_type) if sub_type else None
        )
        name = self._generate_name_for_txn_entry(
            transaction_type=transaction_type,
            payment_mode=payment_mode,
            sub_type=sub_type,
            revenue_center=FRONT_DESK,
        )

        return TransactionMasterEntity(
            hotel_id=request_data.hotel_id,
            erp_name=request_data.erp_name,
            revenue_center=FRONT_DESK,
            transaction_type=transaction_type,
            identifier_name=name,
            display_name=name,
            identifier=payment_mode,
            transaction_metadata=metadata,
        )

    def _get_charge_allowance_txn_entries(self, request_data, mapped_interfaces):
        property_skus = self._load_property_skus(request_data.hotel_id)
        tax_configs = self._load_tax_configs(request_data.hotel_id)
        (
            pos_charge_txn_entries,
            pos_allowance_txn_entries,
        ) = self._get_external_pos_charge_allowance_entries(
            request_data, property_skus, tax_configs, mapped_interfaces
        )
        (
            front_office_charge_txn_entries,
            front_office_allowance_txn_entries,
        ) = self._get_front_office_charge_allowance_entries(
            request_data, property_skus, tax_configs
        )
        (
            sh_pos_charge_txn_entries,
            sh_pos_allowance_txn_entries,
        ) = self._get_sh_pos_charge_allowance_entries(request_data, tax_configs)

        charge_txn_entries = (
            pos_charge_txn_entries
            + front_office_charge_txn_entries
            + sh_pos_charge_txn_entries
        )
        allowance_txn_entries = (
            pos_allowance_txn_entries
            + front_office_allowance_txn_entries
            + sh_pos_allowance_txn_entries
        )

        return charge_txn_entries, allowance_txn_entries

    def _get_external_pos_charge_allowance_entries(
        self, request_data, property_skus, tax_configs, mapped_interfaces
    ):
        return self._generate_revenue_txn_entries_for_external_pos(
            request_data,
            property_skus,
            tax_configs,
            mapped_interfaces,
            GLRevenueTypes.CHARGE,
        ), self._generate_revenue_txn_entries_for_external_pos(
            request_data,
            property_skus,
            tax_configs,
            mapped_interfaces,
            GLRevenueTypes.ALLOWANCE,
        )

    def _generate_revenue_txn_entries_for_external_pos(
        self,
        request_data,
        property_skus,
        tax_configs,
        mapped_interfaces,
        transaction_type: str,
    ) -> List[TransactionMasterEntity]:
        sku_code_to_name_mapping = {
            sku.expense_item_id: sku.name for sku in property_skus
        }
        txn_entries = []
        sku_category_tax_configs_map = self._get_sku_category_tax_configs_map(
            tax_configs
        )
        for interface in mapped_interfaces:
            for revenue_center in interface.revenue_center_mappings:
                for sku in revenue_center.sku_mappings:
                    sku_name = sku_code_to_name_mapping.get(sku.internal_id)
                    if not sku_name:
                        continue
                    txn_entries.append(
                        TransactionMasterEntity(
                            hotel_id=request_data.hotel_id,
                            erp_name=request_data.erp_name,
                            revenue_center=revenue_center.internal_id,
                            transaction_type=transaction_type,
                            identifier_name=self._generate_name_for_txn_entry(
                                transaction_type=transaction_type,
                                sku_category=sku.sku_category,
                                sku_name=sku_name,
                                sku_internal_id=sku.internal_id,
                                revenue_center=revenue_center.internal_id,
                            ),
                            display_name=self._generate_name_for_txn_entry(
                                transaction_type=transaction_type,
                                sku_category=sku.sku_category,
                                sku_name=sku_name,
                                sku_internal_id=sku.internal_id,
                                revenue_center=revenue_center.internal_id,
                                is_display_name=True,
                            ),
                            identifier=sku.internal_id,
                            transaction_metadata=TransactionMetaData(
                                sku_category=sku.sku_category
                            ),
                        )
                    )
                    tax_configs_for_sku = sku_category_tax_configs_map.get(
                        sku.sku_category, []
                    )
                    tax_transaction_type = (
                        GLRevenueTypes.TAX_ON_ALLOWANCE
                        if transaction_type == GLRevenueTypes.ALLOWANCE
                        else GLRevenueTypes.TAX_ON_CHARGE
                    )
                    for tax in tax_configs_for_sku:
                        txn_entries.append(
                            TransactionMasterEntity(
                                hotel_id=request_data.hotel_id,
                                erp_name=request_data.erp_name,
                                revenue_center=revenue_center.internal_id,
                                transaction_type=tax_transaction_type,
                                identifier_name=self._generate_name_for_txn_entry(
                                    transaction_type=tax_transaction_type,
                                    sku_category=sku.sku_category,
                                    sku_name=sku_name,
                                    sku_internal_id=sku.internal_id,
                                    revenue_center=revenue_center.internal_id,
                                    tax_code=tax.tax_code,
                                    tax_type=tax.tax_type,
                                    tax_value=tax.tax_value,
                                ),
                                display_name=self._generate_name_for_txn_entry(
                                    transaction_type=tax_transaction_type,
                                    sku_category=sku.sku_category,
                                    sku_name=sku_name,
                                    sku_internal_id=sku.internal_id,
                                    revenue_center=revenue_center.internal_id,
                                    tax_code=tax.tax_code,
                                    tax_type=tax.tax_type,
                                    tax_value=tax.tax_value,
                                    is_display_name=True,
                                ),
                                identifier=sku.internal_id,
                                transaction_metadata=TransactionMetaData(
                                    sku_category=sku.sku_category,
                                    tax_code=tax.tax_code,
                                    tax_value=tax.tax_value,
                                    tax_type=tax.tax_type,
                                ),
                            )
                        )
        return txn_entries

    def _get_front_office_charge_allowance_entries(
        self, request_data, property_skus, tax_configs
    ):
        return self._generate_revenue_txn_entries_for_front_office(
            request_data,
            property_skus,
            tax_configs,
            GLRevenueTypes.CHARGE,
            revenue_center=FRONT_DESK,
        ) + self._generate_txn_entries_for_room_revenue(
            tax_configs, request_data, GLRevenueTypes.CHARGE
        ), self._generate_revenue_txn_entries_for_front_office(
            request_data,
            property_skus,
            tax_configs,
            GLRevenueTypes.ALLOWANCE,
            revenue_center=FRONT_DESK,
        ) + self._generate_txn_entries_for_room_revenue(
            tax_configs, request_data, GLRevenueTypes.ALLOWANCE
        )

    def _get_sh_pos_charge_allowance_entries(self, request_data, tax_configs):
        sh_sellers = self._load_property_sellers(request_data.hotel_id)
        charge_txn_entries = []
        allowance_txn_entries = []
        for seller in sh_sellers:
            seller_skus = self._load_seller_skus(seller.seller_id)
            charge_entries = self._generate_revenue_txn_entries_for_front_office(
                request_data,
                seller_skus,
                tax_configs,
                GLRevenueTypes.CHARGE,
                revenue_center=seller.name,
            )
            charge_txn_entries.extend(charge_entries)
            allowance_entries = self._generate_revenue_txn_entries_for_front_office(
                request_data,
                seller_skus,
                tax_configs,
                GLRevenueTypes.ALLOWANCE,
                revenue_center=seller.name,
            )
            allowance_txn_entries.extend(allowance_entries)
        return charge_txn_entries, allowance_txn_entries

    def _generate_revenue_txn_entries_for_front_office(
        self,
        request_data,
        skus_data,
        tax_configs,
        transaction_type: str,
        revenue_center: str,
    ) -> List[TransactionMasterEntity]:
        txn_entries = []
        sku_category_tax_configs_map = self._get_sku_category_tax_configs_map(
            tax_configs
        )
        for sku in skus_data:
            txn_entries.append(
                TransactionMasterEntity(
                    hotel_id=request_data.hotel_id,
                    erp_name=request_data.erp_name,
                    revenue_center=revenue_center,
                    transaction_type=transaction_type,
                    identifier_name=self._generate_name_for_txn_entry(
                        transaction_type=transaction_type,
                        sku_category=sku.sku_category,
                        sku_name=sku.name,
                        sku_internal_id=sku.expense_item_id,
                        revenue_center=revenue_center,
                    ),
                    display_name=self._generate_name_for_txn_entry(
                        transaction_type=transaction_type,
                        sku_category=sku.sku_category,
                        sku_name=sku.name,
                        sku_internal_id=sku.expense_item_id,
                        revenue_center=revenue_center,
                        is_display_name=True,
                    ),
                    identifier=sku.expense_item_id,
                    transaction_metadata=TransactionMetaData(
                        sku_category=sku.sku_category
                    ),
                )
            )
            tax_configs_for_sku = sku_category_tax_configs_map.get(sku.sku_category, [])
            tax_transaction_type = (
                GLRevenueTypes.TAX_ON_ALLOWANCE
                if transaction_type == GLRevenueTypes.ALLOWANCE
                else GLRevenueTypes.TAX_ON_CHARGE
            )
            for tax in tax_configs_for_sku:
                txn_entries.append(
                    TransactionMasterEntity(
                        hotel_id=request_data.hotel_id,
                        erp_name=request_data.erp_name,
                        revenue_center=revenue_center,
                        transaction_type=tax_transaction_type,
                        identifier_name=self._generate_name_for_txn_entry(
                            transaction_type=tax_transaction_type,
                            sku_category=sku.sku_category,
                            sku_name=sku.name,
                            sku_internal_id=sku.expense_item_id,
                            revenue_center=revenue_center,
                            tax_code=tax.tax_code,
                            tax_type=tax.tax_type,
                            tax_value=tax.tax_value,
                        ),
                        display_name=self._generate_name_for_txn_entry(
                            transaction_type=tax_transaction_type,
                            sku_category=sku.sku_category,
                            sku_name=sku.name,
                            sku_internal_id=sku.expense_item_id,
                            revenue_center=revenue_center,
                            tax_code=tax.tax_code,
                            tax_type=tax.tax_type,
                            tax_value=tax.tax_value,
                            is_display_name=True,
                        ),
                        identifier=sku.expense_item_id,
                        transaction_metadata=TransactionMetaData(
                            sku_category=sku.sku_category,
                            tax_code=tax.tax_code,
                            tax_value=tax.tax_value,
                            tax_type=tax.tax_type,
                        ),
                    )
                )
        return txn_entries

    def _generate_txn_entries_for_room_revenue(
        self, tax_configs, request_data, transaction_type
    ):
        sku_category_tax_configs_map = self._get_sku_category_tax_configs_map(
            tax_configs
        )
        sku_category, sku_name, sku_code = (
            RoomLevelSkuDetails["sku_category"],
            RoomLevelSkuDetails["sku_name"],
            RoomLevelSkuDetails["sku_code"],
        )
        txn_entries = [
            TransactionMasterEntity(
                hotel_id=request_data.hotel_id,
                erp_name=request_data.erp_name,
                revenue_center=FRONT_DESK,
                transaction_type=transaction_type,
                identifier_name=self._generate_name_for_txn_entry(
                    transaction_type=transaction_type,
                    sku_category=sku_category,
                    sku_name=sku_name,
                    sku_internal_id=sku_code,
                    revenue_center=FRONT_DESK,
                ),
                display_name=self._generate_name_for_txn_entry(
                    transaction_type=transaction_type,
                    sku_category=sku_category,
                    sku_name=sku_name,
                    sku_internal_id=sku_code,
                    revenue_center=FRONT_DESK,
                    is_display_name=True,
                ),
                identifier=sku_code,
                transaction_metadata=TransactionMetaData(
                    sku_category=sku_category,
                ),
            )
        ]
        tax_configs_for_sku = sku_category_tax_configs_map.get(sku_category, [])
        tax_transaction_type = (
            GLRevenueTypes.TAX_ON_ALLOWANCE
            if transaction_type == GLRevenueTypes.ALLOWANCE
            else GLRevenueTypes.TAX_ON_CHARGE
        )
        for tax in tax_configs_for_sku:
            txn_entries.append(
                TransactionMasterEntity(
                    hotel_id=request_data.hotel_id,
                    erp_name=request_data.erp_name,
                    revenue_center=FRONT_DESK,
                    transaction_type=tax_transaction_type,
                    identifier_name=self._generate_name_for_txn_entry(
                        transaction_type=tax_transaction_type,
                        sku_category=sku_category,
                        sku_name=sku_name,
                        sku_internal_id=sku_code,
                        revenue_center=FRONT_DESK,
                        tax_code=tax.tax_code,
                        tax_type=tax.tax_type,
                        tax_value=tax.tax_value,
                    ),
                    display_name=self._generate_name_for_txn_entry(
                        transaction_type=tax_transaction_type,
                        sku_category=sku_category,
                        sku_name=sku_name,
                        sku_internal_id=sku_code,
                        revenue_center=FRONT_DESK,
                        tax_code=tax.tax_code,
                        tax_type=tax.tax_type,
                        tax_value=tax.tax_value,
                        is_display_name=True,
                    ),
                    identifier=sku_code,
                    transaction_metadata=TransactionMetaData(
                        sku_category=sku_category,
                        tax_code=tax.tax_code,
                        tax_value=tax.tax_value,
                        tax_type=tax.tax_type,
                    ),
                )
            )
        return txn_entries

    @staticmethod
    def _get_fundamental_txn_entries(request_data):
        return [
            TransactionMasterEntity(
                hotel_id=request_data.hotel_id,
                erp_name=IntegratedERPs.PROLOGIC,
                identifier_name=f"Customer Advance",
                display_name=f"Customer Advance",
                revenue_center=FRONT_DESK,
                identifier=AccountMasterTypes.CUSTOMER_ADVANCE,
                transaction_type=GLRevenueTypes.PAYMENT,
            ),
            TransactionMasterEntity(
                hotel_id=request_data.hotel_id,
                erp_name=IntegratedERPs.PROLOGIC,
                identifier_name=f"Aggregated GL guest control GL2",
                display_name=f"Aggregated GL guest control GL2",
                revenue_center=FRONT_DESK,
                identifier=GLGroupCodes.GUEST_GROUP_CODE_AGGREGATE,
                transaction_type=GLRevenueTypes.MASTER_AGGREGATES,
            ),
            TransactionMasterEntity(
                hotel_id=request_data.hotel_id,
                erp_name=IntegratedERPs.PROLOGIC,
                identifier_name=f"Aggregated GL guest control GL6",
                display_name=f"Aggregated GL guest control GL6",
                revenue_center=FRONT_DESK,
                identifier=GLGroupCodes.DEBTOR_GROUP_CODE_AGGREGATE,
                transaction_type=GLRevenueTypes.MASTER_AGGREGATES,
            ),
            TransactionMasterEntity(
                hotel_id=request_data.hotel_id,
                erp_name=IntegratedERPs.PROLOGIC,
                identifier_name=f"Aggregated GL guest control GL8",
                display_name=f"Aggregated GL guest control GL8",
                revenue_center=FRONT_DESK,
                identifier=GLGroupCodes.DEPOSIT_GROUP_CODE_AGGREGATE,
                transaction_type=GLRevenueTypes.MASTER_AGGREGATES,
            ),
        ]

    def _load_mapped_interfaces(self, hotel_id):
        return self.interface_exchange_service_client.get_interfaces(
            hotel_id, include_mappings=True
        )

    def _load_front_office_enums(self, hotel_id):
        return self.catalog_service_client.get_enums(property_id=hotel_id)

    def _load_tax_configs(self, hotel_id):
        return self.taxation_service_client.get_tax_configs(property_id=hotel_id)

    def _load_property_skus(self, hotel_id):
        return self.catalog_service_client.get_property_skus(
            property_id=hotel_id, for_inclusions=True
        )

    def _load_property_sellers(self, hotel_id):
        return self.catalog_service_client.fetch_seller(property_id=hotel_id)

    def _load_seller_skus(self, seller_id):
        return self.catalog_service_client.get_seller_skus(seller_id=seller_id)

    @staticmethod
    def _get_sku_category_tax_configs_map(tax_configs):
        sku_category_tax_configs_mapping = defaultdict(lambda: [])
        for tax in tax_configs:
            sku_category_tax_configs_mapping[tax.sku_category].append(tax)
        return sku_category_tax_configs_mapping

    @staticmethod
    def _generate_name_for_txn_entry(
        transaction_type: str,
        payment_mode: str = None,
        sub_type: str = None,
        sku_category: str = None,
        sku_name: str = None,
        sku_internal_id: str = None,
        revenue_center: str = None,
        tax_code: str = None,
        tax_type: str = None,
        tax_value: str = None,
        is_display_name: bool = False,
    ) -> str:
        name_parts = []
        if transaction_type in [GLRevenueTypes.PAYMENT, GLRevenueTypes.REFUND]:
            name_parts = [transaction_type, payment_mode, sub_type, revenue_center]
        else:
            name_prefix = (
                GLRevenueTypes.ALLOWANCE
                if transaction_type
                in [GLRevenueTypes.ALLOWANCE, GLRevenueTypes.TAX_ON_ALLOWANCE]
                else None
            )
            name_parts = [
                name_prefix,
                sku_category,
                sku_name if is_display_name else None,
                sku_internal_id,
                revenue_center,
            ]
            if tax_code and tax_type and tax_value:
                name_parts.extend([tax_code, tax_type, tax_value])

        return " - ".join(map(str, filter(None, name_parts)))
