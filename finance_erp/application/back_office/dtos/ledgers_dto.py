from datetime import date

from finance_erp.domain.back_office.constants import IntegratedERPs


class GetLedgersFileDto:
    def __init__(
        self,
        start_date=None,
        end_date=None,
        hotel_id=None,
        erp_name=IntegratedERPs.PROLOGIC,
    ):
        self.start_date = start_date
        self.end_date = end_date
        self.hotel_id = hotel_id
        self.erp_name = erp_name


class GenerateLedgersFileDto:
    def __init__(
        self,
        hotel_id: str,
        date: date,
        refresh_transaction_master: bool = False,
        erp_name: str = IntegratedERPs.PROLOGIC,
    ):
        self.hotel_id = hotel_id
        self.date = date
        self.refresh_transaction_master = refresh_transaction_master
        self.erp_name = erp_name
