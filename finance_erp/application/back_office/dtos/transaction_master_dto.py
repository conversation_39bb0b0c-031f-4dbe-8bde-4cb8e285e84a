from typing import Optional

from pydantic import BaseModel, field_validator

from finance_erp.domain.back_office.constants import IntegratedERPs


class TransactionMasterDto:
    def __init__(self, hotel_id=None, erp_name=IntegratedERPs.PROLOGIC):
        self.hotel_id = hotel_id
        self.erp_name = erp_name


class TransactionMasterDetailsSchema(BaseModel):
    code: Optional[str] = None
    sub_code: Optional[str] = None
    name: Optional[str] = None

    @field_validator("code", mode="before")
    def parse_code(cls, code):
        if isinstance(code, int):
            return str(code)
        return code
