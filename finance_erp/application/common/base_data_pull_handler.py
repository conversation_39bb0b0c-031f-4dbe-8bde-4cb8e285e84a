import logging
import traceback

from treebo_commons.request_tracing.context import get_current_request_id

from finance_erp.common.decorators import session_manager
from finance_erp.domain.shared_kernel.audit.process_level.data_pull_audit_service import (
    DataPullAuditService,
)
from object_registry import locate_instance, register_instance

logger = logging.getLogger(__name__)


@register_instance(dependencies=[DataPullAuditService])
class BaseDataPullCommandHandler:
    def __init__(self, data_pull_audit_service: DataPullAuditService):
        self.data_pull_audit_service = data_pull_audit_service

    def handle(self, pull_request_data, report_name, event_id=None):
        try:
            event_id = event_id if event_id else get_current_request_id()
            pull_request_data = vars(pull_request_data)
            self._record_received_data_pull_event(
                pull_request_data, report_name, event_id
            )

            self.data_pull_audit_service.record_data_pull_started_event(
                event_id, report_name
            )
            data = self._pull_and_ingest_data(pull_request_data, report_name, event_id)
            self.data_pull_audit_service.record_data_pull_success_event(
                event_id, report_name
            )
            return data
        except Exception as e:
            logger.exception(e)
            message = f"{report_name} Ingestion failed: {str(e)}"
            self.data_pull_audit_service.record_data_pull_failure_event(
                event_id, report_name, message, str(traceback.format_exc())
            )
            raise

    def _record_received_data_pull_event(
        self, pull_request_data, report_name, event_id
    ):
        stats = pull_request_data
        self.data_pull_audit_service.record_incoming_data_pull_event(
            event_id, report_name, stats=stats
        )

    def _pull_and_ingest_data(self, request_dto, report_name, event_id):
        raise NotImplementedError()

    def _get_request_parser(self):
        raise NotImplementedError()

    # override this method in concrete classes if required
    def _parse_job_data(self, request_data):
        return request_data

    def handle_request_from_job_executor(
        self, request_data, report_name, event_id=None, **kwargs
    ):
        request_dto = self._get_request_parser()(**request_data)
        request_dto.is_manual_push = False
        self.handle(self._parse_job_data(request_dto), report_name, event_id)

    @staticmethod
    @session_manager(commit=True)
    def handle_async_execution_request(request_dto, report_name, event_id=None):
        event_id = event_id if event_id else get_current_request_id()
        from finance_erp.async_job.job_scheduler_service import JobSchedulerService

        job = locate_instance(JobSchedulerService).create_data_pull_job(
            report_name, request_dto, event_id
        )
        return dict(job_id=job.job_id)
