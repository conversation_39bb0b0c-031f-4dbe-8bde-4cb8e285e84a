import logging
import traceback

from treebo_commons.request_tracing.context import get_current_request_id

from finance_erp.domain.shared_kernel.audit.process_level.ingestion_audit_service import (
    IngestionAuditService,
)
from object_registry import locate_instance, register_instance

logger = logging.getLogger(__name__)


@register_instance(dependencies=[IngestionAuditService])
class BaseIngestionCommandHandler:
    def __init__(self, ingestion_audit_service: IngestionAuditService):
        self.ingestion_audit_service = ingestion_audit_service

    def handle(self, ingestion_data_list, report_name, event_id=None):
        try:
            is_retry = event_id is not None
            event_id = event_id if event_id else get_current_request_id()
            self._record_received_report_ingestion_event(
                ingestion_data_list, report_name, event_id, is_retry
            )

            self.ingestion_audit_service.record_ingestion_started_event(
                event_id, report_name
            )
            data = self._run_ingestion(ingestion_data_list, report_name, event_id)
            self.ingestion_audit_service.record_ingestion_success_event(
                event_id, report_name
            )
            return data
        except Exception as e:
            logger.exception(e)
            message = f"{report_name} Ingestion failed: {str(e)}"
            self.ingestion_audit_service.record_ingestion_failure_event(
                event_id, report_name, message, str(traceback.format_exc())
            )
            raise

    def _record_received_report_ingestion_event(
        self, ingestion_data_list, report_name, event_id, is_retry=False
    ):
        stats = dict(
            records_received=self._get_unique_ids(ingestion_data_list),
            no_of_records=len(ingestion_data_list),
        )
        if is_retry:
            self.ingestion_audit_service.record_ingestion_retry_event(
                event_id, report_name, stats=stats
            )
        else:
            self.ingestion_audit_service.record_incoming_ingestion_event(
                event_id, report_name, stats=stats
            )

    def _run_ingestion(self, ingestion_data_list, report_name, event_id):
        raise NotImplementedError()

    def _get_unique_ids(self, ingestion_data_list):
        raise NotImplementedError()

    # override this method in concrete classes if required
    def _parse_job_data(self, request_data):
        return request_data

    def handle_request_from_job_executor(
        self, request_data, report_name, event_id=None, **kwargs
    ):
        self.handle(self._parse_job_data(request_data), report_name, event_id)

    @staticmethod
    def handle_async_execution_request(request_data, report_name, event_id=None):
        event_id = event_id if event_id else get_current_request_id()
        from finance_erp.async_job.job_scheduler_service import JobSchedulerService

        job = locate_instance(JobSchedulerService).create_data_ingestion_job(
            report_name, request_data, event_id
        )
        return dict(job_id=job.job_id)
