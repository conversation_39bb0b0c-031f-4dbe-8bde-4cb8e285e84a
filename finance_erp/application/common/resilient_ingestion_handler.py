import logging

from treebo_commons.request_tracing.context import get_current_request_id

from finance_erp.common.decorators import session_manager
from object_registry import locate_instance, register_instance

logger = logging.getLogger(__name__)


@register_instance()
class ResilientDataIngestionCommandHandler:
    def __init__(self, ingestion_job_name=None):
        self.ingestion_job_name = ingestion_job_name

    @session_manager(commit=True)
    def handle(
        self,
        request_data,
        event_id=None,
        should_retry=True,
    ):
        try:
            event_id = event_id if event_id else get_current_request_id()
            return self._execute(request_data, event_id)
        except Exception as e:
            logger.exception(e)
            if should_retry:
                return self._schedule_for_async_execution(request_data, event_id)
            raise

    def _execute(self, request_data, event_id, from_async_job=False):
        raise NotImplementedError()

    def handle_request_from_job_executor(
        self,
        request_data,
        event_id=None,
        **kwargs,
    ):
        try:
            event_id = event_id if event_id else get_current_request_id()
            return self._execute(request_data, event_id, from_async_job=True)
        except Exception as e:
            logger.error(e)
            raise e

    @session_manager(commit=True)
    def handle_async_execution_request(
        self,
        request_data,
        event_id=None,
    ):
        return self._schedule_for_async_execution(request_data, event_id)

    def _schedule_for_async_execution(
        self,
        request_data,
        event_id=None,
    ):
        event_id = event_id if event_id else get_current_request_id()
        from finance_erp.async_job.job_scheduler_service import JobSchedulerService

        job = locate_instance(JobSchedulerService).create_data_ingestion_job(
            self.ingestion_job_name,
            request_data,
            event_id,
            is_resilient=True,
        )
        return dict(job_id=job.job_id)
