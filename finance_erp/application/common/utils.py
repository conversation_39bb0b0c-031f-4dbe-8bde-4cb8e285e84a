import json
import os

from finance_erp.common.constants import CountryCode


def sanitize_string(string, length=False, only_alpha=False):
    if not string:
        return string
    if length:
        string = string[0:length]
    if only_alpha:
        string = "".join([char for char in string if char.isalpha()])
    return string


def get_state_code(state):
    dir_path = os.path.dirname(os.path.realpath(__file__))
    with open(f"{dir_path}/state_code_map.json") as file:
        state_code_map = json.load(file)
        for key, value in state_code_map.items():
            if state.replace(" ", "").lower() in key.replace(" ", "").lower():
                return value


def get_state_code_from_gstin(gstin):
    dir_path = os.path.dirname(os.path.realpath(__file__))
    with open(f"/{dir_path}/gstin_state_code_map.json") as file:
        gstin_state_code_map = json.load(file)
        return gstin_state_code_map[gstin]


def get_country_code(country):
    return CountryCode.INDIA.value if country.lower() in "INDIA".lower() else None
