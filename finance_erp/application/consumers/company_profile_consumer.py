import logging

from flask import current_app
from flask.cli import with_appcontext
from treebo_commons.multitenancy.tenant_client import TenantClient

from finance_erp.application.corporate.command_handler.ingest_corporate_from_cp import (
    CPCorporateDataIngestionHandler,
)
from finance_erp.common.decorators import consumer_middleware
from finance_erp.infrastructure.consumers.base_consumer import BaseRMQConsumer
from finance_erp.infrastructure.consumers.consumer_config import (
    CompanyProfileConsumerConfig,
)

logger = logging.getLogger(__name__)


class CompanyProfileEventType(object):
    SUB_ENTITY_CREATED = "SubEntityCreated"
    SUB_ENTITY_UPDATED = "SubEntityUpdated"


class CompanyProfileConsumer(BaseRMQConsumer):
    def __init__(
        self,
        company_profile_ingestion_handler: CPCorporateDataIngestionHandler,
        tenant_id=TenantClient.get_default_tenant(),
    ):
        super().__init__(CompanyProfileConsumerConfig(tenant_id))
        self.tenant_id = tenant_id
        logger.info(
            "Listening to RMQ on host: %s from queues: %s",
            self.connection,
            [queue.name for queue in self.queues],
        )
        self.company_profile_ingestion_handler = company_profile_ingestion_handler

    @consumer_middleware
    @with_appcontext
    def process_message(self, body, message):
        message.ack()
        logger.info(f"Received Company Profile Event with body {body}. Message Acked")

        with current_app.test_request_context():
            try:
                event = body.get("events")
                event_type = body.get("event_type")
                if event_type not in {
                    CompanyProfileEventType.SUB_ENTITY_CREATED,
                    CompanyProfileEventType.SUB_ENTITY_UPDATED,
                }:
                    return
                self.company_profile_ingestion_handler.handle(event.get("payload"))
            except Exception as e:
                logger.exception(
                    "Company Profile consumer error while processing message: %s", e
                )
