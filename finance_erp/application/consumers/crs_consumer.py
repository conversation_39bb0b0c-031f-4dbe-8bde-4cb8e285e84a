import logging

from flask import current_app
from flask.cli import with_appcontext
from treebo_commons.multitenancy.tenant_client import TenantClient

from finance_erp.application.consumers.constants import (
    CRSUserActions,
    InvoiceStatusToConsume,
)
from finance_erp.application.corporate.communication.cn_dispatcher import (
    CorporateCreditNoteDispatcher,
)
from finance_erp.application.corporate.communication.invoice_dispatcher import (
    CorporateInvoiceDispatcher,
)
from finance_erp.application.crs.command_handlers.ingest_booking import (
    CrsBookingIngestionCommandHandler,
)
from finance_erp.application.invoice.command_handler.ingest_credit_note import (
    CreditNoteIngestionCommandHandler,
)
from finance_erp.application.invoice.command_handler.ingest_invoice import (
    InvoiceIngestionCommandHandler,
)
from finance_erp.common.decorators import consumer_middleware
from finance_erp.infrastructure.consumers.base_consumer import BaseRMQConsumer
from finance_erp.infrastructure.consumers.consumer_config import CRSConsumerConfig

logger = logging.getLogger(__name__)


class CRSConsumer(BaseRMQConsumer):
    INVOICE_ENTITY_NAME = "invoice"
    CREDIT_NOTE_ENTITY_NAME = "credit_note"
    BOOKING_ENTITY_NAME = "booking"

    def __init__(
        self,
        credit_note_ingestion_handler: CreditNoteIngestionCommandHandler,
        invoice_ingestion_handler: InvoiceIngestionCommandHandler,
        booking_ingestion_handler: CrsBookingIngestionCommandHandler,
        invoice_dispatcher: CorporateInvoiceDispatcher,
        credit_note_dispatcher: CorporateCreditNoteDispatcher,
        tenant_id=TenantClient.get_default_tenant(),
    ):
        super().__init__(CRSConsumerConfig(tenant_id))
        self.tenant_id = tenant_id
        logger.info(
            "Listening to RMQ on host: %s from queues: %s",
            self.connection,
            [queue.name for queue in self.queues],
        )
        self.credit_note_ingestion_handler = credit_note_ingestion_handler
        self.invoice_ingestion_handler = invoice_ingestion_handler
        self.booking_ingestion_handler = booking_ingestion_handler
        self.invoice_dispatcher = invoice_dispatcher
        self.credit_note_dispatcher = credit_note_dispatcher

    @consumer_middleware
    @with_appcontext
    def process_message(self, body, message):
        message.ack()
        if not any(
            event.get("entity_name")
            in (
                self.CREDIT_NOTE_ENTITY_NAME,
                self.INVOICE_ENTITY_NAME,
                self.BOOKING_ENTITY_NAME,
            )
            for event in body.get("events")
        ):
            return

        logger.debug("Processing messages from CRS")

        user_action = body.get("user_action")

        with current_app.test_request_context():
            try:
                checked_out_booking_ids = []
                for event in body.get("events"):
                    event_payload = event.get("payload")
                    entity_name = event.get("entity_name")

                    if entity_name == self.BOOKING_ENTITY_NAME:
                        logger.info(
                            "Processing booking ingestion event: %s",
                            event_payload.get("booking_id"),
                        )
                        self.booking_ingestion_handler.handle(event_payload)
                        booking_status = event_payload.get("status")
                        if (
                            user_action == CRSUserActions.CHECKOUT
                            and booking_status == "checked_out"
                        ):
                            checked_out_booking_ids.append(
                                event_payload.get("booking_id")
                            )

                    elif entity_name == self.CREDIT_NOTE_ENTITY_NAME:
                        logger.info(
                            "Processing credit note ingestion event with payload: %s",
                            event_payload,
                        )
                        self.credit_note_ingestion_handler.handle(event_payload)

                    elif (
                        entity_name == self.INVOICE_ENTITY_NAME
                        and self._is_invoice_eligible_for_ingestion(event_payload)
                    ):
                        logger.info(
                            "Processing invoice ingestion event with payload: %s",
                            event_payload,
                        )
                        self.invoice_ingestion_handler.handle(event_payload)
                if checked_out_booking_ids:
                    self.invoice_dispatcher.dispatch_for_bookings(
                        checked_out_booking_ids
                    )
                    self.credit_note_dispatcher.dispatch_for_bookings(
                        checked_out_booking_ids
                    )
            except Exception as e:
                logger.exception("CRS consumer error while processing message: %s", e)

    @staticmethod
    def _is_invoice_eligible_for_ingestion(invoice):
        return invoice.get("status") in {
            InvoiceStatusToConsume.LOCKED,
            InvoiceStatusToConsume.GENERATED,
            InvoiceStatusToConsume.CANCELLED,
        }
