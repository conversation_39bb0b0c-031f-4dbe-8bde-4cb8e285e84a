from datetime import datetime

from ths_common.utils.collectionutils import chunks

from finance_erp.application.corporate.billing_service.handlers.stay_summary.stay_summary_handler import (
    StaySummaryHandler,
)
from finance_erp.application.corporate.dtos.billing_scheduler_request_dto import (
    BillingSchedulerRequestDto,
)
from finance_erp.async_job.job.dto.job_dto import AsyncJobDTO
from finance_erp.async_job.job.job_constants import JobName
from finance_erp.async_job.job_registry import JobRegistry
from finance_erp.async_job.job_result_dto import JobResultDto
from finance_erp.async_job.job_scheduler_service import JobSchedulerService
from finance_erp.domain.company_profile.entity.corporate import CorporateEntity
from finance_erp.domain.company_profile.repository.corporate_repository import (
    CorporateReportRepository,
)
from object_registry import register_instance


@register_instance(
    dependencies=[
        StaySummaryHand<PERSON>,
        JobRegistry,
        CorporateReportRepository,
        JobSchedulerService,
    ]
)
class CorporateBillingAgent:
    def __init__(
        self,
        stay_summary_handler: StaySummaryHand<PERSON>,
        job_registry: JobRegistry,
        corporate_repository: CorporateReportRepository,
        job_scheduler: JobSchedulerService,
    ):
        self.stay_summary_handler: StaySummaryHandler = stay_summary_handler
        self.corporate_repository: CorporateReportRepository = corporate_repository
        self.job_scheduler: JobSchedulerService = job_scheduler

        job_registry.register(JobName.CORPORATE_BILLING_JOB_NAME, self.execute)

    def execute(self, corporate_id: str, billing_date):
        billing_date = (
            datetime.strptime(billing_date, "%Y-%m-%d").date()
            if isinstance(billing_date, str)
            else billing_date
        )
        corporate: CorporateEntity = self.corporate_repository.get_record_for_update(
            corporate_id
        )

        if corporate.next_billing_date != billing_date:
            return JobResultDto.success(
                remarks="Billing date got updated, skipping the billing"
            )

        if corporate.is_stay_summary_dispatch_enabled:
            self.stay_summary_handler.handle(corporate)

        # Add more billing handlers here when needed

        corporate.wrap_up_current_billing_cycle()
        self.corporate_repository.update(corporate)
        return JobResultDto.success()

    def schedule_billing_jobs(self, billing_request_dto: BillingSchedulerRequestDto):
        if billing_request_dto.corporate_code:
            corporate = self.corporate_repository.get(
                billing_request_dto.corporate_code
            )
            if corporate.is_billing_enabled:
                self.job_scheduler.schedule_corporate_billing_job(
                    billing_request_dto.corporate_code, str(corporate.next_billing_date)
                )
                return True
            raise Exception("Billing is not enabled for the corporate")

        corp_ids = self.corporate_repository.get_corporate_ids_for_billing(
            billing_request_dto.billing_date
        )
        for batch in chunks(corp_ids, 2500):
            self._bulk_schedule_billing_jobs(batch, billing_request_dto.billing_date)

        return True

    def _bulk_schedule_billing_jobs(self, corporate_ids, billing_date):
        self.job_scheduler.batch_schedule(
            [
                AsyncJobDTO(
                    job_name=JobName.CORPORATE_BILLING_JOB_NAME,
                    data=dict(corporate_id=corporate_id, billing_date=billing_date),
                    is_resilient=True,
                    suffix=corporate_id,
                )
                for corporate_id in corporate_ids
            ]
        )
