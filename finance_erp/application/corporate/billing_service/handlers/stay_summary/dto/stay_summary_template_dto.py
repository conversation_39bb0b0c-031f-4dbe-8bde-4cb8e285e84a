import json
from datetime import date
from typing import Optional

from pydantic import BaseModel, EmailStr

from finance_erp.domain.company_profile.entity.corporate import CorporateEntity
from finance_erp.domain.crs.aggregate.stay_summary_aggregate import StaySummaryAggregate


class CorporateSchema(BaseModel):
    corporate_code: str
    customer_legal_name: Optional[str] = None
    customer_trading_name: Optional[str] = None
    address: Optional[str] = None
    city: Optional[str] = None
    phone_number: Optional[str] = None
    post_code: Optional[str] = None
    email: Optional[EmailStr] = None
    country_code: Optional[str] = None

    class Config:
        from_attributes = True
        validate_by_name = True


class InvoiceSchema(BaseModel):
    booking_id: Optional[str] = None
    invoice_number: Optional[str] = None
    invoice_date: Optional[date] = None
    post_tax_amount: Optional[float] = None
    vendor_details: Optional[dict] = None
    due_date: Optional[date] = None

    class Config:
        from_attributes = True
        validate_by_name = True


class StaySummarySchema(BaseModel):
    stay_summary_id: str
    billing_from: date
    billing_to: date
    billing_date: date

    class Config:
        from_attributes = True
        validate_by_name = True


class CreditNoteSchema(BaseModel):
    booking_id: Optional[str] = None
    credit_note_number: Optional[str] = None
    credit_note_date: Optional[date] = None
    post_tax_amount: Optional[float] = None
    vendor_details: Optional[dict] = None
    invoice_meta: Optional[dict] = None

    class Config:
        from_attributes = True
        validate_by_name = True


class StaySummaryTemplateDto:
    @staticmethod
    def create(
        corporate: CorporateEntity,
        stay_summary_aggregate: StaySummaryAggregate,
        chain_manager_banking_details=None,
        chain_manager_company_details=None,
    ):
        corporate_data = json.loads(
            CorporateSchema(**corporate.to_json()).model_dump_json()
        )
        summary_data = json.loads(
            StaySummarySchema(
                stay_summary_id=stay_summary_aggregate.stay_summary.stay_summary_id,
                billing_from=stay_summary_aggregate.stay_summary.billing_from,
                billing_to=stay_summary_aggregate.stay_summary.billing_to,
                billing_date=stay_summary_aggregate.stay_summary.billing_date,
            ).model_dump_json()
        )
        invoice_total = (
            str(stay_summary_aggregate.inv_total_amount)
            if stay_summary_aggregate.inv_total_amount
            else 0
        )
        cn_total = (
            str(stay_summary_aggregate.cn_total_amount)
            if stay_summary_aggregate.cn_total_amount
            else 0
        )
        balance_to_pay = (
            str(stay_summary_aggregate.due_amount)
            if stay_summary_aggregate.due_amount
            else 0
        )
        paid_inv_amount = (
            str(stay_summary_aggregate.paid_invoice_amount)
            if stay_summary_aggregate.paid_invoice_amount
            else 0
        )
        credit_notes_data = [
            json.loads(
                CreditNoteSchema(
                    booking_id=credit_note.booking_meta.reference_number,
                    credit_note_number=credit_note.credit_note_number,
                    credit_note_date=credit_note.credit_note_date,
                    post_tax_amount=credit_note.post_tax_amount,
                    vendor_details=credit_note.vendor_details,
                    invoice_meta=credit_note.invoice_meta,
                ).model_dump_json()
            )
            for credit_note in stay_summary_aggregate.credit_notes
        ]
        invoices_data = [
            json.loads(
                InvoiceSchema(
                    booking_id=invoice.booking_meta.reference_number,
                    invoice_number=invoice.invoice_number,
                    invoice_date=invoice.invoice_date,
                    post_tax_amount=invoice.post_tax_amount,
                    vendor_details=invoice.vendor_details,
                    due_date=invoice.due_date,
                ).model_dump_json()
            )
            for invoice in stay_summary_aggregate.invoices
        ]
        return dict(
            corporate=corporate_data,
            summary=summary_data,
            invoices=invoices_data,
            invoice_total=invoice_total,
            cn_total=cn_total,
            balance_to_pay=balance_to_pay,
            paid_inv_amount=paid_inv_amount,
            credit_notes=credit_notes_data,
            chain_manager_banking_details=chain_manager_banking_details,
            chain_manager_company_details=chain_manager_company_details,
        )
