import logging

from finance_erp.application.hotel_settings.constants import TenantConstants
from finance_erp.application.hotel_settings.tenant_settings import TenantSettings
from finance_erp.async_job.job.job_constants import JobName
from finance_erp.async_job.job_registry import JobRegistry
from finance_erp.async_job.job_scheduler_service import JobSchedulerService
from finance_erp.common.exception import DefinitiveFailureException
from finance_erp.domain.company_profile.constants import ProfilePOCRoles
from finance_erp.domain.company_profile.entity.corporate import CorporateEntity
from finance_erp.domain.company_profile.repository.corporate_repository import (
    CorporateReportRepository,
)
from finance_erp.domain.crs.aggregate.stay_summary_aggregate import StaySummaryAggregate
from finance_erp.domain.crs.repository.stay_summary_repository import (
    StaySummaryRepository,
)
from finance_erp.domain.shared_kernel.audit.communication_audit.audit_service import (
    CommunicationAuditService,
)
from finance_erp.domain.shared_kernel.audit.communication_audit.constants import (
    CommunicationStatus,
)
from finance_erp.infrastructure.alerting.slack_alert_service_client import (
    SlackAlertServiceClient,
)
from finance_erp.infrastructure.external_clients.aws_service_client import (
    AwsServiceClient,
)
from finance_erp.infrastructure.external_clients.communication_service_client import (
    CommunicationServiceClient,
)
from object_registry import register_instance

logger = logging.getLogger(__name__)

FILE_EXPIRY = 7 * 24 * 60 * 60  # 7 days in seconds


@register_instance(
    dependencies=[
        StaySummaryRepository,
        CorporateReportRepository,
        SlackAlertServiceClient,
        TenantSettings,
        CommunicationServiceClient,
        CommunicationAuditService,
        JobSchedulerService,
        JobRegistry,
    ]
)
class StaySummaryDispatcher:
    email_identifier = "b2b_stay_summary_mailer"

    def __init__(
        self,
        stay_summary_repository: StaySummaryRepository,
        corporate_report_repository: CorporateReportRepository,
        slack_alert_client: SlackAlertServiceClient,
        tenant_settings: TenantSettings,
        communication_service_client: CommunicationServiceClient,
        communication_audit_service: CommunicationAuditService,
        job_scheduler_service: JobSchedulerService,
        job_registry: JobRegistry,
    ):
        self.stay_summary_repository = stay_summary_repository
        self.corporate_report_repository = corporate_report_repository
        self.slack_alert_client = slack_alert_client
        self.tenant_settings = tenant_settings
        self.communication_service_client = communication_service_client
        self.communication_audit_service = communication_audit_service
        self.job_scheduler_service = job_scheduler_service

        job_registry.register(
            JobName.CORPORATE_STAY_SUMMARY_DISPATCH_JOB_NAME, self.dispatch
        )

    def dispatch(self, stay_summary_id: str, corporate_id: str = None):
        stay_summary_aggregate: StaySummaryAggregate = (
            self.stay_summary_repository.get_by_id(
                stay_summary_id, load_invoices=True, load_cns=True
            )
        )
        corporate_id = corporate_id or stay_summary_aggregate.stay_summary.corporate_id
        corporate: CorporateEntity = self.corporate_report_repository.get(corporate_id)
        notification_id = self._send_summary_to_customer(
            stay_summary_aggregate, corporate
        )
        if notification_id:
            self.communication_audit_service.audit_corporate_stay_summary_dispatch(
                stay_summary_aggregate.stay_summary, corporate, notification_id
            )

    def _send_summary_to_customer(
        self, stay_summary: StaySummaryAggregate, corporate: CorporateEntity
    ):
        to_emails = self._find_receiver_emails(corporate)
        if not to_emails:
            message = (
                f"No receiver emails found for corporate {corporate.corporate_code} "
                f"for stay summary {stay_summary.stay_summary_id} dispatch"
            )
            self.communication_audit_service.audit_corporate_stay_summary_dispatch(
                stay_summary=stay_summary.stay_summary,
                corporate=corporate,
                message=message,
                status=CommunicationStatus.FAILED,
            )
            raise DefinitiveFailureException(
                message,
                can_retry_once=True,
                send_alert_after_retry_threshold=False,
            )

        cc_emails = self._find_cc_emails(corporate)
        subject = (
            f"Stay Summary {stay_summary.stay_summary_id} Available for the Period: "
            f"{stay_summary.from_date.strftime('%B %d, %Y')} - {stay_summary.to_date.strftime('%B %d, %Y')}"
        )
        chain_manager_bank_details = self.tenant_settings.get_setting_value(
            TenantConstants.CHAIN_MANGER_BANK_DETAILS
        )
        (
            billing_team_email,
            billing_team_name,
        ) = self.tenant_settings.get_billing_team_contact()
        context_data = dict(
            customer_name=corporate.customer_legal_name
            or corporate.customer_trading_name,
            due_amount=str(stay_summary.due_amount),
            from_date=str(stay_summary.from_date),
            to_date=str(stay_summary.to_date),
            stay_summary_id=stay_summary.stay_summary_id,
            billing_team_email=billing_team_email,
            chain_manager_bank_details=chain_manager_bank_details,
        )
        signed_stay_summary_url_with_longer_expiry = (
            AwsServiceClient.get_presigned_url_from_s3_url(
                stay_summary.signed_url,
                link_expires_in=FILE_EXPIRY,
            )
        )
        booking_request_url_with_longer_expiry = None
        if stay_summary.booking_request_archive_url:
            booking_request_url_with_longer_expiry = (
                AwsServiceClient.get_presigned_url_from_s3_url(
                    stay_summary.booking_request_archive_url,
                    link_expires_in=FILE_EXPIRY,
                )
            )
        attachments = [
            dict(
                url=signed_stay_summary_url_with_longer_expiry,
                filename=f"{stay_summary.stay_summary_id}.pdf",
            ),
        ]
        if booking_request_url_with_longer_expiry:
            attachments.append(
                dict(
                    url=booking_request_url_with_longer_expiry,
                    filename=f"{stay_summary.stay_summary_id}_booking_request.zip",
                )
            )
        response = self.communication_service_client.send_email(
            identifier=self.email_identifier,
            context_data=context_data,
            to_emails=list(to_emails),
            cc=list(cc_emails) if cc_emails else [],
            subject=subject,
            attachments=attachments,
            from_email=billing_team_email,
            sender_name=billing_team_name,
        )
        return response.get("notification_id") if response else None

    @staticmethod
    def _find_receiver_emails(corporate: CorporateEntity):
        finance_admin_pocs = corporate.get_poc_by_designation(
            ProfilePOCRoles.FINANCE_ADMIN
        )
        primary_admin_pocs = corporate.get_poc_by_designation(
            ProfilePOCRoles.PRIMARY_ADMIN
        )
        to_emails = set()
        if finance_admin_pocs and finance_admin_pocs.email_ids:
            to_emails = set(finance_admin_pocs.email_ids)
        if primary_admin_pocs and primary_admin_pocs.email_ids:
            to_emails.update(primary_admin_pocs.email_ids)
        return to_emails

    def _find_cc_emails(self, corporate):
        treebo_sales_poc = corporate.get_poc_by_designation(ProfilePOCRoles.TREEBO_POC)
        cc_emails = set(self.tenant_settings.get_b2b_script_receiver_emails())
        if treebo_sales_poc and treebo_sales_poc.email_ids:
            cc_emails.update(treebo_sales_poc.email_ids)
        return cc_emails

    def async_dispatch(self, corporate_id: str, stay_summary_id: str):
        return self.job_scheduler_service.schedule_corporate_stay_summary_dispatch_job(
            corporate_id=corporate_id,
            stay_summary_id=stay_summary_id,
        )
