import logging
from concurrent.futures import ThreadPoolExecutor
from typing import List, Optional, Union

from flask import current_app as app

from finance_erp.application.corporate.billing_service.handlers.stay_summary.dto.stay_summary_template_dto import (
    StaySummaryTemplateDto,
)
from finance_erp.application.corporate.billing_service.handlers.stay_summary.stay_summary_dispatcher import (
    StaySummaryDispatcher,
)
from finance_erp.application.hotel_settings.configs.corporate_communication_config import (
    CorporateCommChannelRestriction,
)
from finance_erp.application.hotel_settings.constants import TenantConstants
from finance_erp.application.hotel_settings.tenant_settings import TenantSettings
from finance_erp.common.utils.s3_file_archiver import (
    FileMeta,
    InputFileMetaForFileArchiver,
    S3FileArchiver,
)
from finance_erp.domain.company_profile.entity.corporate import CorporateEntity
from finance_erp.domain.crs.aggregate.stay_summary_aggregate import StaySummaryAggregate
from finance_erp.domain.crs.entity.credit_note import CreditNote
from finance_erp.domain.crs.entity.invoice import Invoice
from finance_erp.domain.crs.entity.stay_summary import StaySummary
from finance_erp.domain.crs.repository.booking_repository import BookingRepository
from finance_erp.domain.crs.repository.stay_summary_repository import (
    StaySummaryRepository,
)
from finance_erp.domain.crs.value_objects.booking import Attachment
from finance_erp.infrastructure.alerting.slack_alert_service_client import (
    SlackAlertServiceClient,
)
from finance_erp.infrastructure.external_clients.aws_service_client import (
    AwsServiceClient,
)
from finance_erp.infrastructure.external_clients.communication_service_client import (
    CommunicationServiceClient,
)
from finance_erp.infrastructure.external_clients.template_service_client import (
    TemplateNameSpace,
    TemplateServiceClient,
)
from object_registry import register_instance

logger = logging.getLogger(__name__)

FILE_EXPIRY = 60 * 60  # well enough for file merger


@register_instance(
    dependencies=[
        StaySummaryRepository,
        BookingRepository,
        StaySummaryDispatcher,
        TemplateServiceClient,
        TenantSettings,
        SlackAlertServiceClient,
        AwsServiceClient,
        CommunicationServiceClient,
    ]
)
class StaySummaryHandler:
    def __init__(
        self,
        stay_summary_repository: StaySummaryRepository,
        booking_repository: BookingRepository,
        stay_summary_dispatcher: StaySummaryDispatcher,
        template_service_client: TemplateServiceClient,
        tenant_settings: TenantSettings,
        slack_alert_client: SlackAlertServiceClient,
        aws_service_client: AwsServiceClient,
        communication_service_client: CommunicationServiceClient,
    ):
        self.stay_summary_repository = stay_summary_repository
        self.booking_repository = booking_repository
        self.stay_summary_dispatcher = stay_summary_dispatcher
        self.template_service_client = template_service_client
        self.tenant_settings = tenant_settings
        self.slack_alert_client = slack_alert_client
        self.aws_service_client = aws_service_client
        self.communication_service_client = communication_service_client

    def handle(self, corporate: CorporateEntity):
        stay_summary_aggregate: Optional[StaySummaryAggregate] = self._generate(
            corporate
        )
        if not stay_summary_aggregate:
            logger.info(
                f"No stay summary to generate for corporate {corporate.corporate_code}"
            )
            return None
        self.stay_summary_dispatcher.async_dispatch(
            corporate.corporate_code, stay_summary_aggregate.stay_summary_id
        )
        return self.stay_summary_repository.save(stay_summary_aggregate)

    def _generate(self, corporate: CorporateEntity) -> Optional[StaySummaryAggregate]:
        assert corporate.invoice_types_for_stay_summary
        invoices_to_dispatch = self.stay_summary_repository.get_eligible_corporate_invoices_for_stay_summary(
            corporate_id=corporate.corporate_code,
            bill_to=corporate.billing_cycle.to_date,
            invoice_types=corporate.invoice_types_for_stay_summary,
        )
        credit_notes_to_dispatch = (
            self.stay_summary_repository.get_eligible_credit_notes_for_stay_summary(
                corporate_id=corporate.corporate_code,
                bill_to=corporate.billing_cycle.to_date,
                credit_note_types=corporate.invoice_types_for_stay_summary,
            )
            if self.tenant_settings.should_include_cn_in_stay_summary()
            else []
        )
        channel_communication_config: CorporateCommChannelRestriction = (
            self.tenant_settings.fetch_corporate_comm_channel_config()
        )
        if not channel_communication_config:
            return None

        invoices_to_dispatch = self._filter_documents_by_channel_config(
            invoices_to_dispatch, channel_communication_config
        )
        credit_notes_to_dispatch = self._filter_documents_by_channel_config(
            credit_notes_to_dispatch, channel_communication_config
        )

        if not (invoices_to_dispatch or credit_notes_to_dispatch):
            return None

        return self._create_new_stay_summary_aggregate(
            corporate,
            invoices_to_dispatch,
            credit_notes_to_dispatch,
        )

    @staticmethod
    def _filter_documents_by_channel_config(
        invoices: List[Union[CreditNote, Invoice]],
        channel_communication_config: CorporateCommChannelRestriction,
    ) -> List[Union[CreditNote, Invoice]]:
        return [
            invoice
            for invoice in invoices
            if channel_communication_config.is_stay_summary_communication_enabled(
                invoice.booking_meta.channel_code,
                invoice.booking_meta.subchannel_code,
            )
        ]

    def _create_new_stay_summary_aggregate(
        self,
        corporate: CorporateEntity,
        invoices: List[Invoice],
        credit_notes: List[CreditNote],
    ) -> StaySummaryAggregate:
        stay_summary_id = self.stay_summary_repository.get_next_stay_summary_id(
            corporate_code=corporate.corporate_code
        )
        stay_summary_aggregate = StaySummaryAggregate(
            stay_summary=StaySummary(
                stay_summary_id=stay_summary_id,
                corporate_id=corporate.corporate_code,
                billing_from=corporate.billing_cycle.from_date,
                billing_to=corporate.billing_cycle.to_date,
                billing_date=corporate.next_billing_date,
            ),
            invoices=invoices,
            credit_notes=credit_notes,
        )
        booking_request_attachments = (
            stay_summary_aggregate.get_all_booking_request_attachments()
        )
        if (
            corporate.should_dispatch_booking_request_with_stay_summary
            and booking_request_attachments
        ):
            booking_request_archive = self._generate_booking_request_zip(
                booking_request_attachments
            )
            if booking_request_archive:
                stay_summary_aggregate.update_booking_request_signed_url(
                    booking_request_archive.signed_url
                )
        if stay_summary_aggregate.has_documents():
            jumbo_pdf_url = self._generate_stay_summary_jumbo_pdf(
                corporate, stay_summary_aggregate
            )
            stay_summary_aggregate.update_signed_url(jumbo_pdf_url)
        return stay_summary_aggregate

    def _generate_stay_summary_jumbo_pdf(
        self, corporate: CorporateEntity, stay_summary_aggregate: StaySummaryAggregate
    ):
        cover_page_url = self.template_service_client.generate(
            TemplateNameSpace.STAY_SUMMARY.value,
            context=self._stay_summary_cover_page_template_payload(
                corporate,
                stay_summary_aggregate,
            ),
        )
        cover_page_url = AwsServiceClient.get_presigned_url_from_s3_url(
            cover_page_url, link_expires_in=FILE_EXPIRY
        )
        file_prefix = stay_summary_aggregate.stay_summary_id.replace("/", "_")
        pdf_urls_to_merge = (
            [dict(path=cover_page_url, name=f"{file_prefix}_cover_page.pdf")]
            + [
                dict(
                    path=AwsServiceClient.get_presigned_url_from_s3_url(
                        invoice.invoice_url, link_expires_in=FILE_EXPIRY
                    ),
                    name=f"{invoice.invoice_number}.pdf",
                )
                for invoice in stay_summary_aggregate.invoices
                if invoice.invoice_url
            ]
            + [
                dict(
                    path=AwsServiceClient.get_presigned_url_from_s3_url(
                        credit_note.credit_note_url, link_expires_in=FILE_EXPIRY
                    ),
                    name=f"{credit_note.credit_note_number}.pdf",
                )
                for credit_note in stay_summary_aggregate.credit_notes
                if credit_note.credit_note_url
            ]
        )
        return self.template_service_client.merge_pdfs(
            stay_summary_aggregate.stay_summary_id,
            pdf_urls_to_merge,
            merged_file_name=f"{file_prefix}_summary.pdf",
        )

    def _stay_summary_cover_page_template_payload(
        self,
        corporate: CorporateEntity,
        stay_summary_aggregate: StaySummaryAggregate,
    ):
        chain_manager_banking_details = self.tenant_settings.get_setting_value(
            TenantConstants.CHAIN_MANGER_BANK_DETAILS
        )
        chain_manager_company_details = self.tenant_settings.get_setting_value(
            TenantConstants.CHAIN_MANGER_COMPANY_DETAILS
        )
        return StaySummaryTemplateDto.create(
            corporate=corporate,
            stay_summary_aggregate=stay_summary_aggregate,
            chain_manager_banking_details=chain_manager_banking_details,
            chain_manager_company_details=chain_manager_company_details,
        )

    def _generate_booking_request_zip(
        self, booking_request_attachments: List[Attachment]
    ) -> Optional[FileMeta]:
        files_to_process = []
        for booking_request in booking_request_attachments:
            files_to_process.append(
                InputFileMetaForFileArchiver(
                    file_name=f"{booking_request.attachment_id}.{booking_request.file_type}",
                    file_url=booking_request.signed_url,
                )
            )
        if not files_to_process:
            return None
        return self._prepare_and_upload_zipped_booking_request(files_to_process)

    @staticmethod
    def _prepare_and_upload_zipped_booking_request(
        files_to_process: List[InputFileMetaForFileArchiver],
    ) -> FileMeta:
        invoice_archiver = S3FileArchiver("booking_request")
        with ThreadPoolExecutor(
            max_workers=int(app.config["MAX_THREAD_FOR_PDF_GENERATION"])
        ) as executor:
            executor.map(
                invoice_archiver.download_file,
                files_to_process,
            )
        return invoice_archiver.prepare_archive_and_upload()
