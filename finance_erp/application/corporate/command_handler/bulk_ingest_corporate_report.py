import logging
from typing import List, Tuple

from finance_erp.application.common.base_ingestion_handler import (
    BaseIngestionCommandHandler,
)
from finance_erp.domain.company_profile.dto.corporate_dto import CorporateDto
from finance_erp.domain.company_profile.entity.corporate import CorporateEntity
from finance_erp.domain.company_profile.factory.corporate_factory import (
    CorporateFactory,
)
from finance_erp.domain.company_profile.repository.corporate_repository import (
    CorporateReportRepository,
)
from finance_erp.domain.shared_kernel.audit.process_level.ingestion_audit_service import (
    IngestionAuditService,
)
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(dependencies=[CorporateReportRepository, IngestionAuditService])
class BulkIngestCorporateReportCommandHandler(BaseIngestionCommandHandler):
    def __init__(
        self,
        corporate_repository: CorporateReportRepository,
        ingestion_audit_service: IngestionAuditService,
    ):
        self.corporate_repository = corporate_repository
        super().__init__(ingestion_audit_service)

    def _run_ingestion(self, corporate_dtos: List[CorporateDto], report_name, event_id):
        if not corporate_dtos:
            return
        corporate_reports_to_update, new_corporate_reports = self._segregate_records(
            corporate_dtos
        )
        if new_corporate_reports:
            self.corporate_repository.insert_many(new_corporate_reports)
        self.ingestion_audit_service.record_ingestion_data_insertion_event(
            event_id,
            report_name,
            stats=dict(no_of_inserted_records=len(new_corporate_reports)),
        )
        if corporate_reports_to_update:
            self.corporate_repository.bulk_update_records(corporate_reports_to_update)
        self.ingestion_audit_service.record_ingestion_data_update_event(
            event_id,
            report_name,
            stats=dict(no_of_updated_records=len(corporate_reports_to_update)),
        )
        return f"Successfully ingested {report_name} event {event_id}"

    def _parse_job_data(self, data_list):
        return [CorporateDto.from_dict(item) for item in data_list]

    def _segregate_records(
        self, corporate_dtos: List[CorporateDto]
    ) -> Tuple[List[CorporateEntity], List[CorporateEntity]]:
        existing_records = self.corporate_repository.get_by_ids(
            self._get_unique_ids(corporate_dtos), for_update=True
        )
        existing_records_map = {
            record.get_unique_identifier(): record for record in existing_records
        }

        records_to_update, records_to_insert = [], []
        for item in corporate_dtos:
            new_corporate_entity = CorporateFactory.create_from_corporate_dto(item)
            record_id = item.get_unique_identifier()

            if record_id in existing_records_map:
                existing_record = existing_records_map[record_id]
                existing_record.update(new_corporate_entity)
                records_to_update.append(existing_record)
            else:
                records_to_insert.append(new_corporate_entity)

        return records_to_update, records_to_insert

    def _get_unique_ids(self, ingestion_data_list: List[CorporateDto]):
        return [rc.corporate_code for rc in ingestion_data_list]
