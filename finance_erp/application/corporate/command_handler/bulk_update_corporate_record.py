from typing import List

from ths_common.exceptions import ResourceNotFound

from finance_erp.domain.company_profile.entity.corporate import (
    UPDATABLE_FIELDS,
    CorporateEntity,
)
from finance_erp.domain.company_profile.repository.corporate_repository import (
    CorporateReportRepository,
)
from object_registry import register_instance


@register_instance(dependencies=[CorporateReportRepository])
class BulkUpdateCorporateReportCommandHandler:
    def __init__(self, corporate_repository: CorporateReportRepository):
        self.corporate_repository = corporate_repository

    def handle(self, request_data_list: list):
        uuids = [rc["corporate_code"] for rc in request_data_list]
        corporate_report_reports: List[
            CorporateEntity
        ] = self.corporate_repository.get_by_ids(uuids, for_update=True)
        report_map = {ag.corporate_code: ag for ag in corporate_report_reports}
        corporate_report_reports = []
        for request_data in request_data_list:
            uu_id = request_data["corporate_code"]
            if uu_id not in report_map:
                raise ResourceNotFound(
                    "CorporateRecord", extra_payload=dict(corporate_code=uu_id)
                )
            corporate_report_report: CorporateEntity = report_map[uu_id]
            # TODO: Handle updates elegantly
            for attr in UPDATABLE_FIELDS:
                if attr in request_data:
                    setattr(corporate_report_report, attr, request_data[attr])
            corporate_report_reports.append(corporate_report_report)
        self.corporate_repository.bulk_update_records(corporate_report_reports)
        return corporate_report_reports
