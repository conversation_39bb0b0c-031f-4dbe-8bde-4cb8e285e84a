from datetime import datetime

from core.common.utils.date import utc_to_ist
from finance_erp.application.corporate.dtos.get_corporate_report_dto import (
    GetCorporateReportDto,
)
from finance_erp.domain.company_profile.repository.corporate_repository import (
    CorporateReportRepository,
)
from object_registry import register_instance


@register_instance(dependencies=[CorporateReportRepository])
class FetchCorporateCommandHandler:
    def __init__(self, corporate_repository: CorporateReportRepository):
        self.corporate_repository = corporate_repository

    def handle(self, input_criteria: GetCorporateReportDto):
        if input_criteria.corporate_codes:
            input_criteria.corporate_codes = (
                input_criteria.corporate_codes.split(",")
                if input_criteria.corporate_codes
                else None
            )
            return self.corporate_repository.get_by_ids(input_criteria.corporate_codes)
        else:
            if not input_criteria.from_date:
                input_criteria.from_date = utc_to_ist(datetime.utcnow()).date()
            if not input_criteria.to_date:
                input_criteria.to_date = utc_to_ist(datetime.utcnow()).date()
            return self.corporate_repository.get_reports_by_date_range(
                from_date=input_criteria.from_date, to_date=input_criteria.to_date
            )
