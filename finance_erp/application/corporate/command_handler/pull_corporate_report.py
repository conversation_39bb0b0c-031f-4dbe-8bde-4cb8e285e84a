from finance_erp.application.common.base_data_pull_handler import (
    BaseDataPullCommandHandler,
)
from finance_erp.application.corporate.command_handler.bulk_ingest_corporate_report import (
    BulkIngestCorporateReportCommandHandler,
)
from finance_erp.application.corporate.dtos.generate_corporate_report_dto import (
    GenerateCorporateReportDto,
)
from finance_erp.domain.company_profile.dto.corporate_dto import CorporateDto
from finance_erp.domain.shared_kernel.audit.process_level.data_pull_audit_service import (
    DataPullAuditService,
)
from finance_erp.infrastructure.external_clients.company_profile_client import (
    CompanyProfileServiceClient,
)
from object_registry import register_instance


@register_instance(
    dependencies=[
        DataPullAuditService,
        CompanyProfileServiceClient,
        BulkIngestCorporateReportCommandHandler,
    ]
)
class BulkPullCorporateReportCommandHandler(BaseDataPullCommandHandler):
    def __init__(
        self,
        data_pull_audit_service: DataPullAuditService,
        cp_service_client: CompanyProfileServiceClient,
        corporate_data_ingestion_handler: BulkIngestCorporateReportCommandHandler,
    ):
        super().__init__(data_pull_audit_service)
        self.cp_service_client = cp_service_client
        self.corporate_data_ingestion_handler = corporate_data_ingestion_handler

    def _pull_and_ingest_data(self, request_data, report_name, event_id):
        corporate_data = self.cp_service_client.fetch_created_or_modified_entities(
            request_data
        ).get("data")

        if not corporate_data:
            return []

        # TODO: [PROM-17203] Choose one of the following approaches
        #  1.Replace the current API call with the company-profiles/v1/search API (
        #    after adding support for multiple superhero_company_codes and modified_after filter)
        #  2. [OR] Alternatively, modify the response structure of the currently used API
        #    to match the response structure of the company-profiles/v1/search API
        #  In either case, update the code to use finance_erp.domain.company_profile.dto.company_profiles_dto.SubEntity
        #  a) Use CorporateDto.create_from_company_profile_entity method b) remove from_corporate_data_response from dto

        data_to_ingest = [
            CorporateDto.from_corporate_data_response(data) for data in corporate_data
        ]

        return self.corporate_data_ingestion_handler.handle(
            data_to_ingest, report_name, event_id
        )

    def _get_request_parser(self):
        return GenerateCorporateReportDto
