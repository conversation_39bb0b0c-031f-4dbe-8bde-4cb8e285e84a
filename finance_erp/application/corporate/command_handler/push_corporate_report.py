from ths_common.exceptions import ValidationException

from finance_erp.application.common.base_data_push_handler import (
    BaseDataPushCommandHandler,
)
from finance_erp.application.corporate.dtos.bulk_corporate_push_request_dto import (
    BulkCorporatePushRequestDto,
)
from finance_erp.application.hotel_settings.tenant_settings import TenantSettings
from finance_erp.common.constants import DataPushNavKeys
from finance_erp.common.decorators import session_manager
from finance_erp.common.error_codes import ApplicationErrors
from finance_erp.common.schema.corporate import CorporatePushRequestSchema
from finance_erp.domain.company_profile.entity.corporate import CorporateEntity
from finance_erp.domain.company_profile.repository.corporate_repository import (
    CorporateReportRepository,
)
from finance_erp.domain.hotel.repository.hotel_repository import HotelReportRepository
from finance_erp.domain.shared_kernel.audit.process_level.nav_push_audit_service import (
    NavPushAuditService,
)
from finance_erp.infrastructure.alerting.slack_alert_service_client import (
    SlackAlertServiceClient,
)
from finance_erp.infrastructure.external_clients.business_central_client import (
    BusinessCentralClient,
)
from object_registry import register_instance


@register_instance(
    dependencies=[
        BusinessCentralClient,
        SlackAlertServiceClient,
        CorporateReportRepository,
        NavPushAuditService,
        TenantSettings,
        HotelReportRepository,
    ]
)
class BulkPushCorporateReportCommandHandler(BaseDataPushCommandHandler):
    def __init__(
        self,
        external_data_push_client: BusinessCentralClient,
        slack_alert_client: SlackAlertServiceClient,
        corporate_repository: CorporateReportRepository,
        nav_push_audit_service: NavPushAuditService,
        tenant_settings: TenantSettings,
        hotel_reports_repository: HotelReportRepository,
    ):
        super().__init__(
            external_data_push_client,
            slack_alert_client,
            nav_push_audit_service,
            tenant_settings,
            hotel_reports_repository,
        )
        self.corporate_repository = corporate_repository
        self.tenant_settings = tenant_settings
        self.hotel_reports_repository = hotel_reports_repository

    def _get_schema_and_json_key(self):
        return CorporatePushRequestSchema, DataPushNavKeys.CORPORATE

    def _get_records_to_push(self, request_dto: BulkCorporatePushRequestDto):
        if request_dto.corporate_codes:
            return self.corporate_repository.get_by_ids(
                request_dto.corporate_codes, for_update=True
            )
        return self.corporate_repository.get_eligible_records_to_push()

    @session_manager(commit=True)
    def _capture_failure(self, record: CorporateEntity, reason):
        record.capture_data_push_failure(reason)
        self.corporate_repository.update_record(record)

    @session_manager(commit=True)
    def _capture_success(self, record: CorporateEntity):
        record.capture_data_push_success()
        self.corporate_repository.update_record(record)

    def _get_request_parser(self):
        return BulkCorporatePushRequestDto

    @staticmethod
    def _validate_records(records):
        for record in records:
            if not record.verified:
                raise ValidationException(
                    ApplicationErrors.DATA_PUSH_NOT_ALLOWED_FOR_UNVERIFIED_RECORDS,
                    extra_payload=dict(uuid=record.get_unique_identifier()),
                )

    def _apply_data_exclusion_rules(self, records):
        return records
