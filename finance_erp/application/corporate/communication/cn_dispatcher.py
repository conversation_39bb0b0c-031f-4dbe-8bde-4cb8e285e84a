import logging

from finance_erp.application.corporate.communication.corporate_dispatcher import (
    CorporateDispatcherBase,
)
from finance_erp.application.hotel_settings.tenant_settings import TenantSettings
from finance_erp.async_job.job.job_constants import JobName
from finance_erp.async_job.job_registry import JobRegistry
from finance_erp.async_job.job_result_dto import JobResultDto
from finance_erp.async_job.job_scheduler_service import JobSchedulerService
from finance_erp.domain.company_profile.entity.corporate import CorporateEntity
from finance_erp.domain.company_profile.repository.corporate_repository import (
    CorporateReportRepository,
)
from finance_erp.domain.crs.entity.credit_note import CreditNote
from finance_erp.domain.crs.repository.booking_repository import BookingRepository
from finance_erp.domain.crs.repository.credit_note_repository import (
    CreditNoteRepository,
)
from finance_erp.domain.shared_kernel.audit.communication_audit.audit_service import (
    CommunicationAuditService,
)
from finance_erp.domain.shared_kernel.audit.communication_audit.constants import (
    CommunicationStatus,
)
from finance_erp.infrastructure.alerting.slack_alert_service_client import (
    SlackAlertServiceClient,
)
from finance_erp.infrastructure.external_clients.communication_service_client import (
    CommunicationServiceClient,
)
from object_registry import register_instance

logger = logging.getLogger(__name__)

FILE_EXPIRY = 60 * 60  # well enough for email attachments


@register_instance(
    dependencies=[
        CreditNoteRepository,
        JobSchedulerService,
        CommunicationAuditService,
        JobRegistry,
        CorporateReportRepository,
        SlackAlertServiceClient,
        TenantSettings,
        CommunicationServiceClient,
        BookingRepository,
    ]
)
class CorporateCreditNoteDispatcher(CorporateDispatcherBase):
    email_identifier = "b2b_invoice_mailer"

    def __init__(
        self,
        credit_note_repository: CreditNoteRepository,
        job_scheduler_service: JobSchedulerService,
        communication_audit_service: CommunicationAuditService,
        job_registry: JobRegistry,
        corporate_repository: CorporateReportRepository,
        slack_alert_client: SlackAlertServiceClient,
        tenant_settings: TenantSettings,
        communication_service_client: CommunicationServiceClient,
        booking_repository: BookingRepository,
    ):
        super().__init__(
            corporate_repository,
            slack_alert_client,
            tenant_settings,
            communication_service_client,
            email_identifier=self.email_identifier,
        )
        self.credit_note_repository = credit_note_repository
        self.job_scheduler_service = job_scheduler_service
        self.communication_audit_service = communication_audit_service
        self.booking_repository = booking_repository

        job_registry.register(
            JobName.CORPORATE_CREDIT_NOTE_DISPATCH_JOB_NAME, self.dispatch
        )

    def dispatch(self, credit_note_id):
        credit_note: CreditNote = self.credit_note_repository.get_by_id(credit_note_id)
        booking = self.booking_repository.get_by_id(credit_note.booking_id)
        corporate: CorporateEntity = self.corporate_repository.get(
            credit_note.booker_legal_entity_id
        )
        (
            billing_team_email,
            billing_team_name,
        ) = self.tenant_settings.get_billing_team_contact()
        if (
            corporate.invoice_types_for_checkout_dispatch
            and credit_note.credit_note_type
            in corporate.invoice_types_for_checkout_dispatch
        ):
            context_data = {
                "customer_name": str(credit_note.issued_to.name),
                "hotel_name": credit_note.vendor_details.get("vendor_name"),
                "credit_note_number": credit_note.credit_note_number,
                "due_amount": "0",
                "billing_date": credit_note.credit_note_date.isoformat(),
                "billing_team_email": billing_team_name,
            }
            subject = f"Your Credit for Booking ID: {credit_note.booking_meta.reference_number}"
            external_communication_id = self._send_document_to_customer(
                booking,
                credit_note,
                corporate,
                subject,
                context_data,
                from_email=billing_team_email,
                sender_name=billing_team_name,
            )
            self.communication_audit_service.audit_corporate_credit_note_dispatch(
                credit_note=credit_note,
                corporate=corporate,
                external_communication_id=external_communication_id,
            )
            return JobResultDto.success()
        return JobResultDto.success(
            remark="Either dispatch is disabled for corporate or credit_note type not supported for dispatch"
        )

    def capture_communication_failure_log(
        self, credit_note: CreditNote, error_message: str, corporate=None
    ):
        return self.communication_audit_service.audit_corporate_credit_note_dispatch(
            credit_note=credit_note,
            corporate=corporate,
            status=CommunicationStatus.FAILED,
            message=error_message,
        )

    def was_document_already_dispatched(self, credit_note: CreditNote):
        return self.communication_audit_service.was_cn_dispatched(credit_note)

    def schedule_dispatch_job(self, credit_note: CreditNote):
        self.job_scheduler_service.schedule_corporate_credit_note_dispatch_job(
            credit_note.credit_note_id
        )

    def dispatch_for_bookings(self, booking_ids):
        credit_notes = self.credit_note_repository.get_by_booking_ids(booking_ids)
        for credit_notes in credit_notes:
            self.schedule_dispatch_if_eligible(credit_notes)

    def doc_type(self, document: CreditNote):
        return CreditNote.__name__
