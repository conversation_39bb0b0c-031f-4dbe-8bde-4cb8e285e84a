import logging
from typing import Union

from finance_erp.application.hotel_settings.configs.corporate_communication_config import (
    CorporateCommChannelRestriction,
)
from finance_erp.application.hotel_settings.tenant_settings import TenantSettings
from finance_erp.common.constants import FIN_ERP_MINT_MIGRATION_GUARD_DATE
from finance_erp.common.exception import DefinitiveFailureException
from finance_erp.domain.company_profile.constants import ProfilePOCRoles
from finance_erp.domain.company_profile.entity.corporate import CorporateEntity
from finance_erp.domain.company_profile.repository.corporate_repository import (
    CorporateReportRepository,
)
from finance_erp.domain.crs.entity.booking import Booking
from finance_erp.domain.crs.entity.credit_note import CreditNote
from finance_erp.domain.crs.entity.invoice import Invoice
from finance_erp.infrastructure.alerting.slack_alert_service_client import (
    SlackAlertServiceClient,
)
from finance_erp.infrastructure.external_clients.aws_service_client import (
    AwsServiceClient,
)
from finance_erp.infrastructure.external_clients.communication_service_client import (
    CommunicationServiceClient,
)

logger = logging.getLogger(__name__)

FILE_EXPIRY = 60 * 60  # well enough for email attachments


class CorporateDispatcherBase:
    def __init__(
        self,
        corporate_repository: CorporateReportRepository,
        slack_alert_client: SlackAlertServiceClient,
        tenant_settings: TenantSettings,
        communication_service_client: CommunicationServiceClient,
        email_identifier: str,
    ):
        self.corporate_repository = corporate_repository
        self.slack_alert_client = slack_alert_client
        self.tenant_settings = tenant_settings
        self.communication_service_client = communication_service_client
        self.email_identifier = email_identifier

    @staticmethod
    def _find_receiver_emails(corporate: CorporateEntity):
        finance_admin_pocs = corporate.get_poc_by_designation(
            ProfilePOCRoles.FINANCE_ADMIN
        )
        primary_admin_pocs = corporate.get_poc_by_designation(
            ProfilePOCRoles.PRIMARY_ADMIN
        )
        to_emails = set()
        if finance_admin_pocs and finance_admin_pocs.email_ids:
            to_emails = set(finance_admin_pocs.email_ids)
        if primary_admin_pocs and primary_admin_pocs.email_ids:
            to_emails.update(primary_admin_pocs.email_ids)
        return to_emails

    def _find_cc_emails(self, corporate):
        treebo_sales_poc = corporate.get_poc_by_designation(ProfilePOCRoles.TREEBO_POC)
        cc_emails = set(self.tenant_settings.get_b2b_script_receiver_emails())
        if treebo_sales_poc and treebo_sales_poc.email_ids:
            cc_emails.update(treebo_sales_poc.email_ids)
        return cc_emails

    def _send_document_to_customer(
        self,
        booking: Booking,
        document: Union[CreditNote, Invoice],
        corporate: CorporateEntity,
        subject,
        context_data,
        from_email: str = None,
        sender_name: str = None,
    ):
        attachments = []
        if corporate.should_dispatch_booking_request_invoice_or_cn:
            booking_request_attachments = booking.get_booking_request_attachment()
            if booking_request_attachments:
                signed_req_url_with_longer_expiry = (
                    AwsServiceClient.get_presigned_url_from_s3_url(
                        booking_request_attachments.original_url,
                        link_expires_in=FILE_EXPIRY,
                    )
                )
                attachments.append(
                    dict(
                        url=signed_req_url_with_longer_expiry,
                        filename=f"Req_{booking.reference_number}.{booking_request_attachments.file_type}",
                    )
                )

        to_emails = self._find_receiver_emails(corporate)
        if not to_emails:
            message = (
                f"No receiver emails found for corporate {corporate.corporate_code} "
                f"for document {self.doc_type(document)} {document.uuid} dispatch, Please check the POCs"
            )
            self.capture_communication_failure_log(
                document, message, corporate=corporate
            )
            raise DefinitiveFailureException(
                message,
                can_retry_once=True,
                send_alert_after_retry_threshold=False,
            )

        cc_emails = self._find_cc_emails(corporate)
        signed_url_with_longer_expiry = AwsServiceClient.get_presigned_url_from_s3_url(
            document.url,
            link_expires_in=FILE_EXPIRY,
        )
        attachments.append(
            dict(
                url=signed_url_with_longer_expiry,
                filename=f"{document.number}.pdf",
            )
        )
        response = self.communication_service_client.send_email(
            identifier=self.email_identifier,
            context_data=context_data,
            to_emails=list(to_emails),
            cc=list(cc_emails),
            subject=subject,
            attachments=attachments,
            from_email=from_email,
            sender_name=sender_name,
        )
        return response.get("notification_id") if response else None

    def doc_type(self, document):
        raise NotImplementedError

    def was_document_already_dispatched(self, document):
        raise NotImplementedError

    def schedule_dispatch_job(self, document):
        raise NotImplementedError

    def capture_communication_failure_log(
        self, document, error_message: str, corporate=None
    ):
        raise NotImplementedError

    def schedule_dispatch_if_eligible(self, document: Union[CreditNote, Invoice]):
        if document.doc_date < FIN_ERP_MINT_MIGRATION_GUARD_DATE:
            return
        if not document.url:
            return
        if not document.is_b2b:
            return
        if self.was_document_already_dispatched(document):
            logger.info(
                f"SKipping {self.doc_type(document)} {document.uuid} dispatch: "
                f"as it is already dispatched."
            )
            return
        channel_communication_config: CorporateCommChannelRestriction = (
            self.tenant_settings.fetch_corporate_comm_channel_config()
        )
        if not channel_communication_config.is_invoice_communication_enabled(
            document.booking_meta.channel_code,
            document.booking_meta.subchannel_code,
        ):
            return
        if not document.booker_legal_entity_id:
            message = f"Dispatch not attempted: Booker LE id not found for {self.doc_type(document)} {document.uuid}"
            return self.capture_communication_failure_log(document, message)
        corporate: CorporateEntity = self.corporate_repository.get(
            document.booker_legal_entity_id
        )
        if not corporate:
            message = (
                f"Dispatch not attempted: LE :{document.booker_legal_entity_id}"
                f"not found/active in Fin ERP for {self.doc_type(document)} {document.uuid}"
            )
            return self.capture_communication_failure_log(document, message)
        if not (
            corporate.invoice_types_for_checkout_dispatch
            and document.category in corporate.invoice_types_for_checkout_dispatch
        ):
            logger.info(
                f"SKipping {self.doc_type(document)} {document.uuid} dispatch:"
                f"{document.category} is disabled for corporate {corporate.corporate_code}"
            )
            return
        self.schedule_dispatch_job(document)
