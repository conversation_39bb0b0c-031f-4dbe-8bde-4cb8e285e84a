import logging

from finance_erp.application.corporate.communication.corporate_dispatcher import (
    CorporateDispatcherBase,
)
from finance_erp.application.hotel_settings.tenant_settings import TenantSettings
from finance_erp.async_job.job.job_constants import JobName
from finance_erp.async_job.job_registry import JobRegistry
from finance_erp.async_job.job_result_dto import JobResultDto
from finance_erp.async_job.job_scheduler_service import JobSchedulerService
from finance_erp.domain.company_profile.entity.corporate import CorporateEntity
from finance_erp.domain.company_profile.repository.corporate_repository import (
    CorporateReportRepository,
)
from finance_erp.domain.crs.entity.invoice import Invoice
from finance_erp.domain.crs.repository.booking_repository import BookingRepository
from finance_erp.domain.crs.repository.invoice_repository import InvoiceRepository
from finance_erp.domain.shared_kernel.audit.communication_audit.audit_service import (
    CommunicationAuditService,
)
from finance_erp.domain.shared_kernel.audit.communication_audit.constants import (
    CommunicationStatus,
)
from finance_erp.infrastructure.alerting.slack_alert_service_client import (
    SlackAlertServiceClient,
)
from finance_erp.infrastructure.external_clients.communication_service_client import (
    CommunicationServiceClient,
)
from object_registry import register_instance

logger = logging.getLogger(__name__)

FILE_EXPIRY = 60 * 60  # well enough for email attachments


@register_instance(
    dependencies=[
        InvoiceRepository,
        JobSchedulerService,
        CommunicationAuditService,
        BookingRepository,
        JobRegistry,
        CorporateReportRepository,
        SlackAlertServiceClient,
        TenantSettings,
        CommunicationServiceClient,
    ]
)
class CorporateInvoiceDispatcher(CorporateDispatcherBase):
    email_identifier = "b2b_invoice_mailer"

    def __init__(
        self,
        invoice_repository: InvoiceRepository,
        job_scheduler_service: JobSchedulerService,
        communication_audit_service: CommunicationAuditService,
        booking_repository: BookingRepository,
        job_registry: JobRegistry,
        corporate_repository: CorporateReportRepository,
        slack_alert_client: SlackAlertServiceClient,
        tenant_settings: TenantSettings,
        communication_service_client: CommunicationServiceClient,
    ):
        super().__init__(
            corporate_repository,
            slack_alert_client,
            tenant_settings,
            communication_service_client,
            email_identifier=self.email_identifier,
        )
        self.invoice_repository = invoice_repository
        self.job_scheduler_service = job_scheduler_service
        self.communication_audit_service = communication_audit_service
        self.booking_repository = booking_repository

        job_registry.register(
            JobName.CORPORATE_INVOICE_DISPATCH_JOB_NAME, self.dispatch
        )

    def dispatch(self, invoice_id):
        invoice = self.invoice_repository.get_by_invoice_id(invoice_id)
        booking = self.booking_repository.get_by_id(invoice.booking_id)
        corporate: CorporateEntity = self.corporate_repository.get(
            invoice.booker_legal_entity_id
        )
        (
            billing_team_email,
            billing_team_name,
        ) = self.tenant_settings.get_billing_team_contact()
        if (
            corporate.invoice_types_for_checkout_dispatch
            and invoice.invoice_type in corporate.invoice_types_for_checkout_dispatch
        ):
            context_data = {
                "customer_name": str(invoice.issued_to.name),
                "hotel_name": invoice.vendor_details.get("vendor_name"),
                "invoice_number": invoice.invoice_number,
                "due_amount": str(invoice.due_amount),
                "billing_date": invoice.invoice_date.isoformat(),
                "billing_team_email": billing_team_email,
            }
            subject = f"Your Invoice for Booking ID: {invoice.booking_meta.reference_number} is ready"
            external_communication_id = self._send_document_to_customer(
                booking,
                invoice,
                corporate,
                subject,
                context_data,
                from_email=billing_team_email,
                sender_name=billing_team_name,
            )
            self.communication_audit_service.audit_corporate_invoice_dispatch(
                invoice=invoice,
                corporate=corporate,
                external_communication_id=external_communication_id,
            )
            return JobResultDto.success()
        return JobResultDto.success(
            remark="Either dispatch is disabled for corporate or invoice type not supported for dispatch"
        )

    def was_document_already_dispatched(self, invoice: Invoice):
        return self.communication_audit_service.was_invoice_dispatched(invoice)

    def schedule_dispatch_job(self, invoice: Invoice):
        self.job_scheduler_service.schedule_corporate_invoice_dispatch_job(
            invoice.invoice_id
        )

    def capture_communication_failure_log(
        self, invoice: Invoice, error_message: str, corporate=None
    ):
        return self.communication_audit_service.audit_corporate_invoice_dispatch(
            invoice=invoice,
            corporate=corporate,
            status=CommunicationStatus.FAILED,
            message=error_message,
        )

    def doc_type(self, invoice: Invoice):
        return Invoice.__name__

    def dispatch_for_bookings(self, booking_ids):
        invoices = self.invoice_repository.get_by_booking_ids(booking_ids)
        for invoice in invoices:
            self.schedule_dispatch_if_eligible(invoice)

    def scheduled_manual_dispatch(self, invoice_ids):
        invoices = self.invoice_repository.get_by_invoice_ids(invoice_ids)
        for invoice in invoices:
            self.schedule_dispatch_if_eligible(invoice)
