import logging

from thsc.crs.convertors.booking_convertors import BookingConvertor
from thsc.crs.entities.billing import Bill as THS<PERSON>Bill
from thsc.crs.entities.booking import Booking as THSCBooking

from finance_erp.application.common.resilient_ingestion_handler import (
    ResilientDataIngestionCommandHandler,
)
from finance_erp.common.constants import IngestionJobs
from finance_erp.domain.crs.entity.booking import Booking
from finance_erp.domain.crs.factory.booking_factory import BookingFactory
from finance_erp.domain.crs.repository.booking_repository import BookingRepository
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(dependencies=[BookingRepository])
class CrsBookingIngestionCommandHandler(ResilientDataIngestionCommandHandler):
    def __init__(
        self,
        booking_repository: BookingRepository,
    ):
        self.booking_repository = booking_repository
        super().__init__(IngestionJobs.BOOKING_INGESTION)

    def _execute(self, request_data, event_id, from_async_job=False):
        thsc_booking: THSCBooking = self._get_thsc_booking(
            request_data, re_fetch_invoice=from_async_job
        )
        return self._ingest(thsc_booking)

    def _ingest(self, thsc_booking: THSCBooking):
        thsc_bill = THSCBill.get(thsc_booking.bill_id)
        attachments = thsc_booking.get_attachments()

        existing_booking: Booking = self.booking_repository.get_by_id(
            thsc_booking.booking_id
        )
        if existing_booking:
            return self._update_booking(
                existing_booking, thsc_booking, thsc_bill, attachments
            )

        booking = BookingFactory.build(thsc_booking, thsc_bill, attachments)

        return self.booking_repository.save(booking)

    def _update_booking(self, booking: Booking, thsc_booking, thsc_bill, attachments):
        logger.info(f"Booking already exists with id: {booking.booking_id}")
        booking.update_from_thsc_data(thsc_booking, thsc_bill, attachments)
        return self.booking_repository.update(booking)

    def ingest(self, booking_id):
        thsc_booking: THSCBooking = THSCBooking.get(booking_id)
        return self._ingest(thsc_booking)

    @staticmethod
    def _get_thsc_booking(request_data, re_fetch_invoice) -> THSCBooking:
        if re_fetch_invoice:
            return THSCBooking.get(request_data.get("booking_id"))
        return BookingConvertor().from_dict(request_data)
