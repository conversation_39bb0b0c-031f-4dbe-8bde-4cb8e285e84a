import logging

from finance_erp.application.common.base_ingestion_handler import (
    BaseIngestionCommandHandler,
)
from finance_erp.common.constants import NavisionReports
from finance_erp.common.utils.checksum_creator import generate_check_sum
from finance_erp.domain.hotel.factory.hotel_factory import HotelFactory
from finance_erp.domain.hotel.repository.hotel_repository import HotelReportRepository
from finance_erp.domain.shared_kernel.audit.process_level.ingestion_audit_service import (
    IngestionAuditService,
)
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(dependencies=[HotelReportRepository, IngestionAuditService])
class BulkIngestHotelReportCommandHandler(BaseIngestionCommandHandler):
    def __init__(
        self,
        hotel_reports_repository: HotelReportRepository,
        ingestion_audit_service: IngestionAuditService,
    ):
        super().__init__(ingestion_audit_service)
        self.hotel_reports_repository = hotel_reports_repository

    def _run_ingestion(self, ingestion_data_list, report_name, event_id):
        hotel_reports_to_update, new_hotel_reports = self._segregate_records(
            ingestion_data_list, event_id
        )

        if new_hotel_reports:
            self.hotel_reports_repository.insert_many(new_hotel_reports)
        self.ingestion_audit_service.record_ingestion_data_insertion_event(
            event_id,
            report_name,
            stats=dict(no_of_inserted_records=len(new_hotel_reports)),
        )
        if hotel_reports_to_update:
            self.hotel_reports_repository.bulk_update_records(hotel_reports_to_update)
        self.ingestion_audit_service.record_ingestion_data_update_event(
            event_id,
            report_name,
            stats=dict(no_of_updated_records=len(hotel_reports_to_update)),
        )
        return f"Successfully ingested {report_name} event {event_id}"

    def _segregate_records(self, report_dicts: list, event_id):
        uuids_of_records = self._get_unique_ids(report_dicts)
        existing_reports = self.hotel_reports_repository.get_by_ids(
            uuids_of_records, for_update=True
        )
        uuids_of_existing_reports = {
            report.get_unique_identifier() for report in existing_reports
        }
        uuids_to_record_map = {
            report.get_unique_identifier(): report for report in existing_reports
        }
        (
            data_to_update,
            data_to_insert,
            uuids_of_new_records_to_insert,
            uuids_of_updatable_existing_record,
        ) = ([], [], set(), set())
        existing_records_without_data_change = set()
        for item in report_dicts:
            if item["hotel_code"] in uuids_of_existing_reports:
                record = uuids_to_record_map[item["hotel_code"]]
                data_checksum = generate_check_sum(
                    NavisionReports.HOTEL_REPORT, data_dict=item
                )
                if record.data_checksum != data_checksum:
                    record.update(item)
                    data_to_update.append(record)
                    uuids_of_updatable_existing_record.add(
                        record.get_unique_identifier()
                    )
                else:
                    existing_records_without_data_change.add(
                        record.get_unique_identifier()
                    )
            else:
                uuids_of_new_records_to_insert.add(item["hotel_code"])
                data_to_insert.append(HotelFactory.create_hotel_from_data_dict(item))
        # as navision allows update all the records are eligible for ingestion
        self._record_report_filtering_stats(
            existing_records_without_data_change,
            uuids_of_updatable_existing_record,
            uuids_of_new_records_to_insert,
            event_id,
        )
        return data_to_update, data_to_insert

    def _record_report_filtering_stats(
        self,
        existing_records_without_data_change,
        uuids_of_updatable_existing_record,
        uuids_of_new_records_to_insert,
        event_id,
    ):
        # TODO USE value object
        stats = dict(
            already_pushed_reports=None,
            count_of_pushed_records=None,
            records_elligible_for_ingestion=None,
            elligible_count=None,
            existing_records_without_data_change=existing_records_without_data_change,
            existing_records_without_data_change_count=len(
                existing_records_without_data_change
            ),
            new_records=uuids_of_new_records_to_insert,
            new_records_count=len(uuids_of_new_records_to_insert),
            updatable_existing_record=uuids_of_updatable_existing_record,
            updatable_existing_record_count=len(uuids_of_updatable_existing_record),
        )
        self.ingestion_audit_service.record_ingestion_data_filter_event(
            event_id, NavisionReports.HOTEL_REPORT, stats
        )

    def _get_unique_ids(self, ingestion_data_list):
        return [rc["hotel_code"] for rc in ingestion_data_list]
