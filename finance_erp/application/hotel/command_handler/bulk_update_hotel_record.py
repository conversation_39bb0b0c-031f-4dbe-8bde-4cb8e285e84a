from typing import List

from ths_common.exceptions import ResourceNotFound

from finance_erp.domain.hotel.entity.hotel import UPDATABLE_FIELDS, HotelEntity
from finance_erp.domain.hotel.repository.hotel_repository import HotelReportRepository
from object_registry import register_instance


@register_instance(dependencies=[HotelReportRepository])
class BulkUpdateHotelReportCommandHandler:
    def __init__(self, hotel_reports_repository: HotelReportRepository):
        self.hotel_reports_repository = hotel_reports_repository

    def handle(self, request_data_list: list):
        uuids = [rc["hotel_code"] for rc in request_data_list]
        hotel_report_reports: List[
            HotelEntity
        ] = self.hotel_reports_repository.get_by_ids(uuids, for_update=True)
        report_map = {ag.hotel_code: ag for ag in hotel_report_reports}
        hotel_report_reports = []
        for request_data in request_data_list:
            uu_id = request_data["hotel_code"]
            if uu_id not in report_map:
                raise ResourceNotFound(
                    "HotelRecord", extra_payload=dict(hotel_code=uu_id)
                )
            hotel_report_report: HotelEntity = report_map[uu_id]
            # TODO: Handle updates elegantly
            for attr in UPDATABLE_FIELDS:
                if attr in request_data:
                    setattr(hotel_report_report, attr, request_data[attr])
            hotel_report_reports.append(hotel_report_report)
        self.hotel_reports_repository.bulk_update_records(hotel_report_reports)
        return hotel_report_reports
