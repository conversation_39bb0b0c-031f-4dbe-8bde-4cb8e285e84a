from datetime import datetime

from core.common.utils.date import utc_to_ist
from finance_erp.application.hotel.dtos.get_hotel_report_dto import GetHotelReportDto
from finance_erp.domain.hotel.repository.hotel_repository import HotelReportRepository
from object_registry import register_instance


@register_instance(dependencies=[HotelReportRepository])
class FetchHotelCommandHandler:
    def __init__(self, hotel_reports_repository: HotelReportRepository):
        self.hotel_reports_repository = hotel_reports_repository

    def handle(self, input_criteria: GetHotelReportDto):
        if input_criteria.hotel_codes:
            input_criteria.hotel_codes = (
                input_criteria.hotel_codes.split(",")
                if input_criteria.hotel_codes
                else None
            )
            return self.hotel_reports_repository.get_by_ids(input_criteria.hotel_codes)
        else:
            if not input_criteria.from_date:
                input_criteria.from_date = utc_to_ist(datetime.utcnow()).date()
            if not input_criteria.to_date:
                input_criteria.to_date = utc_to_ist(datetime.utcnow()).date()
            return self.hotel_reports_repository.get_reports_by_date_range(
                from_date=input_criteria.from_date, to_date=input_criteria.to_date
            )
