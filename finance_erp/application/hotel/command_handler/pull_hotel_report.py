from finance_erp.application.common.base_data_pull_handler import (
    BaseDataPullCommandHandler,
)
from finance_erp.application.hotel.command_handler.bulk_ingest_hotel_report import (
    BulkIngestHotelReportCommandHandler,
)
from finance_erp.application.hotel.dtos.generate_hotel_report_dto import (
    GenerateHotelReportDto,
)
from finance_erp.domain.hotel.dto.hotel_report_dto import HotelReportDto
from finance_erp.domain.shared_kernel.audit.process_level.data_pull_audit_service import (
    DataPullAuditService,
)
from finance_erp.infrastructure.external_clients.catalog_service_client import (
    CatalogServiceClient,
)
from object_registry import register_instance


@register_instance(
    dependencies=[
        DataPullAuditService,
        CatalogServiceClient,
        BulkIngestHotelReportCommandHandler,
    ]
)
class BulkPullHotelReportCommandHandler(BaseDataPullCommandHandler):
    def __init__(
        self,
        data_pull_audit_service: DataPullAuditService,
        catalog_service_client: CatalogServiceClient,
        hotel_data_ingestion_handler: BulkIngestHotelReportCommandHandler,
    ):
        super().__init__(data_pull_audit_service)
        self.catalog_service_client = catalog_service_client
        self.hotel_data_ingestion_handler = hotel_data_ingestion_handler

    def _pull_and_ingest_data(self, request_data, report_name, event_id):
        hotel_data_list = self.catalog_service_client.get_hotels(
            request_data,
            fields="id,name,location,property_details,cost_center_id",
            status="LIVE",
        )
        if not hotel_data_list:
            return []

        data_to_ingest = [HotelReportDto(**data).to_dict() for data in hotel_data_list]
        data = self.hotel_data_ingestion_handler.handle(
            data_to_ingest, report_name, event_id
        )
        return data

    def _get_request_parser(self):
        return GenerateHotelReportDto
