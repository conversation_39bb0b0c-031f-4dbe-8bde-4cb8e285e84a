from ths_common.exceptions import ValidationException

from finance_erp.application.common.base_data_push_handler import (
    BaseDataPushCommandHandler,
)
from finance_erp.application.hotel.dtos.bulk_hotel_push_request_dto import (
    BulkHotelPushRequestDto,
)
from finance_erp.application.hotel_settings.tenant_settings import TenantSettings
from finance_erp.common.constants import DataPushNavKeys
from finance_erp.common.decorators import session_manager
from finance_erp.common.error_codes import ApplicationErrors
from finance_erp.common.schema.hotel import HotelPushRequestSchema
from finance_erp.domain.hotel.entity.hotel import HotelEntity
from finance_erp.domain.hotel.repository.hotel_repository import HotelReportRepository
from finance_erp.domain.shared_kernel.audit.process_level.nav_push_audit_service import (
    NavPushAuditService,
)
from finance_erp.infrastructure.alerting.slack_alert_service_client import (
    SlackAlertServiceClient,
)
from finance_erp.infrastructure.external_clients.business_central_client import (
    BusinessCentralClient,
)
from object_registry import register_instance


@register_instance(
    dependencies=[
        BusinessCentralClient,
        SlackAlertServiceClient,
        HotelReportRepository,
        NavPushAuditService,
        TenantSettings,
    ]
)
class BulkPushHotelReportCommandHandler(BaseDataPushCommandHandler):
    def __init__(
        self,
        external_data_push_client: BusinessCentralClient,
        slack_alert_client: SlackAlertServiceClient,
        hotel_reports_repository: HotelReportRepository,
        nav_push_audit_service: NavPushAuditService,
        tenant_settings: TenantSettings,
    ):
        super().__init__(
            external_data_push_client,
            slack_alert_client,
            nav_push_audit_service,
            hotel_reports_repository,
            tenant_settings,
        )
        self.hotel_reports_repository = hotel_reports_repository
        self.tenant_settings = tenant_settings

    def _get_schema_and_json_key(self):
        return HotelPushRequestSchema, DataPushNavKeys.HOTEL

    def _get_records_to_push(self, request_dto: BulkHotelPushRequestDto):
        if request_dto.hotel_codes:
            return self.hotel_reports_repository.get_by_ids(
                request_dto.hotel_codes, for_update=True
            )
        return self.hotel_reports_repository.get_eligible_records_to_push()

    @session_manager(commit=True)
    def _capture_failure(self, record: HotelEntity, reason):
        record.capture_data_push_failure(reason)
        self.hotel_reports_repository.update_record(record)

    @session_manager(commit=True)
    def _capture_success(self, record: HotelEntity):
        record.capture_data_push_success()
        self.hotel_reports_repository.update_record(record)

    def _get_request_parser(self):
        return BulkHotelPushRequestDto

    @staticmethod
    def _validate_records(records):
        for record in records:
            if not record.verified:
                raise ValidationException(
                    ApplicationErrors.DATA_PUSH_NOT_ALLOWED_FOR_UNVERIFIED_RECORDS,
                    extra_payload=dict(uuid=record.get_unique_identifier()),
                )

    def _update_records(self, records):
        self.hotel_reports_repository.bulk_update_records(records)
