from typing import Optional


class CorporateCommunicationChannelConfigDto:
    def __init__(
        self,
        channel: str,
        sub_channels: Optional[list[str]] = None,
    ):
        self.channel = channel
        self.sub_channels = sub_channels


class CorporateCommChannelRestriction:
    def __init__(self, invoice=None, stay_summary=None, **kwargs):
        self.invoice_configs = dict()
        self.stay_summary_config = dict()
        if invoice:
            for config in invoice:
                config_dto = CorporateCommunicationChannelConfigDto(**config)
                self.invoice_configs[config_dto.channel] = config_dto
        if stay_summary:
            for config in stay_summary:
                config_dto = CorporateCommunicationChannelConfigDto(**config)
                self.stay_summary_config[config_dto.channel] = config_dto

    def is_stay_summary_communication_enabled(self, channel: str, sub_channel: str):
        return self._is_channel_enabled(
            self.stay_summary_config,
            channel,
            sub_channel,
        )

    def is_invoice_communication_enabled(self, channel: str, sub_channel: str):
        return self._is_channel_enabled(
            self.invoice_configs,
            channel,
            sub_channel,
        )

    @staticmethod
    def _is_channel_enabled(config, channel: str, sub_channel: str):
        if not config:
            return False
        channel_config = config.get(channel)
        if not channel_config:
            return False
        if not channel_config.sub_channels:
            # If sub_channels are not defined,
            # then its enabled for all sub_channels in the channel
            return True
        return sub_channel in channel_config.sub_channels
