from typing import Optional

from flask import current_app as app
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import get_current_tenant_id
from treebo_commons.utils import dateutils

from finance_erp.application.hotel_settings.configs.corporate_communication_config import (
    CorporateCommChannelRestriction,
)
from finance_erp.application.hotel_settings.configs.payment_debto_mapping import (
    PaymentDebtorCodeMappingDto,
)
from finance_erp.application.hotel_settings.constants import (
    TenantConstants,
    TenantSettingsDefaultValues,
)
from finance_erp.domain.back_office.constants import PaymentModes
from finance_erp.infrastructure.external_clients.catalog_service_client import (
    CatalogServiceClient,
)
from object_registry import register_instance


@register_instance(dependencies=[CatalogServiceClient])
class TenantSettings(object):
    def __init__(self, catalog_service_client: CatalogServiceClient):
        self.catalog_service_client = catalog_service_client
        self.tenant_config_dict = dict()
        self.last_refresh_time = dict()
        # pylint: disable=not-an-iterable
        for tenant in TenantClient.get_active_tenants():
            self.tenant_config_dict[tenant.tenant_id] = dict()
            self.last_refresh_time[tenant.tenant_id] = dict()

    def _get_tenant_config(self, hotel_id=None):
        current_tenant_id = get_current_tenant_id() or TenantClient.get_default_tenant()
        tenant_config_dict = self.tenant_config_dict.get(current_tenant_id)
        last_refresh_time_dict = self.last_refresh_time.get(current_tenant_id)

        global_config = tenant_config_dict.get("global")
        global_config_last_refresh_time = last_refresh_time_dict.get("global")

        hotel_config = (
            tenant_config_dict.get(hotel_id)
            if tenant_config_dict and hotel_id
            else None
        )
        hotel_config_last_refresh_time = (
            last_refresh_time_dict.get(hotel_id)
            if last_refresh_time_dict and hotel_id
            else None
        )

        tenant_config = hotel_config if hotel_id else global_config
        last_refresh_time = (
            hotel_config_last_refresh_time
            if hotel_id
            else global_config_last_refresh_time
        )

        if tenant_config is None or last_refresh_time < dateutils.subtract(
            dateutils.current_datetime(), minutes=5
        ):
            tenant_configs = self.catalog_service_client.get_tenant_configs(hotel_id)
            tenant_config = {config.config_name: config for config in tenant_configs}
            if hotel_id:
                self.tenant_config_dict[current_tenant_id][hotel_id] = tenant_config
                self.last_refresh_time[current_tenant_id][
                    hotel_id
                ] = dateutils.current_datetime()
            else:
                self.tenant_config_dict[current_tenant_id]["global"] = tenant_config
                self.last_refresh_time[current_tenant_id][
                    "global"
                ] = dateutils.current_datetime()
        return tenant_config

    def get_setting_value(self, setting_name, hotel_id=None):
        tenant_config = self._get_tenant_config(hotel_id)
        if not tenant_config:
            return None
        setting = tenant_config.get(setting_name)
        if not setting:
            return None
        return setting.get_config_value()

    def fetch_corporate_comm_channel_config(
        self, hotel_id=None
    ) -> CorporateCommChannelRestriction:
        config = self.get_setting_value(
            TenantConstants.CORPORATE_COMM_CHANNEL_CONFIG,
            hotel_id=hotel_id,
        )
        return CorporateCommChannelRestriction(**config) if config else None

    def is_credit_note_communication_enabled(self, hotel_id=None):
        # default to true if setting is not present
        return (
            self.get_setting_value(
                TenantConstants.CREDIT_NOTE_COMM_ENABLED,
                hotel_id=hotel_id,
            )
            or True
        )

    def get_business_central_blacklisted_hotels(self):
        tenant_config = self._get_tenant_config()
        if not tenant_config:
            return []

        excluded_hotels = tenant_config.get(
            TenantConstants.BUSINESS_CENTRAL_BLACKLISTED_HOTELS
        )
        if not excluded_hotels:
            return []
        return excluded_hotels.get_config_value()

    def get_b2b_script_receiver_emails(self, hotel_id=None) -> list[str]:
        return self.get_setting_value(
            TenantConstants.B2B_SCRIPT_RECEIVER_EMAILS,
            hotel_id=hotel_id,
        ) or (
            TenantSettingsDefaultValues.B2B_SCRIPT_RECEIVER_EMAILS
            if app.config.get("ENV") == "production"
            else []
        )

    def get_billing_team_contact(self, hotel_id=None) -> [str, str]:
        contact = (
            self.get_setting_value(
                TenantConstants.BILLING_TEAM_CONTACT,
                hotel_id=hotel_id,
            )
            or TenantSettingsDefaultValues.BILLING_TEAM_CONTACT
        )
        return contact.get("email"), contact.get("name")

    def get_payment_debtor_mapping(self, hotel_id=None):
        configs = self.get_setting_value(
            TenantConstants.PAYMENT_DEBTOR_MAPPING,
            hotel_id=hotel_id,
        )
        return [PaymentDebtorCodeMappingDto(**config) for config in configs or []]

    def get_bo_fallback_account_number(self, hotel_id=None):
        return self.get_setting_value(
            TenantConstants.FALL_BACK_ACCOUNT_NUMBER,
            hotel_id=hotel_id,
        )

    def get_receive_later_payment_modes(self, hotel_id=None):
        return (
            self.get_setting_value(
                TenantConstants.RECEIVE_LATER_PAYMENT_MODES,
                hotel_id=hotel_id,
            )
            or []
        )

    def get_units_credit_pay_modes(self, hotel_id=None):
        return self.get_setting_value(
            TenantConstants.UNITS_CREDIT_PAYMENT_MODES,
            hotel_id=hotel_id,
        ) or [PaymentModes.CITY_LEDGER]

    def get_refund_exclusion_list_for_backoffice(self, hotel_id=None):
        return (
            self.get_setting_value(
                TenantConstants.BACKOFFICE_REFUND_EXCLUSION_LIST,
                hotel_id=hotel_id,
            )
            or []
        )

    def get_hotel_pocs_email(self, hotel_id=None):
        return (
            self.get_setting_value(
                TenantConstants.HOTEL_POCS_EMAIL,
                hotel_id=hotel_id,
            )
            or TenantSettingsDefaultValues.DEV_EMAILS
        )

    def get_enabled_backoffice_erps(self, hotel_id=None):
        return (
            self.get_setting_value(
                TenantConstants.ENABLED_BACKOFFICE_ERPS,
                hotel_id=hotel_id,
            )
            or []
        )

    def get_payment_config(self, hotel_id=None):
        return (
            self.get_setting_value(
                TenantConstants.PAYMENT_CONFIG,
                hotel_id=hotel_id,
            )
            or {}
        )

    def get_backoffice_hotel_code(self, hotel_id):
        return (
            self.get_setting_value(
                TenantConstants.BACKOFFICE_HOTEL_CODE,
                hotel_id=hotel_id,
            )
            or hotel_id
        )

    def should_include_cn_in_stay_summary(self, hotel_id=None) -> Optional[bool]:
        return self.get_setting_value(
            TenantConstants.INCLUDE_CREDIT_NOTE_IN_STAY_SUMMARY,
            hotel_id=hotel_id,
        )

    def get_channels_not_applicable_for_payment_push_to_bizc(self, hotel_id=None):
        return (
            self.get_setting_value(
                TenantConstants.CHANNELS_NOT_APPLICABLE_FOR_PAYMENT_PUSH_TO_BIZC,
                hotel_id=hotel_id,
            )
            or []
        )
