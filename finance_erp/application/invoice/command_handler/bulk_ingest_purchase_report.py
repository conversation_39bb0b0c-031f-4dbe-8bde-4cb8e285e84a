import logging

from finance_erp.application.common.base_ingestion_handler import (
    BaseIngestionCommandHandler,
)
from finance_erp.common.constants import NavisionReports
from finance_erp.domain.reseller.factory.purchase_factory import PurchaseFactory
from finance_erp.domain.reseller.repository.purchase_invoice_repository import (
    PurchaseInvoiceRepository,
)
from finance_erp.domain.shared_kernel.audit.process_level.ingestion_audit_service import (
    IngestionAuditService,
)
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(dependencies=[PurchaseInvoiceRepository, IngestionAuditService])
class BulkIngestPurchaseReportCommandHandler(BaseIngestionCommandHandler):
    def __init__(
        self,
        purchase_reports_repository: PurchaseInvoiceRepository,
        ingestion_audit_service: IngestionAuditService,
    ):
        self.purchase_reports_repository = purchase_reports_repository
        super().__init__(ingestion_audit_service)

    def _run_ingestion(self, ingestion_data_list, report_name, event_id):
        purchase_reports_to_update, new_purchase_reports = self._segregate_records(
            ingestion_data_list, event_id
        )
        if new_purchase_reports:
            self.purchase_reports_repository.insert_many(new_purchase_reports)
        self.ingestion_audit_service.record_ingestion_data_insertion_event(
            event_id,
            report_name,
            stats=dict(no_of_inserted_records=len(new_purchase_reports)),
        )
        if purchase_reports_to_update:
            self.purchase_reports_repository.bulk_update_records(
                purchase_reports_to_update
            )
        self.ingestion_audit_service.record_ingestion_data_update_event(
            event_id,
            report_name,
            stats=dict(no_of_updated_records=len(purchase_reports_to_update)),
        )
        return f"Successfully ingested {report_name} event {event_id}"

    def _segregate_records(self, purchase_report_dicts: list, event_id):
        (
            uuids_of_already_pushed_invoice_reports,
            non_pushed_purchase_report_dicts,
        ) = self._filter_out_already_pushed_records(purchase_report_dicts)
        uuids_of_nn_pushed_records = self._get_unique_ids(
            non_pushed_purchase_report_dicts
        )
        updatable_existing_reports = self.purchase_reports_repository.get_by_ids(
            uuids_of_nn_pushed_records, for_update=True
        )
        uuids_of_updatable_existing_record = {
            inv_re.get_unique_identifier() for inv_re in updatable_existing_reports
        }
        uuids_to_record_map = {
            report.get_unique_identifier(): report
            for report in updatable_existing_reports
        }
        data_to_update, data_to_insert, uuids_of_new_records_to_insert = [], [], set()
        for item in non_pushed_purchase_report_dicts:
            if item["unique_ref_id"] in uuids_of_updatable_existing_record:
                record = uuids_to_record_map.get(item["unique_ref_id"])
                record.update(item)
                data_to_update.append(record)
            else:
                uuids_of_new_records_to_insert.add(item["unique_ref_id"])
                data_to_insert.append(PurchaseFactory.create_from_data_dict(item))
        self._record_report_filtering_stats(
            uuids_of_already_pushed_invoice_reports,
            uuids_of_nn_pushed_records,
            uuids_of_updatable_existing_record,
            uuids_of_new_records_to_insert,
            event_id,
        )
        return data_to_update, data_to_insert

    def _filter_out_already_pushed_records(self, purchase_report_dicts):
        invoice_numbers = {data["invoice_number"] for data in purchase_report_dicts}
        already_pushed_invoice_report = self.purchase_reports_repository.load_all(
            invoice_numbers=list(invoice_numbers), only_pushed_records=True
        )
        already_pushed_invoices_pool = {
            inv_re.invoice_number for inv_re in already_pushed_invoice_report
        }
        purchase_report_dicts = [
            report
            for report in purchase_report_dicts
            if report["invoice_number"] not in already_pushed_invoices_pool
        ]
        uuids_of_already_pushed_invoice_reports = {
            inv_re.get_unique_identifier() for inv_re in already_pushed_invoice_report
        }
        return uuids_of_already_pushed_invoice_reports, purchase_report_dicts

    def _record_report_filtering_stats(
        self,
        uuids_of_already_pushed_invoice_reports,
        uuids_of_nn_pushed_records,
        uuids_of_updatable_existing_record,
        uuids_of_new_records_to_insert,
        event_id,
    ):
        stats = dict(
            already_pushed_reports=uuids_of_already_pushed_invoice_reports,
            count_of_pushed_records=len(uuids_of_already_pushed_invoice_reports),
            records_elligible_for_ingestion=uuids_of_nn_pushed_records,
            elligible_count=len(uuids_of_nn_pushed_records),
            new_records=uuids_of_new_records_to_insert,
            new_records_count=len(uuids_of_new_records_to_insert),
            updatable_existing_record=uuids_of_updatable_existing_record,
            updatable_existing_record_count=len(uuids_of_updatable_existing_record),
        )
        self.ingestion_audit_service.record_ingestion_data_filter_event(
            event_id, NavisionReports.PURCHASE_INVOICE_REPORT, stats
        )

    def _get_unique_ids(self, ingestion_data_list):
        return [rc["unique_ref_id"] for rc in ingestion_data_list]
