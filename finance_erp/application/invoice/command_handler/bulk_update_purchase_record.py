from typing import List

from ths_common.exceptions import ResourceNotFound, ValidationException

from finance_erp.common.error_codes import ApplicationErrors
from finance_erp.domain.reseller.entity.purchase import PurchaseInvoiceEntity
from finance_erp.domain.reseller.repository.purchase_invoice_repository import (
    PurchaseInvoiceRepository,
)
from object_registry import register_instance


@register_instance(dependencies=[PurchaseInvoiceRepository])
class BulkUpdatePurchaseReportCommandHandler:
    def __init__(self, purchase_reports_repository: PurchaseInvoiceRepository):
        self.purchase_reports_repository = purchase_reports_repository

    def handle(self, request_data_list: list):
        uuids = [rc["unique_ref_id"] for rc in request_data_list]
        purchase_report_reports: List[
            PurchaseInvoiceEntity
        ] = self.purchase_reports_repository.get_by_ids(uuids, for_update=True)
        report_map = {ag.unique_ref_id: ag for ag in purchase_report_reports}
        purchase_report_reports = []
        for request_data in request_data_list:
            uu_id = request_data["unique_ref_id"]
            if uu_id not in report_map:
                raise ResourceNotFound("PurchaseRecord", extra_payload=dict(uuid=uu_id))
            purchase_report_report: PurchaseInvoiceEntity = report_map[uu_id]
            if purchase_report_report.pushed:
                raise ValidationException(
                    ApplicationErrors.CANNOT_UPDATE_PUSHED_RECORDS,
                    extra_payload=dict(unique_ref_id=uu_id),
                )
            self._update_entity_attributes(purchase_report_report, request_data)
            purchase_report_reports.append(purchase_report_report)
        self.purchase_reports_repository.bulk_update_records(purchase_report_reports)
        return purchase_report_reports

    def _update_entity_attributes(self, entity, data):
        for key, value in data.items():
            if hasattr(entity, key):
                current_attr = getattr(entity, key)
                if isinstance(value, dict) and isinstance(current_attr, object):
                    self._update_entity_attributes(current_attr, value)
                else:
                    setattr(entity, key, value)
