from collections import defaultdict
from typing import List

from ths_common.exceptions import ValidationException

from finance_erp.common.error_codes import ApplicationErrors
from finance_erp.domain.reseller.entity.sales import SalesInvoiceEntity
from finance_erp.domain.reseller.entity.sales_summary import SalesSummaryEntity
from finance_erp.domain.reseller.models import SalesInvoiceStatus
from finance_erp.domain.reseller.repository.sales_invoice_repository import (
    SalesInvoiceRepository,
)
from finance_erp.domain.reseller.repository.sales_summary_repository import (
    SalesInvoiceSummaryRepository,
)
from object_registry import register_instance


@register_instance(dependencies=[SalesInvoiceRepository, SalesInvoiceSummaryRepository])
class BulkUpdateSalesReportCommandHandler:
    def __init__(
        self,
        sales_invoice_repository: SalesInvoiceRepository,
        sales_invoice_summary_repository: SalesInvoiceSummaryRepository,
    ):
        self.sales_invoice_repository = sales_invoice_repository
        self.sales_invoice_summary_repository = sales_invoice_summary_repository

    def handle(self, request_data_list: list):
        uuids = [rc["unique_ref_id"] for rc in request_data_list]
        uu_id_to_request_data_mapping = {
            req_data["unique_ref_id"]: req_data for req_data in request_data_list
        }
        sales_records: List[
            SalesInvoiceEntity
        ] = self.sales_invoice_repository.get_by_ids(uuids, for_update=True)
        if any(record.status == SalesInvoiceStatus.PUSHED for record in sales_records):
            raise ValidationException(
                ApplicationErrors.CANNOT_UPDATE_PUSHED_RECORDS,
                extra_payload=dict(
                    already_pushed_uu_ids={
                        ag.unique_ref_id
                        for ag in sales_records
                        if ag.status == SalesInvoiceStatus.PUSHED
                    }
                ),
            )
        aggregated_sales_records = [
            rec
            for rec in sales_records
            if rec.status
            in [SalesInvoiceStatus.AGGREGATED, SalesInvoiceStatus.FAILED_TO_PUSH]
        ]
        non_aggregated_sales_records = [
            rec
            for rec in sales_records
            if rec.status
            in [SalesInvoiceStatus.INGESTED, SalesInvoiceStatus.EXCLUDED_FOR_DATA_PUSH]
        ]
        updated_sales_record_got_aggregated = self._update_aggregated_records(
            aggregated_sales_records, uu_id_to_request_data_mapping
        )
        self._update_non_aggregated_records(
            non_aggregated_sales_records, uu_id_to_request_data_mapping
        )
        return updated_sales_record_got_aggregated + non_aggregated_sales_records

    # Updating all the sales entries attached with Non-Pushed Aggregated Summary
    def _update_aggregated_records(
        self, aggregated_sales_records, uu_id_to_request_data_mapping
    ):
        if not aggregated_sales_records:
            return []
        aggregated_ids = {ag.aggregation_id for ag in aggregated_sales_records}
        sales_data_attached_to_summaries = (
            self.sales_invoice_repository.get_by_aggregation_ids(aggregated_ids)
        )
        sales_summary_records: List[
            SalesSummaryEntity
        ] = self.sales_invoice_summary_repository.get_by_ids(
            aggregated_ids, for_update=True
        )
        aggregated_entries_against_aggregation_ids = defaultdict(lambda: [])
        for record in sales_data_attached_to_summaries:
            aggregated_entries_against_aggregation_ids[record.aggregation_id].append(
                record
            )

        for sales_summary_entry in sales_summary_records:
            for sales_record in aggregated_entries_against_aggregation_ids[
                sales_summary_entry.unique_ref_id
            ]:
                if uu_id_to_request_data_mapping.get(sales_record.unique_ref_id):
                    self._update_entity_attributes(
                        sales_record,
                        uu_id_to_request_data_mapping[sales_record.unique_ref_id],
                    )
                sales_record.status = SalesInvoiceStatus.INGESTED
                sales_record.aggregation_id = None
                sales_record.aggregated_at = None
            sales_summary_entry.deleted = True
        self.sales_invoice_repository.bulk_update_records(
            sales_data_attached_to_summaries
        )
        self.sales_invoice_summary_repository.bulk_update_records(sales_summary_records)
        return sales_data_attached_to_summaries

    def _update_non_aggregated_records(
        self, non_aggregated_sales_records, uu_id_to_request_data_mapping
    ):
        for sales_record in non_aggregated_sales_records:
            self._update_entity_attributes(
                sales_record, uu_id_to_request_data_mapping[sales_record.unique_ref_id]
            )
        self.sales_invoice_repository.bulk_update_records(non_aggregated_sales_records)

    def _update_entity_attributes(self, entity, data):
        for key, value in data.items():
            if hasattr(entity, key):
                current_attr = getattr(entity, key)
                if isinstance(value, dict) and isinstance(current_attr, object):
                    self._update_entity_attributes(current_attr, value)
                else:
                    setattr(entity, key, value)
