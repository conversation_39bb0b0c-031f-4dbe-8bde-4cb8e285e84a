import logging
import uuid
from collections import defaultdict
from concurrent.futures import ThreadPoolExecutor
from datetime import date, datetime, timedelta
from typing import List, Optional

from flask import current_app as app

from finance_erp.async_job.job.job_constants import JobName
from finance_erp.async_job.job_registry import JobRegistry
from finance_erp.async_job.job_scheduler_service import JobSchedulerService
from finance_erp.common.decorators import session_manager
from finance_erp.common.utils.s3_file_archiver import (
    FileMeta,
    InputFileMetaForFileArchiver,
    S3FileArchiver,
)
from finance_erp.domain.company_profile.repository.corporate_repository import (
    CorporateReportRepository,
)
from finance_erp.domain.crs.entity.credit_note import CreditNote
from finance_erp.domain.crs.entity.invoice import Invoice
from finance_erp.domain.crs.repository.credit_note_repository import (
    CreditNoteRepository,
)
from finance_erp.domain.crs.repository.invoice_repository import InvoiceRepository
from finance_erp.infrastructure.external_clients.communication_service_client import (
    CommunicationServiceClient,
)
from object_registry import register_instance

TMP_FOLDER = "/tmp"
EXPIRY_TIME = 1 * 3600
APPLICATION_NAME = "fin-erp"
email_identifier = "finance_erp_invoice"

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        CreditNoteRepository,
        InvoiceRepository,
        JobRegistry,
        JobSchedulerService,
        CommunicationServiceClient,
        CorporateReportRepository,
    ]
)
class DownloadInvoiceCommandHandler:
    def __init__(
        self,
        credit_note_repository: CreditNoteRepository,
        invoice_repository: InvoiceRepository,
        job_registry: JobRegistry,
        job_scheduler_service: JobSchedulerService,
        communication_service_client: CommunicationServiceClient,
        corporate_repository=CorporateReportRepository,
    ):
        self.credit_note_repository = credit_note_repository
        self.invoice_repository = invoice_repository
        self.job_scheduler_service = job_scheduler_service
        self.communication_service_client = communication_service_client
        self.corporate_repository = corporate_repository
        job_registry.register(JobName.INVOICE_DOWNLOAD_JOB_NAME, self.download_invoices)

    @session_manager(commit=True)
    def handle(self, request_dto):
        self.job_scheduler_service.schedule_download_invoice_job(
            start_date=request_dto.start_date,
            end_date=request_dto.end_date,
            hotel_id=request_dto.hotel_id,
            booker_legal_entity_ids=request_dto.booker_legal_entity_ids,
            invoice_numbers=request_dto.invoice_numbers,
            booking_reference_numbers=request_dto.booking_reference_numbers,
            invoice_type=request_dto.invoice_type,
            fetch_credit_notes=request_dto.fetch_credit_notes,
            user_email_id=request_dto.user_email_id,
        )

    def download_invoices(
        self,
        start_date,
        end_date,
        booker_legal_entity_ids,
        invoice_numbers,
        booking_reference_numbers,
        invoice_type,
        user_email_id,
        fetch_credit_notes=False,
        hotel_id=None,
    ):
        entries_for_sending_email = (
            booker_legal_entity_ids
            or invoice_numbers
            or booking_reference_numbers
            or []
        )
        today_date = date.today().strftime("%Y-%m-%d")
        start_date = (
            datetime.strptime(start_date, "%Y-%m-%d").date()
            if isinstance(start_date, str)
            else start_date
        )
        end_date = (
            datetime.strptime(end_date, "%Y-%m-%d").date()
            if isinstance(end_date, str)
            else end_date
        )

        (
            invoice_counts,
            credit_note_counts,
        ) = self._fetch_and_count_invoices_and_credit_notes(
            start_date=start_date,
            end_date=end_date,
            booker_legal_entity_ids=booker_legal_entity_ids,
            hotel_id=hotel_id,
            invoice_type=invoice_type,
            fetch_credit_notes=fetch_credit_notes,
            invoice_numbers=invoice_numbers,
            booking_reference_numbers=booking_reference_numbers,
        )
        (
            dates_for_download,
            max_entries_breached,
        ) = self._group_and_select_dates_for_download(
            invoice_counts, credit_note_counts
        )
        if not dates_for_download:
            logger.info("No invoices Or Credit Notes.")
            self._send_email_communication(
                entries_for_sending_email,
                user_email_id,
            )
            return

        invoices = []
        if not (fetch_credit_notes and not invoice_type):
            invoices = self.invoice_repository.get_invoices_for_booker_legal_entity_id(
                start_date=min(dates_for_download),
                end_date=max(dates_for_download),
                booker_legal_entity_ids=booker_legal_entity_ids,
                hotel_id=hotel_id,
                invoice_type=invoice_type,
                invoice_numbers=invoice_numbers,
                booking_reference_numbers=booking_reference_numbers,
            )

        credit_notes = []
        if fetch_credit_notes or not invoice_type:
            credit_notes = (
                self.credit_note_repository.get_credit_notes_for_booker_legal_entity_id(
                    start_date=min(dates_for_download),
                    end_date=max(dates_for_download),
                    booker_legal_entity_ids=booker_legal_entity_ids,
                    hotel_id=hotel_id,
                    invoice_numbers=invoice_numbers,
                    booking_reference_numbers=booking_reference_numbers,
                )
            )
        invoice_and_credit_note_zip_meta = self._generate_invoice_and_credit_note_zip(
            invoices, credit_notes
        )
        if not invoice_and_credit_note_zip_meta:
            logger.info("No invoices Or Credit Notes.")
            self._send_email_communication(
                entries_for_sending_email,
                user_email_id,
            )
            return
        logger.info(f"Zip file {invoice_and_credit_note_zip_meta.signed_url}")

        start_date_of_missed_entries, end_date_of_missed_entries = None, None
        if max_entries_breached:
            start_date_of_missed_entries = str(
                max(dates_for_download) + timedelta(days=1)
            )
            end_date_of_missed_entries = str(end_date) if end_date else today_date

        self._send_email_communication(
            entries_for_sending_email,
            user_email_id,
            invoice_and_credit_note_zip_meta=invoice_and_credit_note_zip_meta,
            start_date_of_missed_entries=start_date_of_missed_entries,
            end_date_of_missed_entries=end_date_of_missed_entries,
        )

    def _generate_invoice_and_credit_note_zip(
        self, invoices: List[Invoice], credit_notes: List[CreditNote]
    ) -> Optional[FileMeta]:
        files_to_process = []
        for invoice in invoices:
            files_to_process.append(
                InputFileMetaForFileArchiver(
                    file_url=invoice.invoice_url,
                    file_name=f"{invoice.invoice_number or uuid.uuid4()}.pdf",
                )
            )
        for credit_note in credit_notes:
            files_to_process.append(
                InputFileMetaForFileArchiver(
                    file_url=credit_note.credit_note_url,
                    file_name=f"{credit_note.credit_note_id or uuid.uuid4()}.pdf",
                )
            )
        if not files_to_process:
            return None
        return self._prepare_and_upload_zipped_booking_request(files_to_process)

    @staticmethod
    def _prepare_and_upload_zipped_booking_request(
        files_to_process: List[InputFileMetaForFileArchiver],
    ) -> FileMeta:
        invoice_archiver = S3FileArchiver("invoices")
        with ThreadPoolExecutor(
            max_workers=int(app.config["MAX_THREAD_FOR_PDF_GENERATION"])
        ) as executor:
            executor.map(
                invoice_archiver.download_file,
                files_to_process,
            )
        return invoice_archiver.prepare_archive_and_upload()

    def _send_email_communication(
        self,
        entries_for_sending_email,
        user_email_id,
        invoice_and_credit_note_zip_meta: FileMeta = None,
        start_date_of_missed_entries=None,
        end_date_of_missed_entries=None,
    ):

        if invoice_and_credit_note_zip_meta:
            subject = f"Your request to download invoices is successful! | {', '.join(entries_for_sending_email)}"
            context_data = dict(
                invoices_link=invoice_and_credit_note_zip_meta.signed_url,
            )
            if start_date_of_missed_entries:
                subject = f"Your request to download invoices was partially successful | {', '.join(entries_for_sending_email)}"
                context_data.update(
                    {
                        "start_date_of_missed_entries": start_date_of_missed_entries,
                        "end_date_of_missed_entries": end_date_of_missed_entries,
                    }
                )
        else:
            subject = f"Your request to download invoices was unsuccessful: No data found | {', '.join(entries_for_sending_email)}"
            context_data = {}
        self.communication_service_client.send_email(
            identifier=email_identifier,
            context_data=context_data,
            to_emails=[user_email_id],
            cc=[],
            subject=subject,
        )

    def _fetch_and_count_invoices_and_credit_notes(
        self,
        start_date,
        end_date,
        booker_legal_entity_ids,
        hotel_id,
        invoice_type,
        fetch_credit_notes,
        invoice_numbers,
        booking_reference_numbers,
    ):
        invoice_counts = []
        if not (fetch_credit_notes and not invoice_type):
            invoice_counts = self.invoice_repository.get_invoice_counts_by_date(
                start_date=start_date,
                end_date=end_date,
                booker_legal_entity_ids=booker_legal_entity_ids,
                hotel_id=hotel_id,
                invoice_type=invoice_type,
                invoice_numbers=invoice_numbers,
                booking_reference_numbers=booking_reference_numbers,
            )

        credit_note_counts = []
        if fetch_credit_notes or not invoice_type:
            credit_note_counts = (
                self.credit_note_repository.get_credit_note_counts_by_date(
                    start_date=start_date,
                    end_date=end_date,
                    booker_legal_entity_ids=booker_legal_entity_ids,
                    hotel_id=hotel_id,
                    invoice_numbers=invoice_numbers,
                    booking_reference_numbers=booking_reference_numbers,
                )
            )

        return invoice_counts, credit_note_counts

    @staticmethod
    def _group_and_select_dates_for_download(
        invoice_counts, credit_note_counts, max_entries=750
    ):
        date_summary = defaultdict(lambda: {"invoices": 0, "credit_notes": 0})

        for date, count in invoice_counts:
            date_summary[date]["invoices"] += count

        for date, count in credit_note_counts:
            date_summary[date]["credit_notes"] += count

        sorted_dates = sorted(date_summary.items(), key=lambda x: x[0], reverse=False)

        dates_for_download = []
        max_entries_breached = False
        total_entries = 0
        for date, counts in sorted_dates:
            date_total = counts["invoices"] + counts["credit_notes"]
            if total_entries + date_total > max_entries:
                max_entries_breached = True
                break
            dates_for_download.append(date)
            total_entries += date_total

        return dates_for_download, max_entries_breached
