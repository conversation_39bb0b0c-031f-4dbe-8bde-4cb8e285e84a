from datetime import datetime

from core.common.utils.date import utc_to_ist
from finance_erp.application.invoice.dtos.generate_purchase_report_dto import (
    GeneratePurchaseReportDto,
)
from finance_erp.infrastructure.external_clients.reseller_service_client import (
    ResellerServiceClient,
)
from object_registry import register_instance


@register_instance(dependencies=[ResellerServiceClient])
class PurchaseReportGeneratorCommandHandler:
    def __init__(self, reseller_service_client: ResellerServiceClient):
        self.reseller_service_client = reseller_service_client

    def handle(self, input_criteria: GeneratePurchaseReportDto):
        if input_criteria.invoice_numbers or input_criteria.unique_ref_id:
            self.reseller_service_client.trigger_async_purchase_report_generation(
                {
                    "invoice_numbers": ",".join(input_criteria.invoice_numbers)
                    if input_criteria.invoice_numbers
                    else None,
                    "unique_ref_id": input_criteria.unique_ref_id,
                }
            )
        else:
            input_criteria.date = (
                utc_to_ist(datetime.utcnow()).date().strftime("%Y-%m-%d")
                if input_criteria.date is None
                else input_criteria.date
            )
            self.reseller_service_client.trigger_async_purchase_report_generation(
                {"date": input_criteria.date}
            )
        return "Triggered report generation"
