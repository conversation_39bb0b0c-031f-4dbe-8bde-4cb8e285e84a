from typing import Dict, List

from treebo_commons.request_tracing.context import get_current_request_id

from finance_erp.application.common.dtos.data_generation_base_dto import (
    DataGenerationBaseDto,
)
from finance_erp.application.hotel_settings.tenant_settings import TenantSettings
from finance_erp.common.constants import NavisionReports
from finance_erp.common.decorators import session_manager
from finance_erp.common.utils.utils import partition
from finance_erp.domain.reseller.entity.sales import SalesInvoiceEntity
from finance_erp.domain.reseller.entity.sales_summary import SalesSummaryEntity
from finance_erp.domain.reseller.factory.sales_summary_factory import (
    SalesSummaryEntityFactory,
)
from finance_erp.domain.reseller.repository.sales_invoice_repository import (
    SalesInvoiceRepository,
)
from finance_erp.domain.reseller.repository.sales_summary_repository import (
    SalesInvoiceSummaryRepository,
)
from object_registry import locate_instance, register_instance


@register_instance(
    dependencies=[SalesInvoiceSummaryRepository, SalesInvoiceRepository, TenantSettings]
)
class SalesInvoiceSummaryGenerationHandler:
    def __init__(
        self,
        sales_invoice_summary_repository: SalesInvoiceSummaryRepository,
        sales_invoice_repository: SalesInvoiceRepository,
        tenant_settings: TenantSettings,
    ):
        self.sales_invoice_summary_repository = sales_invoice_summary_repository
        self.sales_invoice_repository = sales_invoice_repository
        self.tenant_settings = tenant_settings

    @session_manager(commit=True)
    def handle(
        self, request: DataGenerationBaseDto = None, sales_records_to_aggregate=None
    ):
        aggregation_map: Dict[
            SalesInvoiceEntity.SalesInvoiceGroupKey, SalesSummaryEntity
        ] = self._aggregate_sales_record(sales_records_to_aggregate)
        records_to_insert, records_to_update = partition(
            aggregation_map.values(), lambda x: x.is_new
        )
        self.sales_invoice_summary_repository.insert_many(records_to_insert)
        self.sales_invoice_summary_repository.bulk_update_records(
            [record for record in records_to_update if record.is_dirty]
        )

    def _aggregate_sales_record(self, sales_records_to_aggregate):
        black_listed_hotels = (
            self.tenant_settings.get_business_central_blacklisted_hotels()
        )
        aggregation_map: Dict[
            SalesInvoiceEntity.SalesInvoiceGroupKey, SalesSummaryEntity
        ] = self._get_eligible_non_pushed_summary_records()
        sales_entity_to_update = []
        for sales_data in (
            sales_records_to_aggregate
            or self.sales_invoice_repository.get_eligible_records_to_aggregates(
                yield_count=1000
            )
        ):
            if sales_data.hotel_code in black_listed_hotels:
                sales_data.mark_as_excluded_for_data_push(
                    f"This Hotel ({sales_data.hotel_code}) is excluded"
                )
            else:
                aggregation_key = sales_data.aggregation_group_key()
                if aggregation_key in aggregation_map:
                    aggregation_map[aggregation_key].aggregate(sales_data)
                else:
                    aggregation_map[
                        aggregation_key
                    ] = SalesSummaryEntityFactory.create_sales_summary_from_sales_invoice_entity(
                        sales_data
                    )
                summary: SalesSummaryEntity = aggregation_map[aggregation_key]
                sales_data.mark_as_aggregated(summary.unique_ref_id)

            sales_entity_to_update.append(sales_data)
            if len(sales_entity_to_update) > 1000:
                self.sales_invoice_repository.bulk_update_records(
                    sales_entity_to_update
                )
                sales_entity_to_update = []
        if sales_entity_to_update:
            self.sales_invoice_repository.bulk_update_records(sales_entity_to_update)
        return aggregation_map

    def _get_eligible_non_pushed_summary_records(self):
        sales_summaries: List[
            SalesSummaryEntity
        ] = self.sales_invoice_summary_repository.get_eligible_records_to_push()
        aggregation_map: Dict[
            SalesInvoiceEntity.SalesInvoiceGroupKey, SalesSummaryEntity
        ] = dict()
        for summary in sales_summaries:
            group_key = SalesInvoiceEntity.SalesInvoiceGroupKey(
                entry_type=summary.entry_type,
                tax_type=summary.tax_type,
                tax_percentage=str(summary.tax_percentage),
                hsn_code=summary.hsn_code,
                posting_date=summary.posting_date,
            )
            aggregation_map[group_key] = summary
        return aggregation_map

    @staticmethod
    @session_manager(commit=True)
    def handle_async_execution_request(request, event_id=None):
        event_id = event_id if event_id else get_current_request_id()
        from finance_erp.async_job.job_scheduler_service import JobSchedulerService

        job = locate_instance(JobSchedulerService).create_data_aggregation_job(
            NavisionReports.CUSTOMER_INVOICE_SUMMARY_REPORT, request, event_id
        )
        return dict(job_id=job.job_id)

    def handle_request_from_job_executor(self, request_data, **kwargs):
        request_dto = DataGenerationBaseDto(**request_data)
        self.handle(request_dto)
