from datetime import datetime

from core.common.utils.date import utc_to_ist
from finance_erp.application.invoice.dtos.get_purchase_reprt_dto import (
    GetPurchaseReportDto,
)
from finance_erp.domain.reseller.repository.purchase_invoice_repository import (
    PurchaseInvoiceRepository,
)
from object_registry import register_instance


@register_instance(dependencies=[PurchaseInvoiceRepository])
class FetchPurchaseReportCommandHandler:
    def __init__(self, purchase_reports_repository: PurchaseInvoiceRepository):
        self.purchase_reports_repository = purchase_reports_repository

    def handle(self, input_criteria: GetPurchaseReportDto):
        if input_criteria.invoice_numbers or input_criteria.unique_ref_ids:
            input_criteria.invoice_numbers = (
                input_criteria.invoice_numbers.split(",")
                if input_criteria.invoice_numbers
                else None
            )
            input_criteria.unique_ref_ids = (
                input_criteria.unique_ref_ids.split(",")
                if input_criteria.unique_ref_ids
                else None
            )
            return self.purchase_reports_repository.load_all(
                invoice_numbers=input_criteria.invoice_numbers,
                uu_ids=input_criteria.unique_ref_ids,
            )
        else:
            if not input_criteria.from_date:
                input_criteria.from_date = utc_to_ist(datetime.utcnow()).date()
            if not input_criteria.to_date:
                input_criteria.to_date = utc_to_ist(datetime.utcnow()).date()
            return self.purchase_reports_repository.get_reports_by_date_range(
                from_date=input_criteria.from_date, to_date=input_criteria.to_date
            )
