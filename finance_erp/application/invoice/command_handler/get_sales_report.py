from datetime import datetime

from core.common.utils.date import utc_to_ist
from finance_erp.application.invoice.dtos.get_sales_reprt_dto import GetSalesReportDto
from finance_erp.domain.reseller.repository.sales_invoice_repository import (
    SalesInvoiceRepository,
)
from object_registry import register_instance


@register_instance(dependencies=[SalesInvoiceRepository])
class FetchSalesReportCommandHandler:
    def __init__(self, sales_invoice_repository: SalesInvoiceRepository):
        self.sales_invoice_repository = sales_invoice_repository

    def handle(self, input_criteria: GetSalesReportDto):
        if input_criteria.invoice_numbers or input_criteria.unique_ref_ids:
            input_criteria.invoice_numbers = (
                input_criteria.invoice_numbers.split(",")
                if input_criteria.invoice_numbers
                else None
            )
            input_criteria.unique_ref_ids = (
                input_criteria.unique_ref_ids.split(",")
                if input_criteria.unique_ref_ids
                else None
            )
            return self.sales_invoice_repository.get_reports(
                input_criteria.invoice_numbers, input_criteria.unique_ref_ids
            )
        else:
            if not input_criteria.from_date:
                input_criteria.from_date = utc_to_ist(datetime.utcnow()).date()
            if not input_criteria.to_date:
                input_criteria.to_date = utc_to_ist(datetime.utcnow()).date()
            return self.sales_invoice_repository.get_reports_by_date_range(
                from_date=input_criteria.from_date, to_date=input_criteria.to_date
            )
