import logging

from ths_common.constants.billing_constants import IssuedToType
from thsc.crs.convertors.billing_convertors import CreditNoteConvertor
from thsc.crs.entities.billing import CreditNote as THSCCreditNote

from finance_erp.application.common.resilient_ingestion_handler import (
    ResilientDataIngestionCommandHandler,
)
from finance_erp.application.corporate.communication.cn_dispatcher import (
    CorporateCreditNoteDispatcher,
)
from finance_erp.application.invoice.command_handler.ingest_invoice import (
    InvoiceIngestionCommandHandler,
)
from finance_erp.common.constants import IngestionJobs
from finance_erp.domain.crs.entity.credit_note import CreditNote
from finance_erp.domain.crs.entity.invoice import Invoice
from finance_erp.domain.crs.factory.credit_note_factory import CreditNoteFactory
from finance_erp.domain.crs.repository.credit_note_repository import (
    CreditNoteRepository,
)
from finance_erp.domain.crs.repository.invoice_repository import InvoiceRepository
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        InvoiceRepository,
        CreditNoteRepository,
        InvoiceIngestionCommandHandler,
        CorporateCreditNoteDispatcher,
    ]
)
class CreditNoteIngestionCommandHandler(ResilientDataIngestionCommandHandler):
    def __init__(
        self,
        invoice_repository: InvoiceRepository,
        credit_note_repository: CreditNoteRepository,
        invoice_ingestion_command_handler: InvoiceIngestionCommandHandler,
        corporate_credit_note_dispatcher: CorporateCreditNoteDispatcher,
    ):
        self.invoice_repository = invoice_repository
        self.credit_note_repository = credit_note_repository
        self.invoice_ingestion_command_handler = invoice_ingestion_command_handler
        self.corporate_credit_note_dispatcher = corporate_credit_note_dispatcher

        super().__init__(IngestionJobs.CREDIT_NOTE_INGESTION)

    def _execute(self, request_data, event_id, from_async_job=False):
        if not self._is_eligible_for_ingestion(request_data):
            return
        thsc_credit_note: THSCCreditNote = self._get_thsc_credit_note(
            request_data, re_fetch_from_crs=from_async_job
        )
        credit_note: CreditNote = self.credit_note_repository.get_by_id(
            thsc_credit_note.credit_note_id
        )
        if credit_note:
            credit_note = self._update_credit_note(credit_note, thsc_credit_note)
            if credit_note.credit_note_url:
                self.corporate_credit_note_dispatcher.schedule_dispatch_if_eligible(
                    credit_note
                )
            return credit_note

        invoice_id = next(
            item.invoice_id for item in thsc_credit_note.credit_note_line_items
        )

        if not invoice_id:
            raise Exception("Invalid Credit Note. No linked invoice found")

        invoice: Invoice = self.invoice_repository.get_by_invoice_id(invoice_id)
        if not invoice:
            invoice = self.invoice_ingestion_command_handler.ingest(invoice_id)

        credit_note = CreditNoteFactory.build(thsc_credit_note, invoice)

        credit_note = self.credit_note_repository.save(credit_note)

        if credit_note.credit_note_url:
            self.corporate_invoice_dispatcher.schedule_dispatch_if_eligible(credit_note)
        return credit_note

    @staticmethod
    def _is_eligible_for_ingestion(request_data):
        return request_data.get("issued_to_type") == IssuedToType.CUSTOMER.value

    def _update_credit_note(
        self, credit_note: CreditNote, thsc_credit_note: THSCCreditNote
    ):
        logger.info(f"CN already exists with cn_id: {credit_note.credit_note_id}")
        credit_note.update_from_thsc_entity(thsc_credit_note)
        return self.credit_note_repository.update(credit_note)

    @staticmethod
    def _get_thsc_credit_note(request_data, re_fetch_from_crs=False) -> THSCCreditNote:
        if re_fetch_from_crs:
            return THSCCreditNote.get(request_data.get("credit_note_id"))
        return CreditNoteConvertor().from_dict(request_data)
