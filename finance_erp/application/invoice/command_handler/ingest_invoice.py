import logging

from ths_common.constants.billing_constants import (
    ChargeBillToTypes,
    InvoiceStatus,
    IssuedToType,
)
from thsc.crs.convertors.billing_convertors import InvoiceConvertor
from thsc.crs.entities.billing import Invoice as THSCInvoice

from finance_erp.application.common.resilient_ingestion_handler import (
    ResilientDataIngestionCommandHandler,
)
from finance_erp.application.corporate.communication.invoice_dispatcher import (
    CorporateInvoiceDispatcher,
)
from finance_erp.application.crs.command_handlers.ingest_booking import (
    CrsBookingIngestionCommandHandler,
)
from finance_erp.async_job.job_scheduler_service import JobSchedulerService
from finance_erp.common.constants import IngestionJobs
from finance_erp.domain.company_profile.entity.corporate import CorporateEntity
from finance_erp.domain.company_profile.repository.corporate_repository import (
    CorporateReportRepository,
)
from finance_erp.domain.crs.entity.booking import Booking
from finance_erp.domain.crs.entity.invoice import Invoice
from finance_erp.domain.crs.factory.invoice_factory import InvoiceFactory
from finance_erp.domain.crs.repository.booking_repository import BookingRepository
from finance_erp.domain.crs.repository.invoice_repository import InvoiceRepository
from finance_erp.domain.shared_kernel.audit.communication_audit.audit_service import (
    CommunicationAuditService,
)
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        InvoiceRepository,
        CrsBookingIngestionCommandHandler,
        BookingRepository,
        CommunicationAuditService,
        CorporateReportRepository,
        CorporateInvoiceDispatcher,
    ]
)
class InvoiceIngestionCommandHandler(ResilientDataIngestionCommandHandler):
    def __init__(
        self,
        invoice_repository: InvoiceRepository,
        booking_ingestion_handler: CrsBookingIngestionCommandHandler,
        booking_repository: BookingRepository,
        communication_audit_service: CommunicationAuditService,
        corporate_repository: CorporateReportRepository,
        corporate_invoice_dispatcher: CorporateInvoiceDispatcher,
    ):
        self.invoice_repository = invoice_repository
        self.booking_ingestion_handler = booking_ingestion_handler
        self.booking_repository = booking_repository
        self.communication_audit_service = communication_audit_service
        self.corporate_repository = corporate_repository
        self.corporate_invoice_dispatcher = corporate_invoice_dispatcher
        super().__init__(IngestionJobs.INVOICE_INGESTION)

    def _execute(self, request_data, event_id, from_async_job=False):
        if not self._is_eligible_for_ingestion(request_data):
            return
        thsc_invoice: THSCInvoice = self._get_thsc_invoice(
            request_data, re_fetch_invoice=from_async_job
        )
        return self._ingest(thsc_invoice)

    def _ingest(self, thsc_invoice):
        booking_id = thsc_invoice.parent_info.get("booking_id")
        booking: Booking = self.booking_repository.get_by_id(booking_id)
        if not booking:
            self.booking_ingestion_handler.ingest(booking_id)

        credit_period = None
        if (
            thsc_invoice.bill_to_type == ChargeBillToTypes.COMPANY
            and thsc_invoice.bill_to.external_ref_id
        ):
            corporate: CorporateEntity = self.corporate_repository.get(
                thsc_invoice.bill_to.external_ref_id
            )
            credit_period = corporate.credit_period if corporate else None

        invoice_received = InvoiceFactory.build(thsc_invoice, booking, credit_period)

        existing_invoice: Invoice = self.invoice_repository.get_by_invoice_id(
            thsc_invoice.invoice_id
        )
        if existing_invoice:
            invoice = self._update_invoice(existing_invoice, invoice_received)
        else:
            if invoice_received.status == InvoiceStatus.CANCELLED:
                return
            invoice = self.invoice_repository.save(invoice_received)

        if invoice.invoice_url:
            is_booking_eligible_for_invoice_dispatch = (
                booking.is_booking_completely_checked_out()
                or booking.is_cancelled_or_no_show()
            )
            if is_booking_eligible_for_invoice_dispatch:
                self.corporate_invoice_dispatcher.schedule_dispatch_if_eligible(invoice)

        return invoice

    def ingest(self, invoice_id):
        thsc_invoice: THSCInvoice = THSCInvoice.get(invoice_id)
        return self._ingest(thsc_invoice)

    def _update_invoice(self, invoice: Invoice, invoice_received):
        logger.info(f"Invoice already exists with invoice_id: {invoice.invoice_id} ")
        invoice.update(invoice_received)
        return self.invoice_repository.update(invoice)

    @staticmethod
    def _is_eligible_for_ingestion(request_data):
        return request_data.get("issued_to_type") == IssuedToType.CUSTOMER.value

    @staticmethod
    def _get_thsc_invoice(request_data, re_fetch_invoice) -> THSCInvoice:
        if re_fetch_invoice:
            return THSCInvoice.get(request_data.get("invoice_id"))
        return InvoiceConvertor().from_dict(request_data)
