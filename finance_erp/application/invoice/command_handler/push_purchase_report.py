from finance_erp.application.common.base_data_push_handler import (
    BaseDataPushCommandHandler,
)
from finance_erp.application.hotel_settings.tenant_settings import TenantSettings
from finance_erp.application.invoice.dtos.bulk_purchase_push_request_dto import (
    BulkPurchasePushRequestDto,
)
from finance_erp.common.constants import DataPushNavKeys
from finance_erp.common.decorators import session_manager
from finance_erp.common.schema.purchase import PurchaseInvoicePushRequestSchema
from finance_erp.domain.hotel.repository.hotel_repository import HotelReportRepository
from finance_erp.domain.reseller.entity.purchase import PurchaseInvoiceEntity
from finance_erp.domain.reseller.repository.purchase_invoice_repository import (
    PurchaseInvoiceRepository,
)
from finance_erp.domain.shared_kernel.audit.process_level.nav_push_audit_service import (
    NavPushAuditService,
)
from finance_erp.infrastructure.alerting.slack_alert_service_client import (
    SlackAlertServiceClient,
)
from finance_erp.infrastructure.external_clients.business_central_client import (
    BusinessCentralClient,
)
from object_registry import register_instance


@register_instance(
    dependencies=[
        BusinessCentralClient,
        SlackAlertServiceClient,
        PurchaseInvoiceRepository,
        NavPushAuditService,
        TenantSettings,
        HotelReportRepository,
    ]
)
class BulkPushPurchaseReportCommandHandler(BaseDataPushCommandHandler):
    def __init__(
        self,
        external_data_push_client: BusinessCentralClient,
        slack_alert_client,
        purchase_reports_repository: PurchaseInvoiceRepository,
        nav_push_audit_service: NavPushAuditService,
        tenant_settings: TenantSettings,
        hotel_reports_repository: HotelReportRepository,
    ):
        self.purchase_reports_repository: PurchaseInvoiceRepository = (
            purchase_reports_repository
        )
        self.tenant_settings = tenant_settings
        self.hotel_reports_repository = hotel_reports_repository
        super().__init__(
            external_data_push_client,
            slack_alert_client,
            nav_push_audit_service,
            hotel_reports_repository,
            tenant_settings,
        )

    def _get_schema_and_json_key(self):
        return PurchaseInvoicePushRequestSchema, DataPushNavKeys.PURCHASE

    def _get_records_to_push(self, request_dto: BulkPurchasePushRequestDto):
        if request_dto.unique_ref_ids:
            return self.purchase_reports_repository.get_by_ids(
                request_dto.unique_ref_ids, for_update=True
            )
        return self.purchase_reports_repository.get_eligible_records_to_push()

    @session_manager(commit=True)
    def _capture_failure(self, record: PurchaseInvoiceEntity, reason):
        record.capture_data_push_failure(reason)
        self.purchase_reports_repository.update_record(record)

    @session_manager(commit=True)
    def _capture_success(self, record: PurchaseInvoiceEntity):
        record.capture_data_push_success()
        self.purchase_reports_repository.update_record(record)

    def _get_request_parser(self):
        return BulkPurchasePushRequestDto

    def _update_records(self, records):
        self.purchase_reports_repository.bulk_update_records(records)
