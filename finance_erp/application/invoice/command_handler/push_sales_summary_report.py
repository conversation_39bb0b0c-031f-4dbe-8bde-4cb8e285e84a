import datetime
from typing import List

from dateutil.relativedelta import relativedelta

from finance_erp.application.common.base_data_push_handler import (
    BaseDataPushCommandHandler,
)
from finance_erp.application.hotel_settings.tenant_settings import TenantSettings
from finance_erp.application.invoice.dtos.bulk_sales_push_request_dto import (
    BulkSalesPushRequestDto,
)
from finance_erp.common.constants import DataPushNavKeys
from finance_erp.common.decorators import session_manager
from finance_erp.common.schema.sales import SaleInvoiceSummaryPushRequestSchema
from finance_erp.domain.hotel.repository.hotel_repository import HotelReportRepository
from finance_erp.domain.reseller.entity.sales import SalesInvoiceEntity
from finance_erp.domain.reseller.entity.sales_summary import SalesSummaryEntity
from finance_erp.domain.reseller.repository.sales_invoice_repository import (
    SalesInvoiceRepository,
)
from finance_erp.domain.reseller.repository.sales_summary_repository import (
    SalesInvoiceSummaryRepository,
)
from finance_erp.domain.shared_kernel.audit.process_level.nav_push_audit_service import (
    NavPushAuditService,
)
from finance_erp.infrastructure.alerting.slack_alert_service_client import (
    SlackAlertServiceClient,
)
from finance_erp.infrastructure.external_clients.business_central_client import (
    BusinessCentralClient,
)
from object_registry import register_instance


@register_instance(
    dependencies=[
        BusinessCentralClient,
        SlackAlertServiceClient,
        SalesInvoiceSummaryRepository,
        SalesInvoiceRepository,
        NavPushAuditService,
        TenantSettings,
        HotelReportRepository,
    ]
)
class BulkPushSalesSummaryReportCommandHandler(BaseDataPushCommandHandler):
    def __init__(
        self,
        external_data_push_client: BusinessCentralClient,
        slack_alert_client: SlackAlertServiceClient,
        sales_invoice_summary_repository: SalesInvoiceSummaryRepository,
        sales_invoice_repository: SalesInvoiceRepository,
        nav_push_audit_service: NavPushAuditService,
        tenant_settings: TenantSettings,
        hotel_reports_repository: HotelReportRepository,
    ):
        super().__init__(
            external_data_push_client,
            slack_alert_client,
            nav_push_audit_service,
            hotel_reports_repository,
            tenant_settings,
        )
        self.sales_invoice_summary_repository = sales_invoice_summary_repository
        self.sales_invoice_repository = sales_invoice_repository
        self.tenant_settings = tenant_settings
        self.hotel_reports_repository = hotel_reports_repository

    def _get_schema_and_json_key(self):
        return SaleInvoiceSummaryPushRequestSchema, DataPushNavKeys.SALES

    def _get_records_to_push(self, request_dto: BulkSalesPushRequestDto):
        if request_dto.unique_ref_ids:
            return self.sales_invoice_summary_repository.get_by_ids(
                request_dto.unique_ref_ids
            )
        posting_date = None
        if not request_dto.push_all:
            posting_date = datetime.date.today() - relativedelta(days=1)
        return self.sales_invoice_summary_repository.get_eligible_records_to_push(
            posting_date
        )

    @session_manager(commit=True)
    def _capture_failure(self, record: SalesSummaryEntity, reason):
        record.capture_data_push_failure(reason)
        self._capture_data_push_failure_on_granular_sales_records(record, reason)
        self.sales_invoice_summary_repository.update_record(record)

    def _capture_data_push_failure_on_granular_sales_records(
        self, record: SalesSummaryEntity, reason
    ):
        granular_sales_records: List[
            SalesInvoiceEntity
        ] = self.sales_invoice_repository.get_by_aggregation_ids([record.unique_ref_id])
        for sales_record in granular_sales_records:
            sales_record.capture_data_push_failure(reason)
        self.sales_invoice_repository.bulk_update_records(granular_sales_records)

    @session_manager(commit=True)
    def _capture_success(self, record: SalesSummaryEntity):
        record.capture_data_push_success()
        self._capture_data_push_success_on_granular_sales_records(record)
        self.sales_invoice_summary_repository.update_record(record)

    def _capture_data_push_success_on_granular_sales_records(
        self, record: SalesSummaryEntity
    ):
        granular_sales_records: List[
            SalesInvoiceEntity
        ] = self.sales_invoice_repository.get_by_aggregation_ids([record.unique_ref_id])
        for sales_record in granular_sales_records:
            sales_record.capture_data_push_success()
        self.sales_invoice_repository.bulk_update_records(granular_sales_records)

    def _get_request_parser(self):
        return BulkSalesPushRequestDto

    def _apply_data_exclusion_rules(self, records):
        return records
