from typing import Dict, List

from finance_erp.application.invoice.command_handler.bulk_update_purchase_record import (
    BulkUpdatePurchaseReportCommandHandler,
)
from finance_erp.common.constants import PurchaseInvoiceReportCategory
from finance_erp.common.decorators import session_manager
from finance_erp.common.exception import (
    DataAlreadyPushedException,
    PurchaseDataUpdateException,
)
from finance_erp.common.schema.purchase import PurchaseInvoiceIngestSchema
from finance_erp.common.utils.utils import partition
from finance_erp.domain.reseller.entity.purchase import PurchaseInvoiceEntity
from finance_erp.domain.reseller.models import SalesInvoiceStatus
from finance_erp.domain.reseller.repository.purchase_invoice_repository import (
    PurchaseInvoiceRepository,
)
from finance_erp.infrastructure.external_clients.crs_service_client import (
    CrsServiceClient,
)
from finance_erp.infrastructure.external_clients.reseller_service_client import (
    ResellerServiceClient,
)
from object_registry import locate_instance, register_instance


@register_instance(
    dependencies=[PurchaseInvoiceRepository, ResellerServiceClient, CrsServiceClient]
)
class ResyncPurchaseDataHandler:
    def __init__(
        self,
        purchase_invoice_repository: PurchaseInvoiceRepository,
        reseller_service_client: ResellerServiceClient,
        crs_service_client: CrsServiceClient,
    ):
        self.purchase_invoice_repository = purchase_invoice_repository
        self.reseller_service_client = reseller_service_client
        self.crs_service_client = crs_service_client

    @session_manager(commit=True)
    def handle(self, purchase_data_uu_ids):
        purchase_data = self.purchase_invoice_repository.get_by_ids(
            purchase_data_uu_ids
        )

        self._has_already_pushed_records(purchase_data)
        latest_pulled_purchase_data = self._fetch_latest_purchase_data(purchase_data)
        if latest_pulled_purchase_data:
            try:
                self._update_purchase_data(latest_pulled_purchase_data)
            except Exception as e:
                raise PurchaseDataUpdateException(
                    f"Purchase data update failed: {str(e)}"
                )

    @staticmethod
    def _has_already_pushed_records(records):
        if any(record.status == SalesInvoiceStatus.PUSHED for record in records):
            raise DataAlreadyPushedException()

    def _fetch_latest_purchase_data(self, purchase_data: List[PurchaseInvoiceEntity]):
        reseller_purchases, marketplace_purchases = partition(
            purchase_data,
            lambda data: data.report_category
            == PurchaseInvoiceReportCategory.RESELLER_PURCHASE_INVOICE_REPORT.value,
        )
        latest_purchase_data = []
        if reseller_purchases:
            latest_purchase_data += self._fetch_reseller_purchase_data(
                reseller_purchases
            )
        if marketplace_purchases:
            latest_purchase_data += self._fetch_market_place_purchase_data(
                marketplace_purchases
            )
        return [
            PurchaseInvoiceIngestSchema.model_validate(item)
            for item in latest_purchase_data
        ]

    def _fetch_reseller_purchase_data(
        self,
        purchase_data: List[PurchaseInvoiceEntity],
    ) -> List[Dict]:
        unique_ref_ids = [data.unique_ref_id for data in purchase_data]
        return self.reseller_service_client.fetch_hotel_invoice_finance_report(
            unique_ref_ids
        )

    def _fetch_market_place_purchase_data(
        self, purchase_data: List[PurchaseInvoiceEntity]
    ):
        resource_data = [
            dict(
                resource_id=data.reference_number, resource_unique_id=data.unique_ref_id
            )
            for data in purchase_data
        ]
        response_data = self.crs_service_client.fetch_finance_reports(
            resource_data,
            PurchaseInvoiceReportCategory.MARKETPLACE_PURCHASE_INVOICE_REPORT.value,
        )
        return [
            PurchaseInvoiceIngestSchema.model_validate(item) for item in response_data
        ]

    @staticmethod
    def _update_purchase_data(latest_pulled_purchase_data):
        purchase_data = [
            purchase.model_dump() for purchase in latest_pulled_purchase_data
        ]
        handler: BulkUpdatePurchaseReportCommandHandler = locate_instance(
            BulkUpdatePurchaseReportCommandHandler
        )
        handler.handle(purchase_data)
