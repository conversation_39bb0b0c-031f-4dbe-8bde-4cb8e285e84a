from finance_erp.application.invoice.command_handler.bulk_update_sales_record import (
    BulkUpdateSalesReportCommandHandler,
)
from finance_erp.application.invoice.command_handler.generate_sales_summary import (
    SalesInvoiceSummaryGenerationHandler,
)
from finance_erp.common.constants import NavisionReports
from finance_erp.common.decorators import session_manager
from finance_erp.common.exception import (
    DataAlreadyPushedException,
    SalesDataAggregationException,
    SalesDataUpdateException,
)
from finance_erp.common.schema.sales import SalesInvoiceIngestSchema
from finance_erp.domain.reseller.models import SalesInvoiceStatus
from finance_erp.domain.reseller.repository.sales_invoice_repository import (
    SalesInvoiceRepository,
)
from finance_erp.domain.reseller.repository.sales_summary_repository import (
    SalesInvoiceSummaryRepository,
)
from finance_erp.infrastructure.external_clients.crs_service_client import (
    CrsServiceClient,
)
from object_registry import locate_instance, register_instance


@register_instance(
    dependencies=[
        SalesInvoiceSummaryRepository,
        SalesInvoiceRepository,
        CrsServiceClient,
    ]
)
class ResyncSalesDataHandler:
    def __init__(
        self,
        sales_invoice_summary_repository: SalesInvoiceSummaryRepository,
        sales_invoice_repository: SalesInvoiceRepository,
        crs_service_client: CrsServiceClient,
    ):
        self.sales_invoice_summary_repository = sales_invoice_summary_repository
        self.sales_invoice_repository = sales_invoice_repository
        self.crs_service_client = crs_service_client

    @session_manager(commit=True)
    def handle(self, sales_invoice_summary_uu_ids):
        selected_records = self.sales_invoice_summary_repository.get_by_ids(
            sales_invoice_summary_uu_ids
        )
        self._validate_non_pushed_records(selected_records)
        sales_data = self.sales_invoice_repository.get_by_aggregation_ids(
            [record.unique_ref_id for record in selected_records]
        )
        latest_pulled_sales_data = self._fetch_latest_sales_data_from_crs(sales_data)
        if latest_pulled_sales_data:
            try:
                updated_sales_data = self._update_sales_data(latest_pulled_sales_data)
            except Exception as e:
                raise SalesDataUpdateException(f"Sales data update failed: {str(e)}")
            try:
                self._aggregate_sales_data(updated_sales_data)
            except Exception as e:
                raise SalesDataAggregationException(
                    f"Sales data aggregation failed: {str(e)}"
                )

    @staticmethod
    def _validate_non_pushed_records(records):
        if any(record.status == SalesInvoiceStatus.PUSHED for record in records):
            raise DataAlreadyPushedException()

    def _fetch_latest_sales_data_from_crs(self, sales_data):
        resource_data = [
            dict(
                resource_id=data.reference_number, resource_unique_id=data.unique_ref_id
            )
            for data in sales_data
        ]
        response_data = self.crs_service_client.fetch_finance_reports(
            resource_data,
            NavisionReports.CUSTOMER_INVOICE_REPORT,
        )
        crs_sales_data = [
            SalesInvoiceIngestSchema.model_validate(item) for item in response_data
        ]
        return crs_sales_data

    @staticmethod
    def _update_sales_data(latest_pulled_sales_data):
        sales_data = [sales.model_dump() for sales in latest_pulled_sales_data]
        handler: BulkUpdateSalesReportCommandHandler = locate_instance(
            BulkUpdateSalesReportCommandHandler
        )
        return handler.handle(sales_data)

    @staticmethod
    def _aggregate_sales_data(latest_sales_data):
        if not latest_sales_data:
            return
        sales_summary_generation_handler: SalesInvoiceSummaryGenerationHandler = (
            locate_instance(SalesInvoiceSummaryGenerationHandler)
        )
        sales_summary_generation_handler.handle(
            sales_records_to_aggregate=latest_sales_data
        )
