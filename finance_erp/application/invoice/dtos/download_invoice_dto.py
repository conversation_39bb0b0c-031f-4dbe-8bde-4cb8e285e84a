from typing import List, Optional

from core.common.utils import date


class DownloadInvoiceRequestDto:
    def __init__(
        self,
        start_date: date,
        end_date: date,
        user_email_id: str,
        fetch_credit_notes: bool = False,
        hotel_id: Optional[str] = None,
        invoice_type: Optional[List[str]] = None,
        booker_legal_entity_ids: Optional[List[str]] = None,
        invoice_numbers: Optional[List[str]] = None,
        booking_reference_numbers: Optional[List[str]] = None,
    ):
        self.start_date = start_date
        self.end_date = end_date
        self.booker_legal_entity_ids = booker_legal_entity_ids or []
        self.user_email_id = user_email_id
        self.fetch_credit_notes = fetch_credit_notes
        self.hotel_id = hotel_id
        self.invoice_numbers = invoice_numbers or []
        self.booking_reference_numbers = booking_reference_numbers or []
        self.invoice_type = invoice_type or []
