from typing import List

from ths_common.exceptions import ResourceNotFound, ValidationException

from finance_erp.common.error_codes import ApplicationErrors
from finance_erp.domain.ota.entity.ota import UPDATABLE_FIELDS, OTACommissionEntity
from finance_erp.domain.ota.repository.ota_commission_data_repository import (
    OTACommissionReportRepository,
)
from object_registry import register_instance


@register_instance(dependencies=[OTACommissionReportRepository])
class BulkUpdateOTACommissionReportCommandHandler:
    def __init__(
        self, ota_commission_reports_repository: OTACommissionReportRepository
    ):
        self.ota_commission_reports_repository = ota_commission_reports_repository

    def handle(self, request_data_list: list):
        uuids = [rc["reference_number"] for rc in request_data_list]
        ota_commission_report_aggregates: List[
            OTACommissionEntity
        ] = self.ota_commission_reports_repository.get_by_ids(uuids)
        aggregate_map = {
            ag.reference_number: ag for ag in ota_commission_report_aggregates
        }
        ota_commission_report_aggregates = []
        for request_data in request_data_list:
            uu_id = request_data["reference_number"]
            if uu_id not in aggregate_map:
                raise ResourceNotFound(
                    "OTARecord", extra_payload=dict(reference_number=uu_id)
                )
            ota_commission_report_aggregate: OTACommissionEntity = aggregate_map[uu_id]
            if ota_commission_report_aggregate.pushed:
                raise ValidationException(
                    ApplicationErrors.CANNOT_UPDATE_PUSHED_RECORDS,
                    extra_payload=dict(pg_transaction_id=uu_id),
                )
            for attr in UPDATABLE_FIELDS:
                if attr in request_data:
                    setattr(ota_commission_report_aggregate, attr, request_data[attr])
            ota_commission_report_aggregates.append(ota_commission_report_aggregate)
        self.ota_commission_reports_repository.bulk_update_records(
            ota_commission_report_aggregates
        )
        return ota_commission_report_aggregates
