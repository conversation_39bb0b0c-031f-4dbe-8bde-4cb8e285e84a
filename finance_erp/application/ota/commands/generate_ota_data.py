from datetime import datetime, timedelta

from core.common.utils.date import utc_to_ist
from finance_erp.application.ota.dtos.generate_ota_report_dto import (
    GenerateOTACommissionReportDto,
)
from finance_erp.common.constants import NavisionReports
from finance_erp.infrastructure.external_clients.crs_service_client import (
    CrsServiceClient,
)
from object_registry import register_instance


@register_instance(
    dependencies=[
        CrsServiceClient,
    ]
)
class OTACommissionReportGeneratorCommandHandler:
    def __init__(self, crs_service_client: CrsServiceClient):
        self.crs_service_client = crs_service_client

    def handle(self, input_criteria: GenerateOTACommissionReportDto):
        if not input_criteria.date:
            input_criteria.date = (
                utc_to_ist(datetime.utcnow() - timedelta(days=1))
                .date()
                .strftime("%Y-%m-%d")
            )
        self.crs_service_client.trigger_async_report_generation(
            date=input_criteria.date, report_name=NavisionReports.OTA_COMMISSION_REPORT
        )
        return f"Report generation triggered successfully"
