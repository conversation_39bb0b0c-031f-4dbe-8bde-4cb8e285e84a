from datetime import datetime

from core.common.utils.date import utc_to_ist
from finance_erp.application.ota.dtos.get_ota_report_dto import (
    GetOTACommissionReportDto,
)
from finance_erp.domain.ota.repository.ota_commission_data_repository import (
    OTACommissionReportRepository,
)
from object_registry import register_instance


@register_instance(dependencies=[OTACommissionReportRepository])
class FetchOTACommissionsCommandHandler:
    def __init__(self, ota_commission_reports_repository=OTACommissionReportRepository):
        self.ota_commission_reports_repository = ota_commission_reports_repository

    def handle(self, input_criteria: GetOTACommissionReportDto):
        if input_criteria.reference_number:
            input_criteria.reference_number = (
                input_criteria.reference_number.split(",")
                if input_criteria.reference_number
                else None
            )
            return self.ota_commission_reports_repository.get_by_ids(
                input_criteria.reference_number
            )
        else:
            if not input_criteria.from_date:
                input_criteria.from_date = utc_to_ist(datetime.utcnow()).date()
            if not input_criteria.to_date:
                input_criteria.to_date = utc_to_ist(datetime.utcnow()).date()
            return self.ota_commission_reports_repository.get_reports_by_date_range(
                from_date=input_criteria.from_date, to_date=input_criteria.to_date
            )
