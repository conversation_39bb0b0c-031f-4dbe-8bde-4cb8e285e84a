from collections import defaultdict
from typing import List

from ths_common.exceptions import ValidationException

from finance_erp.common.error_codes import ApplicationErrors
from finance_erp.domain.payment.entity.payment import PGPaymentEntity
from finance_erp.domain.payment.entity.payment_summary_entity import (
    PGPaymentSummaryEntity,
)
from finance_erp.domain.payment.models import PGPaymentStatus
from finance_erp.domain.payment.repository.payment_repository import PaymentRepository
from finance_erp.domain.payment.repository.payment_summary_repository import (
    PaymentSummaryRepository,
)
from object_registry import register_instance


@register_instance(dependencies=[PaymentRepository, PaymentSummaryRepository])
class BulkUpdatePaymentReportCommandHandler:
    def __init__(
        self,
        payment_reports_repository: PaymentRepository,
        payment_summary_repository: PaymentSummaryRepository,
    ):
        self.payment_reports_repository = payment_reports_repository
        self.payment_summary_repository = payment_summary_repository

    def handle(self, request_data_list: list):
        uuids = [rc["uu_id"] for rc in request_data_list]
        uu_id_to_request_data_mapping = {
            req_data["uu_id"]: req_data for req_data in request_data_list
        }
        payment_records: List[
            PGPaymentEntity
        ] = self.payment_reports_repository.get_by_ids(uuids, for_update=True)

        if any(record.status == PGPaymentStatus.PUSHED for record in payment_records):
            raise ValidationException(
                ApplicationErrors.CANNOT_UPDATE_PUSHED_RECORDS,
                extra_payload=dict(
                    already_pushed_uu_ids={
                        ag.uu_id
                        for ag in payment_records
                        if ag.status == PGPaymentStatus.PUSHED
                    }
                ),
            )

        aggregated_payment_records = [
            rec
            for rec in payment_records
            if rec.status
            in [PGPaymentStatus.AGGREGATED, PGPaymentStatus.FAILED_TO_PUSH]
        ]
        non_aggregated_payment_records = [
            rec
            for rec in payment_records
            if rec.status
            in [PGPaymentStatus.INGESTED, PGPaymentStatus.EXCLUDED_FOR_DATA_PUSH]
        ]

        updated_payment_records_got_aggregated = self._update_aggregated_records(
            aggregated_payment_records, uu_id_to_request_data_mapping
        )
        self._update_non_aggregated_records(
            non_aggregated_payment_records, uu_id_to_request_data_mapping
        )
        return updated_payment_records_got_aggregated + non_aggregated_payment_records

    # Updating all the payment entries attached with Non-Pushed Aggregated Summary
    def _update_aggregated_records(
        self, aggregated_payment_records, uu_id_to_request_data_mapping
    ):
        if not aggregated_payment_records:
            return []
        aggregated_ids = {ag.aggregation_id for ag in aggregated_payment_records}
        payment_data_attached_to_summaries = (
            self.payment_reports_repository.get_by_aggregation_ids(aggregated_ids)
        )
        payment_summary_records: List[
            PGPaymentSummaryEntity
        ] = self.payment_summary_repository.get_by_ids(aggregated_ids, for_update=True)
        aggregated_entries_against_aggregation_ids = defaultdict(list)
        for record in payment_data_attached_to_summaries:
            aggregated_entries_against_aggregation_ids[record.aggregation_id].append(
                record
            )

        for payment_summary_entry in payment_summary_records:
            for payment_record in aggregated_entries_against_aggregation_ids[
                payment_summary_entry.uu_id
            ]:
                if uu_id_to_request_data_mapping.get(payment_record.uu_id):
                    self._update_entity_attributes(
                        payment_record,
                        uu_id_to_request_data_mapping[payment_record.uu_id],
                    )
                payment_record.status = PGPaymentStatus.INGESTED
                payment_record.aggregation_id = None
                payment_record.aggregated_at = None
            payment_summary_entry.deleted = True
        self.payment_reports_repository.bulk_update_records(
            payment_data_attached_to_summaries
        )
        self.payment_summary_repository.bulk_update_records(payment_summary_records)
        return payment_data_attached_to_summaries

    def _update_non_aggregated_records(
        self, non_aggregated_payment_records, uu_id_to_request_data_mapping
    ):
        for payment_record in non_aggregated_payment_records:
            self._update_entity_attributes(
                payment_record, uu_id_to_request_data_mapping[payment_record.uu_id]
            )
        self.payment_reports_repository.bulk_update_records(
            non_aggregated_payment_records
        )

    def _update_entity_attributes(self, entity, data):
        """
        Recursively updates the entity attributes based on provided dictionary.
        """
        for key, value in data.items():
            if hasattr(entity, key):
                current_attr = getattr(entity, key)
                # Handle nested dictionary update if the attribute is an object
                if isinstance(value, dict) and isinstance(current_attr, object):
                    self._update_entity_attributes(current_attr, value)
                else:
                    setattr(entity, key, value)
