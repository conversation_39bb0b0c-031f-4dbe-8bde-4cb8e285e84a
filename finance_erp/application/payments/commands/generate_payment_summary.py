from typing import Dict

from treebo_commons.request_tracing.context import get_current_request_id

from finance_erp.application.common.dtos.data_generation_base_dto import (
    DataGenerationBaseDto,
)
from finance_erp.application.hotel_settings.tenant_settings import TenantSettings
from finance_erp.common.constants import AR_MODULE, NavisionReports
from finance_erp.common.decorators import session_manager
from finance_erp.domain.payment.entity.payment import PGPaymentEntity
from finance_erp.domain.payment.entity.payment_summary_entity import (
    PGPaymentSummaryEntity,
)
from finance_erp.domain.payment.factory.payment_summary_entity_factory import (
    PaymentSummaryFactory,
)
from finance_erp.domain.payment.repository.payment_repository import PaymentRepository
from finance_erp.domain.payment.repository.payment_summary_repository import (
    PaymentSummaryRepository,
)
from object_registry import locate_instance, register_instance


@register_instance(
    dependencies=[PaymentSummaryRepository, PaymentRepository, TenantSettings]
)
class PaymentSummaryGenerationHandler:
    def __init__(
        self,
        payment_summary_repository: PaymentSummaryRepository,
        payment_repository: PaymentRepository,
        tenant_settings: TenantSettings,
    ):
        self.payment_summary_repository = payment_summary_repository
        self.payment_repository = payment_repository
        self.tenant_settings = tenant_settings

    @session_manager(commit=True)
    def handle(self, request: DataGenerationBaseDto = None, payments_to_aggregate=None):
        eligible_records = (
            payments_to_aggregate
            or self.payment_repository.get_eligible_records_to_aggregates()
        )
        self._create_payment_summary_for_payments_other_than_ar(eligible_records)
        ar_payments_aggregation_map: Dict[
            PGPaymentEntity.PaymentGroupKey, PGPaymentSummaryEntity
        ] = self._aggregate_ar_payments_record(eligible_records)
        payment_summary_to_create_for_ar_payments = list(
            ar_payments_aggregation_map.values()
        )
        self.payment_summary_repository.insert_many(
            payment_summary_to_create_for_ar_payments
        )

    def _aggregate_ar_payments_record(self, payments_to_aggregate):
        payments_to_aggregate = [
            payment for payment in payments_to_aggregate if payment.channel == AR_MODULE
        ]
        aggregation_map: Dict[
            PGPaymentEntity.PaymentGroupKey, PGPaymentSummaryEntity
        ] = dict()
        payment_entity_to_update = []
        payment_summary_entities_to_create = []
        for payment_data in payments_to_aggregate:
            if not payment_data.posting_date:
                parent_summary_entity = (
                    PaymentSummaryFactory.create_payment_summary_from_payment_entity(
                        payment_data.to_json()
                    )
                )
                payment_summary_entities_to_create.append(parent_summary_entity)
                payment_data.mark_as_aggregated(parent_summary_entity.uu_id)
            else:
                aggregation_key = payment_data.aggregation_group_key()
                if aggregation_key in aggregation_map:
                    aggregation_map[aggregation_key].aggregate(payment_data)
                else:
                    aggregation_map[
                        aggregation_key
                    ] = PaymentSummaryFactory.create_payment_summary_from_payment_entity(
                        payment_data.to_json(), is_ar_entry=True
                    )
                summary: PGPaymentSummaryEntity = aggregation_map[aggregation_key]
                payment_data.mark_as_aggregated(summary.uu_id)
            payment_entity_to_update.append(payment_data)
            if len(payment_entity_to_update) > 1000:
                self.payment_repository.bulk_update_records(payment_entity_to_update)
                payment_entity_to_update = []
            if len(payment_summary_entities_to_create) > 1000:
                self.payment_summary_repository.insert_many(
                    payment_summary_entities_to_create
                )
                payment_summary_entities_to_create = []
        if payment_entity_to_update:
            self.payment_repository.bulk_update_records(payment_entity_to_update)
        if payment_summary_entities_to_create:
            self.payment_summary_repository.insert_many(
                payment_summary_entities_to_create
            )
        return aggregation_map

    def _create_payment_summary_for_payments_other_than_ar(self, payments_to_aggregate):
        black_listed_hotels = (
            self.tenant_settings.get_business_central_blacklisted_hotels()
        )
        payments_to_aggregate = [
            payment for payment in payments_to_aggregate if payment.channel != AR_MODULE
        ]
        payment_entities_to_update = []
        payment_summary_entities_to_create = []
        for payment_data in payments_to_aggregate:
            if payment_data.hotel_code in black_listed_hotels:
                payment_data.mark_as_excluded_for_data_push(
                    f"This Hotel ({payment_data.hotel_code}) is excluded"
                )
                payment_entities_to_update.append(payment_data)
            else:
                parent_summary_entity = (
                    PaymentSummaryFactory.create_payment_summary_from_payment_entity(
                        payment_data.to_json()
                    )
                )
                payment_summary_entities_to_create.append(parent_summary_entity)
                payment_data.mark_as_aggregated(parent_summary_entity.uu_id)
                payment_entities_to_update.append(payment_data)
            if len(payment_entities_to_update) > 1000:
                self.payment_repository.bulk_update_records(payment_entities_to_update)
                self.payment_summary_repository.insert_many(
                    payment_summary_entities_to_create
                )
                payment_entities_to_update = []
                payment_summary_entities_to_create = []

        if payment_entities_to_update:
            self.payment_repository.bulk_update_records(payment_entities_to_update)
        if payment_summary_entities_to_create:
            self.payment_summary_repository.insert_many(
                payment_summary_entities_to_create
            )

    @staticmethod
    @session_manager(commit=True)
    def handle_async_execution_request(request, event_id=None):
        event_id = event_id if event_id else get_current_request_id()
        from finance_erp.async_job.job_scheduler_service import JobSchedulerService

        job = locate_instance(JobSchedulerService).create_data_aggregation_job(
            NavisionReports.PAYMENT_GATEWAY_SUMMARY_REPORT, request, event_id
        )
        return dict(job_id=job.job_id)

    def handle_request_from_job_executor(self, request_data, **kwargs):
        request_dto = DataGenerationBaseDto(**request_data)
        self.handle(request_dto)
