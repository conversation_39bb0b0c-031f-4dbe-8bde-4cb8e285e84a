from datetime import datetime

from core.common.utils.date import utc_to_ist
from finance_erp.application.payments.dtos.get_payement_report_dto import (
    GetPaymentReportDto,
)
from finance_erp.domain.payment.repository.payment_repository import PaymentRepository
from object_registry import register_instance


@register_instance(dependencies=[PaymentRepository])
class FetchPaymentsCommandHandler:
    def __init__(self, payment_reports_repository: PaymentRepository):
        self.payment_reports_repository = payment_reports_repository

    def handle(self, input_criteria: GetPaymentReportDto):
        if input_criteria.uu_ids:
            input_criteria.uu_ids = (
                input_criteria.uu_ids.split(",") if input_criteria.uu_ids else None
            )
            return self.payment_reports_repository.get_by_ids(input_criteria.uu_ids)
        else:
            if not input_criteria.from_date:
                input_criteria.from_date = utc_to_ist(datetime.utcnow()).date()
            if not input_criteria.to_date:
                input_criteria.to_date = utc_to_ist(datetime.utcnow()).date()
            return self.payment_reports_repository.get_reports_by_date_range(
                from_date=input_criteria.from_date, to_date=input_criteria.to_date
            )
