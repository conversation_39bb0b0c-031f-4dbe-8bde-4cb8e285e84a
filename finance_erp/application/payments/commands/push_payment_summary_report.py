from typing import List

from treebo_commons.utils import dateutils

from finance_erp.application.common.base_data_push_handler import (
    BaseDataPushCommandHandler,
)
from finance_erp.application.hotel_settings.constants import TenantConstants
from finance_erp.application.hotel_settings.tenant_settings import TenantSettings
from finance_erp.application.payments.dtos.bulk_payment_push_request_dto import (
    BulkPaymentPushRequestDto,
)
from finance_erp.common.constants import DataPushNavKeys
from finance_erp.common.decorators import session_manager
from finance_erp.common.schema.payment import PGTransactionPushRequestSchema
from finance_erp.domain.hotel.repository.hotel_repository import HotelReportRepository
from finance_erp.domain.payment.entity.payment import PGPaymentEntity
from finance_erp.domain.payment.entity.payment_summary_entity import (
    PGPaymentSummaryEntity,
)
from finance_erp.domain.payment.repository.payment_repository import PaymentRepository
from finance_erp.domain.payment.repository.payment_summary_repository import (
    PaymentSummaryRepository,
)
from finance_erp.domain.shared_kernel.audit.process_level.nav_push_audit_service import (
    NavPushAuditService,
)
from finance_erp.infrastructure.alerting.slack_alert_service_client import (
    SlackAlertServiceClient,
)
from finance_erp.infrastructure.external_clients.business_central_client import (
    BusinessCentralClient,
)
from finance_erp.infrastructure.external_clients.catalog_service_client import (
    CatalogServiceClient,
)
from object_registry import register_instance


@register_instance(
    dependencies=[
        BusinessCentralClient,
        SlackAlertServiceClient,
        PaymentSummaryRepository,
        PaymentRepository,
        NavPushAuditService,
        CatalogServiceClient,
        HotelReportRepository,
        TenantSettings,
    ]
)
class BulkPushPaymentReportCommandHandler(BaseDataPushCommandHandler):
    def __init__(
        self,
        external_data_push_client: BusinessCentralClient,
        slack_alert_client: SlackAlertServiceClient,
        payment_summary_repository: PaymentSummaryRepository,
        payment_repository: PaymentRepository,
        nav_push_audit_service: NavPushAuditService,
        catalog_service_client: CatalogServiceClient,
        hotel_reports_repository: HotelReportRepository,
        tenant_settings: TenantSettings,
    ):
        super().__init__(
            external_data_push_client,
            slack_alert_client,
            nav_push_audit_service,
            hotel_reports_repository,
            tenant_settings,
        )
        self.payment_summary_repository = payment_summary_repository
        self.payment_repository = payment_repository
        self.catalog_service_client = catalog_service_client
        self.hotel_reports_repository = hotel_reports_repository
        self.tenant_settings = tenant_settings

    def _get_schema_and_json_key(self):
        return PGTransactionPushRequestSchema, DataPushNavKeys.PAYMENTS

    def _get_records_to_push(self, request_dto: BulkPaymentPushRequestDto):
        if request_dto.uu_ids:
            return self.payment_summary_repository.get_by_ids(
                request_dto.uu_ids, for_update=True
            )
        prev_day_date = dateutils.date_to_ymd_str(
            dateutils.subtract(dateutils.current_datetime(), days=1)
        )
        channels = (
            self.tenant_settings.get_channels_not_applicable_for_payment_push_to_bizc()
        )
        eligible_gateway_payments = (
            self.payment_summary_repository.get_eligible_records_to_push(
                prev_day_date, channels
            )
        )
        payments_to_push = [payment for payment in eligible_gateway_payments]
        return payments_to_push

    @session_manager(commit=True)
    def _capture_failure(self, record: PGPaymentSummaryEntity, reason):
        record.capture_data_push_failure(reason)
        self._capture_data_push_failure_on_granular_payment_records(record, reason)
        self.payment_summary_repository.update_record(record)

    def _capture_data_push_failure_on_granular_payment_records(
        self, record: PGPaymentSummaryEntity, reason
    ):
        granular_payment_records: List[
            PGPaymentEntity
        ] = self.payment_repository.get_by_aggregation_ids([record.uu_id])
        for payment_record in granular_payment_records:
            payment_record.capture_data_push_failure(reason)
        self.payment_repository.bulk_update_records(granular_payment_records)

    @session_manager(commit=True)
    def _capture_success(self, record: PGPaymentSummaryEntity):
        record.capture_data_push_success()
        self._capture_data_push_success_on_granular_payment_records(record)
        self.payment_summary_repository.update_record(record)

    def _capture_data_push_success_on_granular_payment_records(
        self, record: PGPaymentSummaryEntity
    ):
        granular_payment_records: List[
            PGPaymentEntity
        ] = self.payment_repository.get_by_aggregation_ids([record.uu_id])
        for payment_record in granular_payment_records:
            payment_record.capture_data_push_success()
        self.payment_repository.bulk_update_records(granular_payment_records)

    def _get_request_parser(self):
        return BulkPaymentPushRequestDto

    def _update_records(self, records):
        self.payment_summary_repository.bulk_update_records(records)
