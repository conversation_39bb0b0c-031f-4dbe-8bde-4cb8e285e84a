from finance_erp.application.payments.commands.bulk_update_payment_record import (
    BulkUpdatePaymentReportCommandHandler,
)
from finance_erp.application.payments.commands.generate_payment_summary import (
    PaymentSummaryGenerationHandler,
)
from finance_erp.common.constants import AR_MODULE, NavisionReports
from finance_erp.common.decorators import session_manager
from finance_erp.common.exception import (
    ARServiceException,
    CRSServiceException,
    DataAlreadyPushedException,
    PaymentAggregationException,
    PaymentDataUpdateException,
)
from finance_erp.common.schema.payment import PGTransactionPushSchema
from finance_erp.domain.payment.models import PGPaymentStatus
from finance_erp.domain.payment.repository.payment_repository import PaymentRepository
from finance_erp.domain.payment.repository.payment_summary_repository import (
    PaymentSummaryRepository,
)
from finance_erp.infrastructure.external_clients.ar_service_client import (
    ARServiceClient,
)
from finance_erp.infrastructure.external_clients.crs_service_client import (
    CrsServiceClient,
)
from object_registry import locate_instance, register_instance


@register_instance(
    dependencies=[PaymentSummaryRepository, PaymentRepository, CrsServiceClient]
)
class ResyncPaymentDataHandler:
    def __init__(
        self,
        payment_summary_repository: PaymentSummaryRepository,
        payment_repository: PaymentRepository,
        crs_service_client: CrsServiceClient,
    ):
        self.payment_summary_repository = payment_summary_repository
        self.payment_repository = payment_repository
        self.crs_service_client = crs_service_client

    @session_manager(commit=True)
    def handle(self, payment_summary_uu_ids):
        selected_records = self.payment_summary_repository.get_by_ids(
            payment_summary_uu_ids
        )
        self._validate_non_pushed_records(selected_records)
        payment_records = self._get_payment_records(selected_records)
        ar_payments, crs_payments = self._categorize_payment_records(payment_records)

        latest_ar_data = self._fetch_ar_data(ar_payments)
        latest_crs_data = self._fetch_crs_data(crs_payments)

        latest_payments_data = latest_ar_data + latest_crs_data
        try:
            updated_payments = self._update_latest_payments_data(latest_payments_data)
        except Exception as e:
            raise PaymentDataUpdateException(f"Payment data update failed: {str(e)}")
        try:
            self._aggregate_payment_data(updated_payments)
        except Exception as e:
            raise PaymentAggregationException(
                f"Payment data aggregation failed: {str(e)}"
            )

    @staticmethod
    def _validate_non_pushed_records(records):
        pushed_records = [
            record for record in records if record.status == PGPaymentStatus.PUSHED
        ]
        if pushed_records:
            raise DataAlreadyPushedException()
        return True

    def _get_payment_records(self, selected_records):
        aggregation_ids = [record.uu_id for record in selected_records]
        return self.payment_repository.get_by_aggregation_ids(aggregation_ids)

    @staticmethod
    def _categorize_payment_records(payment_records):
        ar_payments = [
            record for record in payment_records if record.channel == AR_MODULE
        ]
        crs_payments = [
            record for record in payment_records if record.channel != AR_MODULE
        ]
        return ar_payments, crs_payments

    @staticmethod
    def _fetch_ar_data(ar_payments):
        if not ar_payments:
            return []
        ar_service_client = locate_instance(ARServiceClient)
        resource_data = [
            dict(
                resource_id=payment.pg_transaction_id, resource_unique_id=payment.uu_id
            )
            for payment in ar_payments
        ]
        try:
            response_data = ar_service_client.pull_payment_data(
                resource_data, NavisionReports.AR_PAYMENT_REPORT
            )
            ar_payments_data = [
                PGTransactionPushSchema.model_validate(item) for item in response_data
            ]
            return ar_payments_data
        except Exception as e:
            raise ARServiceException(
                f"Exception Occurred while fetching payment data from AR: {str(e)}"
            )

    def _fetch_crs_data(self, crs_payments):
        if not crs_payments:
            return []
        resource_data = [
            dict(resource_id=payment.reference_number, resource_unique_id=payment.uu_id)
            for payment in crs_payments
        ]
        response_data = self.crs_service_client.fetch_finance_reports(
            resource_data,
            NavisionReports.PAYMENT_GATEWAY_REPORT,
        )
        crs_payments_data = [
            PGTransactionPushSchema.model_validate(item) for item in response_data
        ]
        return crs_payments_data

    @staticmethod
    def _update_latest_payments_data(latest_payments_data):
        if not latest_payments_data:
            return
        payment_data = [payment.model_dump() for payment in latest_payments_data]
        payment_update_handler: BulkUpdatePaymentReportCommandHandler = locate_instance(
            BulkUpdatePaymentReportCommandHandler
        )
        return payment_update_handler.handle(payment_data)

    @staticmethod
    def _aggregate_payment_data(latest_payments_data):
        if not latest_payments_data:
            return
        payment_summary_generation_handler: PaymentSummaryGenerationHandler = (
            locate_instance(PaymentSummaryGenerationHandler)
        )
        payment_summary_generation_handler.handle(
            payments_to_aggregate=latest_payments_data
        )
